"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Administrator_Desktop_DocumentTracker_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/stats/route.ts */ \"(rsc)/./src/app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Administrator_Desktop_DocumentTracker_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/stats/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/dashboard/stats/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth/options */ \"(rsc)/./src/lib/auth/options.ts\");\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n/* harmony import */ var _services_stats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/stats */ \"(rsc)/./src/services/stats/index.ts\");\n/* harmony import */ var _utils_cacheUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/cacheUtils */ \"(rsc)/./src/utils/cacheUtils.ts\");\n\n\n\n\n\n\n// Mark this route as dynamic to prevent static generation\nconst dynamic = \"force-dynamic\";\n// Cache duration in milliseconds (10 seconds - reduced for more frequent updates)\nconst CACHE_DURATION = 10 * 1000;\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const userId = session.user.id;\n        const now = Date.now();\n        // Check if force refresh is requested\n        const { searchParams } = new URL(request.url);\n        const forceRefresh = searchParams.get(\"forceRefresh\") === \"true\";\n        // Check cache first (unless force refresh is requested)\n        if (!forceRefresh) {\n            const cachedData = (0,_utils_cacheUtils__WEBPACK_IMPORTED_MODULE_5__.getUserStatsFromCache)(userId, now, CACHE_DURATION);\n            if (cachedData) {\n                console.log(`Using cached dashboard stats for user: ${userId}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(cachedData);\n            }\n        }\n        // Get fresh stats using DashboardStatsService\n        console.log(`Computing fresh dashboard stats for user: ${userId}`);\n        const dashboardData = await _services_stats__WEBPACK_IMPORTED_MODULE_4__.DashboardStatsService.getDashboardStats({\n            userId,\n            userRole: session.user.role,\n            userDivision: session.user.division\n        });\n        // Transform the data to match the expected frontend format\n        const response = {\n            stats: {\n                inbox: dashboardData.inboxDocuments,\n                pending: dashboardData.pendingDocuments,\n                processed: dashboardData.processedDocuments,\n                sent: dashboardData.sentDocuments,\n                forwarded: dashboardData.forwardedDocuments,\n                archived: dashboardData.archivedDocuments,\n                recent: dashboardData.recentDocuments.length\n            },\n            recentDocuments: dashboardData.recentDocuments\n        };\n        // Cache the result\n        (0,_utils_cacheUtils__WEBPACK_IMPORTED_MODULE_5__.setUserStatsInCache)(userId, response, now);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Error fetching dashboard stats:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Failed to fetch dashboard stats\",\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth/options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _utils_audit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/audit */ \"(rsc)/./src/utils/audit.ts\");\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n/* harmony import */ var _utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/serverTimestamp */ \"(rsc)/./src/utils/serverTimestamp.ts\");\n/* harmony import */ var _utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/sessionToken */ \"(rsc)/./src/utils/sessionToken.ts\");\n\n\n\n\n\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"credentials\",\n            name: \"Credentials\",\n            // Define the credentials that will be submitted from the login form\n            credentials: {\n                name: {\n                    label: \"Name\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials, req) {\n                // Add more detailed logging of received credentials\n                console.log(\"Received credentials:\", credentials);\n                if (!credentials?.name || !credentials?.password) {\n                    console.error(\"Missing credentials:\", {\n                        name: !!credentials?.name,\n                        password: !!credentials?.password\n                    });\n                    throw new Error(\"Name and password required\");\n                }\n                try {\n                    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                    console.log(\"Looking up user with name:\", credentials.name);\n                    // Make the search case-insensitive and escape special characters in the regex\n                    const escapedName = credentials.name.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n                        name: {\n                            $regex: new RegExp(`^${escapedName}$`, \"i\")\n                        }\n                    }).select(\"+password\");\n                    if (!user) {\n                        console.log(\"User not found with name:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"User found, checking password\");\n                    if (!user.password) {\n                        console.log(\"User has no password set\");\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    const isPasswordCorrect = await (0,bcrypt__WEBPACK_IMPORTED_MODULE_1__.compare)(credentials.password, user.password);\n                    if (!isPasswordCorrect) {\n                        console.log(\"Password incorrect for user:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"Authentication successful for user:\", credentials.name);\n                    const userId = user._id?.toString() || \"\";\n                    // Create a new session token\n                    const userAgent = req?.headers?.[\"user-agent\"];\n                    const ipAddress = req?.headers?.[\"x-forwarded-for\"] || req?.socket?.remoteAddress || \"unknown\";\n                    const sessionToken = await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__.createSession)(userId, userAgent, ipAddress);\n                    // Log successful login\n                    await (0,_utils_audit__WEBPACK_IMPORTED_MODULE_4__.logAuditEvent)({\n                        action: _types_audit__WEBPACK_IMPORTED_MODULE_5__.AuditLogAction.USER_LOGIN,\n                        performedBy: userId,\n                        targetId: userId,\n                        targetType: \"User\",\n                        details: {\n                            name: user.name,\n                            email: user.email,\n                            role: user.role,\n                            division: user.division,\n                            sessionToken: sessionToken\n                        }\n                    });\n                    return {\n                        id: userId,\n                        name: user.name,\n                        email: user.email,\n                        role: user.role,\n                        division: user.division,\n                        image: user.image,\n                        sessionToken: sessionToken\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw new Error(\"Authentication failed. Please try again.\");\n                }\n                // This code is unreachable due to the try/catch block above\n                // but we'll keep it as a fallback\n                console.error(\"Warning: Reached unreachable code in NextAuth authorize callback\");\n                return null;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async jwt ({ token, user, trigger }) {\n            console.log(\"JWT callback called with user:\", user);\n            console.log(\"Initial token:\", token);\n            if (user) {\n                // Add user data to token\n                token.id = user.id;\n                // Cast user to any to access custom properties\n                const customUser = user;\n                token.name = customUser.name; // Ensure name is included\n                token.role = customUser.role;\n                token.division = customUser.division;\n                // Add session token to JWT\n                if (customUser.sessionToken) {\n                    token.sessionToken = customUser.sessionToken;\n                }\n                // Add server timestamp to token to invalidate sessions on server restart\n                token.serverTimestamp = (0,_utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_6__.getServerTimestamp)();\n                console.log(\"Updated token with user data:\", token);\n            }\n            // Handle sign out\n            if (trigger === \"signOut\") {\n                // Remove the session from the database when the user signs out\n                if (token.id && token.sessionToken) {\n                    await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__.removeSession)(token.id, token.sessionToken);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"Session callback called with token:\", token);\n            console.log(\"Initial session:\", session);\n            if (token) {\n                // Add user data to session\n                session.user.id = token.id;\n                session.user.name = token.name; // Ensure name is included\n                session.user.role = token.role;\n                session.user.division = token.division;\n                // Add server timestamp to session\n                session.serverTimestamp = token.serverTimestamp;\n                // Add session token to session\n                if (token.sessionToken) {\n                    session.sessionToken = token.sessionToken;\n                }\n            }\n            console.log(\"Returning session:\", session);\n            return session;\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/mongodb.ts":
/*!*******************************!*\
  !*** ./src/lib/db/mongodb.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isConnectionHealthy: () => (/* binding */ isConnectionHealthy)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _register_models__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./register-models */ \"(rsc)/./src/lib/db/register-models.ts\");\n\n\nconst MONGODB_URI = process.env.MONGODB_URI || \"mongodb://localhost:27017/document-tracker\";\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable\");\n}\n// Connection options\nconst connectionOptions = {\n    bufferCommands: false,\n    serverSelectionTimeoutMS: 10000,\n    socketTimeoutMS: 45000,\n    family: 4,\n    // Enable auto reconnect\n    autoIndex: true,\n    autoCreate: true,\n    // Connection health monitoring\n    heartbeatFrequencyMS: 10000\n};\n// Initialize cached connection object\nlet cached = global.mongoose || {\n    conn: null,\n    promise: null,\n    isConnecting: false,\n    lastConnectionAttempt: 0\n};\nif (!global.mongoose) {\n    global.mongoose = cached;\n}\n// Set up connection event listeners\nfunction setupConnectionMonitoring() {\n    // Only set up listeners once\n    if (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.listenerCount(\"connected\") > 0) return;\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"connected\", ()=>{\n        console.log(\"MongoDB connection established successfully\");\n    });\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"error\", (err)=>{\n        console.error(\"MongoDB connection error:\", err);\n    });\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"disconnected\", ()=>{\n        console.warn(\"MongoDB disconnected. Will attempt to reconnect automatically.\");\n    });\n    // Handle process termination\n    process.on(\"SIGINT\", async ()=>{\n        try {\n            await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n            console.log(\"MongoDB connection closed due to application termination\");\n            process.exit(0);\n        } catch (err) {\n            console.error(\"Error closing MongoDB connection:\", err);\n            process.exit(1);\n        }\n    });\n}\n// Check if connection is healthy\nfunction isConnectionHealthy() {\n    return (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1; // 1 = connected\n}\n// Main connection function\nasync function dbConnect() {\n    // Set up connection monitoring\n    setupConnectionMonitoring();\n    // If we already have a connection and it's healthy, return it\n    if (cached.conn && isConnectionHealthy()) {\n        console.log(\"Using existing MongoDB connection\");\n        return cached.conn;\n    }\n    console.log(\"No healthy MongoDB connection found, creating a new one\");\n    // If we're already trying to connect, wait for that promise\n    if (cached.isConnecting && cached.promise) {\n        try {\n            cached.conn = await cached.promise;\n            return cached.conn;\n        } catch (error) {\n            // If the current connection attempt fails, we'll try again below\n            console.error(\"Ongoing connection attempt failed:\", error);\n        }\n    }\n    // Prevent connection attempts in rapid succession (throttle to once per 5 seconds)\n    const now = Date.now();\n    const minTimeBetweenAttempts = 5000; // 5 seconds\n    if (now - cached.lastConnectionAttempt < minTimeBetweenAttempts) {\n        console.warn(\"Connection attempt throttled. Waiting before retrying...\");\n        await new Promise((resolve)=>setTimeout(resolve, minTimeBetweenAttempts));\n    }\n    // Start a new connection attempt\n    cached.isConnecting = true;\n    cached.lastConnectionAttempt = Date.now();\n    cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, connectionOptions).then((mongoose)=>{\n        console.log(\"MongoDB connected successfully\");\n        cached.isConnecting = false;\n        // Register models after successful connection\n        (0,_register_models__WEBPACK_IMPORTED_MODULE_1__.registerModels)();\n        return mongoose;\n    }).catch((error)=>{\n        console.error(\"MongoDB connection error:\", error);\n        cached.isConnecting = false;\n        cached.promise = null; // Reset the promise on error\n        throw error;\n    });\n    try {\n        cached.conn = await cached.promise;\n        return cached.conn;\n    } catch (error) {\n        console.error(\"Failed to connect to MongoDB:\", error);\n        // Implement exponential backoff for retries in production\n        if (false) {}\n        throw error;\n    }\n}\n// Export the connection function and health check\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL21vbmdvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFDbUI7QUFFbkQsTUFBTUUsY0FBY0MsUUFBUUMsR0FBRyxDQUFDRixXQUFXLElBQUk7QUFFL0MsSUFBSSxDQUFDQSxhQUFhO0lBQ2hCLE1BQU0sSUFBSUcsTUFBTTtBQUNsQjtBQW9CQSxxQkFBcUI7QUFDckIsTUFBTUMsb0JBQW9CO0lBQ3hCQyxnQkFBZ0I7SUFDaEJDLDBCQUEwQjtJQUMxQkMsaUJBQWlCO0lBQ2pCQyxRQUFRO0lBQ1Isd0JBQXdCO0lBQ3hCQyxXQUFXO0lBQ1hDLFlBQVk7SUFDWiwrQkFBK0I7SUFDL0JDLHNCQUFzQjtBQUN4QjtBQUVBLHNDQUFzQztBQUN0QyxJQUFJQyxTQUF3QkMsT0FBT2YsUUFBUSxJQUFJO0lBQzdDZ0IsTUFBTTtJQUNOQyxTQUFTO0lBQ1RDLGNBQWM7SUFDZEMsdUJBQXVCO0FBQ3pCO0FBRUEsSUFBSSxDQUFDSixPQUFPZixRQUFRLEVBQUU7SUFDcEJlLE9BQU9mLFFBQVEsR0FBR2M7QUFDcEI7QUFFQSxvQ0FBb0M7QUFDcEMsU0FBU007SUFDUCw2QkFBNkI7SUFDN0IsSUFBSXBCLDBEQUFtQixDQUFDc0IsYUFBYSxDQUFDLGVBQWUsR0FBRztJQUV4RHRCLDBEQUFtQixDQUFDdUIsRUFBRSxDQUFDLGFBQWE7UUFDbENDLFFBQVFDLEdBQUcsQ0FBQztJQUNkO0lBRUF6QiwwREFBbUIsQ0FBQ3VCLEVBQUUsQ0FBQyxTQUFTLENBQUNHO1FBQy9CRixRQUFRRyxLQUFLLENBQUMsNkJBQTZCRDtJQUM3QztJQUVBMUIsMERBQW1CLENBQUN1QixFQUFFLENBQUMsZ0JBQWdCO1FBQ3JDQyxRQUFRSSxJQUFJLENBQUM7SUFDZjtJQUVBLDZCQUE2QjtJQUM3QnpCLFFBQVFvQixFQUFFLENBQUMsVUFBVTtRQUNuQixJQUFJO1lBQ0YsTUFBTXZCLDBEQUFtQixDQUFDNkIsS0FBSztZQUMvQkwsUUFBUUMsR0FBRyxDQUFDO1lBQ1p0QixRQUFRMkIsSUFBSSxDQUFDO1FBQ2YsRUFBRSxPQUFPSixLQUFLO1lBQ1pGLFFBQVFHLEtBQUssQ0FBQyxxQ0FBcUNEO1lBQ25EdkIsUUFBUTJCLElBQUksQ0FBQztRQUNmO0lBQ0Y7QUFDRjtBQUVBLGlDQUFpQztBQUNqQyxTQUFTQztJQUNQLE9BQU8vQiw0REFBbUIsQ0FBQ2dDLFVBQVUsS0FBSyxHQUFHLGdCQUFnQjtBQUMvRDtBQUVBLDJCQUEyQjtBQUMzQixlQUFlQztJQUNiLCtCQUErQjtJQUMvQmI7SUFFQSw4REFBOEQ7SUFDOUQsSUFBSU4sT0FBT0UsSUFBSSxJQUFJZSx1QkFBdUI7UUFDeENQLFFBQVFDLEdBQUcsQ0FBQztRQUNaLE9BQU9YLE9BQU9FLElBQUk7SUFDcEI7SUFFQVEsUUFBUUMsR0FBRyxDQUFDO0lBRVosNERBQTREO0lBQzVELElBQUlYLE9BQU9JLFlBQVksSUFBSUosT0FBT0csT0FBTyxFQUFFO1FBQ3pDLElBQUk7WUFDRkgsT0FBT0UsSUFBSSxHQUFHLE1BQU1GLE9BQU9HLE9BQU87WUFDbEMsT0FBT0gsT0FBT0UsSUFBSTtRQUNwQixFQUFFLE9BQU9XLE9BQU87WUFDZCxpRUFBaUU7WUFDakVILFFBQVFHLEtBQUssQ0FBQyxzQ0FBc0NBO1FBQ3REO0lBQ0Y7SUFFQSxtRkFBbUY7SUFDbkYsTUFBTU8sTUFBTUMsS0FBS0QsR0FBRztJQUNwQixNQUFNRSx5QkFBeUIsTUFBTSxZQUFZO0lBQ2pELElBQUlGLE1BQU1wQixPQUFPSyxxQkFBcUIsR0FBR2lCLHdCQUF3QjtRQUMvRFosUUFBUUksSUFBSSxDQUFDO1FBQ2IsTUFBTSxJQUFJUyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTRjtJQUNuRDtJQUVBLGlDQUFpQztJQUNqQ3RCLE9BQU9JLFlBQVksR0FBRztJQUN0QkosT0FBT0sscUJBQXFCLEdBQUdnQixLQUFLRCxHQUFHO0lBQ3ZDcEIsT0FBT0csT0FBTyxHQUFHakIsdURBQWdCLENBQUNFLGFBQWFJLG1CQUM1Q21DLElBQUksQ0FBQyxDQUFDekM7UUFDTHdCLFFBQVFDLEdBQUcsQ0FBQztRQUNaWCxPQUFPSSxZQUFZLEdBQUc7UUFDdEIsOENBQThDO1FBQzlDakIsZ0VBQWNBO1FBQ2QsT0FBT0Q7SUFDVCxHQUNDMEMsS0FBSyxDQUFDLENBQUNmO1FBQ05ILFFBQVFHLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDYixPQUFPSSxZQUFZLEdBQUc7UUFDdEJKLE9BQU9HLE9BQU8sR0FBRyxNQUFNLDZCQUE2QjtRQUNwRCxNQUFNVTtJQUNSO0lBRUYsSUFBSTtRQUNGYixPQUFPRSxJQUFJLEdBQUcsTUFBTUYsT0FBT0csT0FBTztRQUNsQyxPQUFPSCxPQUFPRSxJQUFJO0lBQ3BCLEVBQUUsT0FBT1csT0FBTztRQUNkSCxRQUFRRyxLQUFLLENBQUMsaUNBQWlDQTtRQUMvQywwREFBMEQ7UUFDMUQsSUFBSXhCLEtBQXlCLEVBQWMsRUFFMUM7UUFDRCxNQUFNd0I7SUFDUjtBQUNGO0FBRUEsa0RBQWtEO0FBQ2xELGlFQUFlTSxTQUFTQSxFQUFDO0FBQ00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kb2N1bWVudC10cmFja2VyLy4vc3JjL2xpYi9kYi9tb25nb2RiLnRzPzMwYTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gJ21vbmdvb3NlJztcbmltcG9ydCB7IHJlZ2lzdGVyTW9kZWxzIH0gZnJvbSAnLi9yZWdpc3Rlci1tb2RlbHMnO1xuXG5jb25zdCBNT05HT0RCX1VSSSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJIHx8ICdtb25nb2RiOi8vbG9jYWxob3N0OjI3MDE3L2RvY3VtZW50LXRyYWNrZXInO1xuXG5pZiAoIU1PTkdPREJfVVJJKSB7XG4gIHRocm93IG5ldyBFcnJvcignUGxlYXNlIGRlZmluZSB0aGUgTU9OR09EQl9VUkkgZW52aXJvbm1lbnQgdmFyaWFibGUnKTtcbn1cblxuLyoqXG4gKiBHbG9iYWwgaXMgdXNlZCBoZXJlIHRvIG1haW50YWluIGEgY2FjaGVkIGNvbm5lY3Rpb24gYWNyb3NzIGhvdCByZWxvYWRzXG4gKiBpbiBkZXZlbG9wbWVudC4gVGhpcyBwcmV2ZW50cyBjb25uZWN0aW9ucyBncm93aW5nIGV4cG9uZW50aWFsbHlcbiAqIGR1cmluZyBBUEkgUm91dGUgdXNhZ2UuXG4gKi9cbi8vIERlZmluZSB0aGUgdHlwZSBmb3Igb3VyIGNhY2hlZCBtb25nb29zZSBjb25uZWN0aW9uXG5pbnRlcmZhY2UgTW9uZ29vc2VDYWNoZSB7XG4gIGNvbm46IHR5cGVvZiBtb25nb29zZSB8IG51bGw7XG4gIHByb21pc2U6IFByb21pc2U8dHlwZW9mIG1vbmdvb3NlPiB8IG51bGw7XG4gIGlzQ29ubmVjdGluZzogYm9vbGVhbjtcbiAgbGFzdENvbm5lY3Rpb25BdHRlbXB0OiBudW1iZXI7XG59XG5cbi8vIERlY2xhcmUgZ2xvYmFsIHZhcmlhYmxlIHdpdGggbW9uZ29vc2UgcHJvcGVydHlcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgdmFyIG1vbmdvb3NlOiBNb25nb29zZUNhY2hlIHwgdW5kZWZpbmVkO1xufVxuXG4vLyBDb25uZWN0aW9uIG9wdGlvbnNcbmNvbnN0IGNvbm5lY3Rpb25PcHRpb25zID0ge1xuICBidWZmZXJDb21tYW5kczogZmFsc2UsXG4gIHNlcnZlclNlbGVjdGlvblRpbWVvdXRNUzogMTAwMDAsIC8vIEluY3JlYXNlZCB0aW1lb3V0IHRvIDEwIHNlY29uZHNcbiAgc29ja2V0VGltZW91dE1TOiA0NTAwMCwgLy8gQ2xvc2Ugc29ja2V0cyBhZnRlciA0NSBzZWNvbmRzIG9mIGluYWN0aXZpdHlcbiAgZmFtaWx5OiA0LCAvLyBVc2UgSVB2NCwgc2tpcCB0cnlpbmcgSVB2NlxuICAvLyBFbmFibGUgYXV0byByZWNvbm5lY3RcbiAgYXV0b0luZGV4OiB0cnVlLFxuICBhdXRvQ3JlYXRlOiB0cnVlLFxuICAvLyBDb25uZWN0aW9uIGhlYWx0aCBtb25pdG9yaW5nXG4gIGhlYXJ0YmVhdEZyZXF1ZW5jeU1TOiAxMDAwMCwgLy8gQ2hlY2sgY29ubmVjdGlvbiBldmVyeSAxMCBzZWNvbmRzXG59O1xuXG4vLyBJbml0aWFsaXplIGNhY2hlZCBjb25uZWN0aW9uIG9iamVjdFxubGV0IGNhY2hlZDogTW9uZ29vc2VDYWNoZSA9IGdsb2JhbC5tb25nb29zZSB8fCB7XG4gIGNvbm46IG51bGwsXG4gIHByb21pc2U6IG51bGwsXG4gIGlzQ29ubmVjdGluZzogZmFsc2UsXG4gIGxhc3RDb25uZWN0aW9uQXR0ZW1wdDogMFxufTtcblxuaWYgKCFnbG9iYWwubW9uZ29vc2UpIHtcbiAgZ2xvYmFsLm1vbmdvb3NlID0gY2FjaGVkO1xufVxuXG4vLyBTZXQgdXAgY29ubmVjdGlvbiBldmVudCBsaXN0ZW5lcnNcbmZ1bmN0aW9uIHNldHVwQ29ubmVjdGlvbk1vbml0b3JpbmcoKSB7XG4gIC8vIE9ubHkgc2V0IHVwIGxpc3RlbmVycyBvbmNlXG4gIGlmIChtb25nb29zZS5jb25uZWN0aW9uLmxpc3RlbmVyQ291bnQoJ2Nvbm5lY3RlZCcpID4gMCkgcmV0dXJuO1xuXG4gIG1vbmdvb3NlLmNvbm5lY3Rpb24ub24oJ2Nvbm5lY3RlZCcsICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnTW9uZ29EQiBjb25uZWN0aW9uIGVzdGFibGlzaGVkIHN1Y2Nlc3NmdWxseScpO1xuICB9KTtcblxuICBtb25nb29zZS5jb25uZWN0aW9uLm9uKCdlcnJvcicsIChlcnIpID0+IHtcbiAgICBjb25zb2xlLmVycm9yKCdNb25nb0RCIGNvbm5lY3Rpb24gZXJyb3I6JywgZXJyKTtcbiAgfSk7XG5cbiAgbW9uZ29vc2UuY29ubmVjdGlvbi5vbignZGlzY29ubmVjdGVkJywgKCkgPT4ge1xuICAgIGNvbnNvbGUud2FybignTW9uZ29EQiBkaXNjb25uZWN0ZWQuIFdpbGwgYXR0ZW1wdCB0byByZWNvbm5lY3QgYXV0b21hdGljYWxseS4nKTtcbiAgfSk7XG5cbiAgLy8gSGFuZGxlIHByb2Nlc3MgdGVybWluYXRpb25cbiAgcHJvY2Vzcy5vbignU0lHSU5UJywgYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBtb25nb29zZS5jb25uZWN0aW9uLmNsb3NlKCk7XG4gICAgICBjb25zb2xlLmxvZygnTW9uZ29EQiBjb25uZWN0aW9uIGNsb3NlZCBkdWUgdG8gYXBwbGljYXRpb24gdGVybWluYXRpb24nKTtcbiAgICAgIHByb2Nlc3MuZXhpdCgwKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNsb3NpbmcgTW9uZ29EQiBjb25uZWN0aW9uOicsIGVycik7XG4gICAgICBwcm9jZXNzLmV4aXQoMSk7XG4gICAgfVxuICB9KTtcbn1cblxuLy8gQ2hlY2sgaWYgY29ubmVjdGlvbiBpcyBoZWFsdGh5XG5mdW5jdGlvbiBpc0Nvbm5lY3Rpb25IZWFsdGh5KCk6IGJvb2xlYW4ge1xuICByZXR1cm4gbW9uZ29vc2UuY29ubmVjdGlvbi5yZWFkeVN0YXRlID09PSAxOyAvLyAxID0gY29ubmVjdGVkXG59XG5cbi8vIE1haW4gY29ubmVjdGlvbiBmdW5jdGlvblxuYXN5bmMgZnVuY3Rpb24gZGJDb25uZWN0KCkge1xuICAvLyBTZXQgdXAgY29ubmVjdGlvbiBtb25pdG9yaW5nXG4gIHNldHVwQ29ubmVjdGlvbk1vbml0b3JpbmcoKTtcblxuICAvLyBJZiB3ZSBhbHJlYWR5IGhhdmUgYSBjb25uZWN0aW9uIGFuZCBpdCdzIGhlYWx0aHksIHJldHVybiBpdFxuICBpZiAoY2FjaGVkLmNvbm4gJiYgaXNDb25uZWN0aW9uSGVhbHRoeSgpKSB7XG4gICAgY29uc29sZS5sb2coJ1VzaW5nIGV4aXN0aW5nIE1vbmdvREIgY29ubmVjdGlvbicpO1xuICAgIHJldHVybiBjYWNoZWQuY29ubjtcbiAgfVxuXG4gIGNvbnNvbGUubG9nKCdObyBoZWFsdGh5IE1vbmdvREIgY29ubmVjdGlvbiBmb3VuZCwgY3JlYXRpbmcgYSBuZXcgb25lJyk7XG5cbiAgLy8gSWYgd2UncmUgYWxyZWFkeSB0cnlpbmcgdG8gY29ubmVjdCwgd2FpdCBmb3IgdGhhdCBwcm9taXNlXG4gIGlmIChjYWNoZWQuaXNDb25uZWN0aW5nICYmIGNhY2hlZC5wcm9taXNlKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNhY2hlZC5jb25uID0gYXdhaXQgY2FjaGVkLnByb21pc2U7XG4gICAgICByZXR1cm4gY2FjaGVkLmNvbm47XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIElmIHRoZSBjdXJyZW50IGNvbm5lY3Rpb24gYXR0ZW1wdCBmYWlscywgd2UnbGwgdHJ5IGFnYWluIGJlbG93XG4gICAgICBjb25zb2xlLmVycm9yKCdPbmdvaW5nIGNvbm5lY3Rpb24gYXR0ZW1wdCBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIC8vIFByZXZlbnQgY29ubmVjdGlvbiBhdHRlbXB0cyBpbiByYXBpZCBzdWNjZXNzaW9uICh0aHJvdHRsZSB0byBvbmNlIHBlciA1IHNlY29uZHMpXG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gIGNvbnN0IG1pblRpbWVCZXR3ZWVuQXR0ZW1wdHMgPSA1MDAwOyAvLyA1IHNlY29uZHNcbiAgaWYgKG5vdyAtIGNhY2hlZC5sYXN0Q29ubmVjdGlvbkF0dGVtcHQgPCBtaW5UaW1lQmV0d2VlbkF0dGVtcHRzKSB7XG4gICAgY29uc29sZS53YXJuKCdDb25uZWN0aW9uIGF0dGVtcHQgdGhyb3R0bGVkLiBXYWl0aW5nIGJlZm9yZSByZXRyeWluZy4uLicpO1xuICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCBtaW5UaW1lQmV0d2VlbkF0dGVtcHRzKSk7XG4gIH1cblxuICAvLyBTdGFydCBhIG5ldyBjb25uZWN0aW9uIGF0dGVtcHRcbiAgY2FjaGVkLmlzQ29ubmVjdGluZyA9IHRydWU7XG4gIGNhY2hlZC5sYXN0Q29ubmVjdGlvbkF0dGVtcHQgPSBEYXRlLm5vdygpO1xuICBjYWNoZWQucHJvbWlzZSA9IG1vbmdvb3NlLmNvbm5lY3QoTU9OR09EQl9VUkksIGNvbm5lY3Rpb25PcHRpb25zKVxuICAgIC50aGVuKChtb25nb29zZSkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ01vbmdvREIgY29ubmVjdGVkIHN1Y2Nlc3NmdWxseScpO1xuICAgICAgY2FjaGVkLmlzQ29ubmVjdGluZyA9IGZhbHNlO1xuICAgICAgLy8gUmVnaXN0ZXIgbW9kZWxzIGFmdGVyIHN1Y2Nlc3NmdWwgY29ubmVjdGlvblxuICAgICAgcmVnaXN0ZXJNb2RlbHMoKTtcbiAgICAgIHJldHVybiBtb25nb29zZTtcbiAgICB9KVxuICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ01vbmdvREIgY29ubmVjdGlvbiBlcnJvcjonLCBlcnJvcik7XG4gICAgICBjYWNoZWQuaXNDb25uZWN0aW5nID0gZmFsc2U7XG4gICAgICBjYWNoZWQucHJvbWlzZSA9IG51bGw7IC8vIFJlc2V0IHRoZSBwcm9taXNlIG9uIGVycm9yXG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9KTtcblxuICB0cnkge1xuICAgIGNhY2hlZC5jb25uID0gYXdhaXQgY2FjaGVkLnByb21pc2U7XG4gICAgcmV0dXJuIGNhY2hlZC5jb25uO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjb25uZWN0IHRvIE1vbmdvREI6JywgZXJyb3IpO1xuICAgIC8vIEltcGxlbWVudCBleHBvbmVudGlhbCBiYWNrb2ZmIGZvciByZXRyaWVzIGluIHByb2R1Y3Rpb25cbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICAgICAgY29uc29sZS5sb2coJ1dpbGwgcmV0cnkgY29ubmVjdGlvbiBhdXRvbWF0aWNhbGx5Li4uJyk7XG4gICAgfVxuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8vIEV4cG9ydCB0aGUgY29ubmVjdGlvbiBmdW5jdGlvbiBhbmQgaGVhbHRoIGNoZWNrXG5leHBvcnQgZGVmYXVsdCBkYkNvbm5lY3Q7XG5leHBvcnQgeyBpc0Nvbm5lY3Rpb25IZWFsdGh5IH07XG4iXSwibmFtZXMiOlsibW9uZ29vc2UiLCJyZWdpc3Rlck1vZGVscyIsIk1PTkdPREJfVVJJIiwicHJvY2VzcyIsImVudiIsIkVycm9yIiwiY29ubmVjdGlvbk9wdGlvbnMiLCJidWZmZXJDb21tYW5kcyIsInNlcnZlclNlbGVjdGlvblRpbWVvdXRNUyIsInNvY2tldFRpbWVvdXRNUyIsImZhbWlseSIsImF1dG9JbmRleCIsImF1dG9DcmVhdGUiLCJoZWFydGJlYXRGcmVxdWVuY3lNUyIsImNhY2hlZCIsImdsb2JhbCIsImNvbm4iLCJwcm9taXNlIiwiaXNDb25uZWN0aW5nIiwibGFzdENvbm5lY3Rpb25BdHRlbXB0Iiwic2V0dXBDb25uZWN0aW9uTW9uaXRvcmluZyIsImNvbm5lY3Rpb24iLCJsaXN0ZW5lckNvdW50Iiwib24iLCJjb25zb2xlIiwibG9nIiwiZXJyIiwiZXJyb3IiLCJ3YXJuIiwiY2xvc2UiLCJleGl0IiwiaXNDb25uZWN0aW9uSGVhbHRoeSIsInJlYWR5U3RhdGUiLCJkYkNvbm5lY3QiLCJub3ciLCJEYXRlIiwibWluVGltZUJldHdlZW5BdHRlbXB0cyIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImNvbm5lY3QiLCJ0aGVuIiwiY2F0Y2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/register-models.ts":
/*!***************************************!*\
  !*** ./src/lib/db/register-models.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerModels: () => (/* binding */ registerModels)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\n// Define the Document Journey Schema\nconst DocumentJourneySchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    action: {\n        type: String,\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    fromDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    toDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    byUser: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    timestamp: {\n        type: Date,\n        default: Date.now\n    },\n    notes: {\n        type: String\n    }\n});\n// Define the Document Schema\nconst DocumentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a title\"\n        ],\n        maxlength: [\n            100,\n            \"Title cannot be more than 100 characters\"\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a description\"\n        ]\n    },\n    category: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentCategory),\n        required: [\n            true,\n            \"Please provide a category\"\n        ]\n    },\n    status: {\n        type: String,\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX,\n        // Use a custom validator instead of enum to ensure it works properly with case-insensitivity\n        validate: {\n            validator: function(v) {\n                // Convert both to lowercase for case-insensitive comparison\n                return Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).map((s)=>s.toLowerCase()).includes(v.toLowerCase());\n            },\n            message: (props)=>`${props.value} is not a valid status. Valid statuses are: ${Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).join(\", \")}`\n        }\n    },\n    createdBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    currentLocation: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a current location\"\n        ]\n    },\n    recipientId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\"\n    },\n    relatedDocumentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Document\"\n    },\n    fileUrl: {\n        type: String\n    },\n    fileName: {\n        type: String\n    },\n    fileType: {\n        type: String\n    },\n    trackingNumber: {\n        type: String,\n        // Using sparse index to prevent duplicate key errors with null/undefined values\n        index: {\n            sparse: true\n        },\n        validate: {\n            validator: function(v) {\n                // Allow undefined or null values (will be set later)\n                if (!v) return true;\n                // Validate the format: MGBR2-YYYY-NNNN-NNNN\n                return typeof v === \"string\" && /^MGBR2-\\d{4}-\\d{4}-\\d{4}$/.test(v);\n            },\n            message: (props)=>`${props.value} is not a valid tracking number format. Expected format: MGBR2-YYYY-NNNN-NNNN`\n        }\n    },\n    isOriginal: {\n        type: Boolean,\n        default: false,\n        index: true\n    },\n    journey: [\n        DocumentJourneySchema\n    ]\n}, {\n    timestamps: true\n});\n// Register models\nfunction registerModels() {\n    // Only register models if they haven't been registered yet\n    if (!(mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Document) {\n        mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Document\", DocumentSchema);\n        console.log(\"Document model registered\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3JlZ2lzdGVyLW1vZGVscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdDO0FBQ3FDO0FBRXJFLHFDQUFxQztBQUNyQyxNQUFNSSx3QkFBd0IsSUFBSUosd0RBQWUsQ0FBQztJQUNoRE0sUUFBUTtRQUNOQyxNQUFNQztRQUNOQyxVQUFVO1lBQUM7WUFBTTtTQUEyQjtJQUM5QztJQUNBQyxjQUFjO1FBQ1pILE1BQU1DO1FBQ05HLE1BQU1DLE9BQU9DLE1BQU0sQ0FBQ1YsNENBQVFBO0lBQzlCO0lBQ0FXLFlBQVk7UUFDVlAsTUFBTUM7UUFDTkcsTUFBTUMsT0FBT0MsTUFBTSxDQUFDViw0Q0FBUUE7SUFDOUI7SUFDQVksUUFBUTtRQUNOUixNQUFNUCx3REFBZSxDQUFDZ0IsS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xULFVBQVU7WUFBQztZQUFNO1NBQXdCO0lBQzNDO0lBQ0FVLFdBQVc7UUFDVFosTUFBTWE7UUFDTkMsU0FBU0QsS0FBS0UsR0FBRztJQUNuQjtJQUNBQyxPQUFPO1FBQ0xoQixNQUFNQztJQUNSO0FBQ0Y7QUFFQSw2QkFBNkI7QUFDN0IsTUFBTWdCLGlCQUFpQixJQUFJeEIsd0RBQWUsQ0FDeEM7SUFDRXlCLE9BQU87UUFDTGxCLE1BQU1DO1FBQ05DLFVBQVU7WUFBQztZQUFNO1NBQXlCO1FBQzFDaUIsV0FBVztZQUFDO1lBQUs7U0FBMkM7SUFDOUQ7SUFDQUMsYUFBYTtRQUNYcEIsTUFBTUM7UUFDTkMsVUFBVTtZQUFDO1lBQU07U0FBK0I7SUFDbEQ7SUFDQW1CLFVBQVU7UUFDUnJCLE1BQU1DO1FBQ05HLE1BQU1DLE9BQU9DLE1BQU0sQ0FBQ1gsb0RBQWdCQTtRQUNwQ08sVUFBVTtZQUFDO1lBQU07U0FBNEI7SUFDL0M7SUFDQW9CLFFBQVE7UUFDTnRCLE1BQU1DO1FBQ05hLFNBQVNwQixrREFBY0EsQ0FBQzZCLEtBQUs7UUFDN0IsNkZBQTZGO1FBQzdGQyxVQUFVO1lBQ1JDLFdBQVcsU0FBU0MsQ0FBUztnQkFDM0IsNERBQTREO2dCQUM1RCxPQUFPckIsT0FBT0MsTUFBTSxDQUFDWixrREFBY0EsRUFBRWlDLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsV0FBVyxJQUFJQyxRQUFRLENBQUNKLEVBQUVHLFdBQVc7WUFDdkY7WUFDQUUsU0FBUyxDQUFDQyxRQUFlLENBQUMsRUFBRUEsTUFBTUMsS0FBSyxDQUFDLDRDQUE0QyxFQUFFNUIsT0FBT0MsTUFBTSxDQUFDWixrREFBY0EsRUFBRXdDLElBQUksQ0FBQyxNQUFNLENBQUM7UUFDbEk7SUFDRjtJQUNBQyxXQUFXO1FBQ1RuQyxNQUFNUCx3REFBZSxDQUFDZ0IsS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xULFVBQVU7WUFBQztZQUFNO1NBQXdCO0lBQzNDO0lBQ0FrQyxpQkFBaUI7UUFDZnBDLE1BQU1DO1FBQ05HLE1BQU1DLE9BQU9DLE1BQU0sQ0FBQ1YsNENBQVFBO1FBQzVCTSxVQUFVO1lBQUM7WUFBTTtTQUFvQztJQUN2RDtJQUNBbUMsYUFBYTtRQUNYckMsTUFBTVAsd0RBQWUsQ0FBQ2dCLEtBQUssQ0FBQ0MsUUFBUTtRQUNwQ0MsS0FBSztJQUNQO0lBQ0EyQixtQkFBbUI7UUFDakJ0QyxNQUFNUCx3REFBZSxDQUFDZ0IsS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO0lBQ1A7SUFDQTRCLFNBQVM7UUFDUHZDLE1BQU1DO0lBQ1I7SUFDQXVDLFVBQVU7UUFDUnhDLE1BQU1DO0lBQ1I7SUFDQXdDLFVBQVU7UUFDUnpDLE1BQU1DO0lBQ1I7SUFDQXlDLGdCQUFnQjtRQUNkMUMsTUFBTUM7UUFDTixnRkFBZ0Y7UUFDaEYwQyxPQUFPO1lBQUVDLFFBQVE7UUFBSztRQUN0QnBCLFVBQVU7WUFDUkMsV0FBVyxTQUFTQyxDQUE0QjtnQkFDOUMscURBQXFEO2dCQUNyRCxJQUFJLENBQUNBLEdBQUcsT0FBTztnQkFFZiw0Q0FBNEM7Z0JBQzVDLE9BQU8sT0FBT0EsTUFBTSxZQUFZLDRCQUE0Qm1CLElBQUksQ0FBQ25CO1lBQ25FO1lBQ0FLLFNBQVMsQ0FBQ0MsUUFBZSxDQUFDLEVBQUVBLE1BQU1DLEtBQUssQ0FBQyw2RUFBNkUsQ0FBQztRQUN4SDtJQUNGO0lBQ0FhLFlBQVk7UUFDVjlDLE1BQU0rQztRQUNOakMsU0FBUztRQUNUNkIsT0FBTztJQUNUO0lBQ0FLLFNBQVM7UUFBQ25EO0tBQXNCO0FBQ2xDLEdBQ0E7SUFDRW9ELFlBQVk7QUFDZDtBQUdGLGtCQUFrQjtBQUNYLFNBQVNDO0lBQ2QsMkRBQTJEO0lBQzNELElBQUksQ0FBQ3pELHdEQUFlLENBQUMyRCxRQUFRLEVBQUU7UUFDN0IzRCxxREFBYyxDQUFDLFlBQVl3QjtRQUMzQnFDLFFBQVFDLEdBQUcsQ0FBQztJQUNkO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kb2N1bWVudC10cmFja2VyLy4vc3JjL2xpYi9kYi9yZWdpc3Rlci1tb2RlbHMudHM/MDM3YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSAnbW9uZ29vc2UnO1xuaW1wb3J0IHsgRG9jdW1lbnRTdGF0dXMsIERvY3VtZW50Q2F0ZWdvcnksIERpdmlzaW9uIH0gZnJvbSAnQC90eXBlcyc7XG5cbi8vIERlZmluZSB0aGUgRG9jdW1lbnQgSm91cm5leSBTY2hlbWFcbmNvbnN0IERvY3VtZW50Sm91cm5leVNjaGVtYSA9IG5ldyBtb25nb29zZS5TY2hlbWEoe1xuICBhY3Rpb246IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgcmVxdWlyZWQ6IFt0cnVlLCAnUGxlYXNlIHByb3ZpZGUgYW4gYWN0aW9uJ10sXG4gIH0sXG4gIGZyb21EaXZpc2lvbjoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICBlbnVtOiBPYmplY3QudmFsdWVzKERpdmlzaW9uKSxcbiAgfSxcbiAgdG9EaXZpc2lvbjoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICBlbnVtOiBPYmplY3QudmFsdWVzKERpdmlzaW9uKSxcbiAgfSxcbiAgYnlVc2VyOiB7XG4gICAgdHlwZTogbW9uZ29vc2UuU2NoZW1hLlR5cGVzLk9iamVjdElkLFxuICAgIHJlZjogJ1VzZXInLFxuICAgIHJlcXVpcmVkOiBbdHJ1ZSwgJ1BsZWFzZSBwcm92aWRlIGEgdXNlciddLFxuICB9LFxuICB0aW1lc3RhbXA6IHtcbiAgICB0eXBlOiBEYXRlLFxuICAgIGRlZmF1bHQ6IERhdGUubm93LFxuICB9LFxuICBub3Rlczoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgfSxcbn0pO1xuXG4vLyBEZWZpbmUgdGhlIERvY3VtZW50IFNjaGVtYVxuY29uc3QgRG9jdW1lbnRTY2hlbWEgPSBuZXcgbW9uZ29vc2UuU2NoZW1hKFxuICB7XG4gICAgdGl0bGU6IHtcbiAgICAgIHR5cGU6IFN0cmluZyxcbiAgICAgIHJlcXVpcmVkOiBbdHJ1ZSwgJ1BsZWFzZSBwcm92aWRlIGEgdGl0bGUnXSxcbiAgICAgIG1heGxlbmd0aDogWzEwMCwgJ1RpdGxlIGNhbm5vdCBiZSBtb3JlIHRoYW4gMTAwIGNoYXJhY3RlcnMnXSxcbiAgICB9LFxuICAgIGRlc2NyaXB0aW9uOiB7XG4gICAgICB0eXBlOiBTdHJpbmcsXG4gICAgICByZXF1aXJlZDogW3RydWUsICdQbGVhc2UgcHJvdmlkZSBhIGRlc2NyaXB0aW9uJ10sXG4gICAgfSxcbiAgICBjYXRlZ29yeToge1xuICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgZW51bTogT2JqZWN0LnZhbHVlcyhEb2N1bWVudENhdGVnb3J5KSxcbiAgICAgIHJlcXVpcmVkOiBbdHJ1ZSwgJ1BsZWFzZSBwcm92aWRlIGEgY2F0ZWdvcnknXSxcbiAgICB9LFxuICAgIHN0YXR1czoge1xuICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgZGVmYXVsdDogRG9jdW1lbnRTdGF0dXMuSU5CT1gsXG4gICAgICAvLyBVc2UgYSBjdXN0b20gdmFsaWRhdG9yIGluc3RlYWQgb2YgZW51bSB0byBlbnN1cmUgaXQgd29ya3MgcHJvcGVybHkgd2l0aCBjYXNlLWluc2Vuc2l0aXZpdHlcbiAgICAgIHZhbGlkYXRlOiB7XG4gICAgICAgIHZhbGlkYXRvcjogZnVuY3Rpb24odjogc3RyaW5nKSB7XG4gICAgICAgICAgLy8gQ29udmVydCBib3RoIHRvIGxvd2VyY2FzZSBmb3IgY2FzZS1pbnNlbnNpdGl2ZSBjb21wYXJpc29uXG4gICAgICAgICAgcmV0dXJuIE9iamVjdC52YWx1ZXMoRG9jdW1lbnRTdGF0dXMpLm1hcChzID0+IHMudG9Mb3dlckNhc2UoKSkuaW5jbHVkZXModi50b0xvd2VyQ2FzZSgpKTtcbiAgICAgICAgfSxcbiAgICAgICAgbWVzc2FnZTogKHByb3BzOiBhbnkpID0+IGAke3Byb3BzLnZhbHVlfSBpcyBub3QgYSB2YWxpZCBzdGF0dXMuIFZhbGlkIHN0YXR1c2VzIGFyZTogJHtPYmplY3QudmFsdWVzKERvY3VtZW50U3RhdHVzKS5qb2luKCcsICcpfWBcbiAgICAgIH1cbiAgICB9LFxuICAgIGNyZWF0ZWRCeToge1xuICAgICAgdHlwZTogbW9uZ29vc2UuU2NoZW1hLlR5cGVzLk9iamVjdElkLFxuICAgICAgcmVmOiAnVXNlcicsXG4gICAgICByZXF1aXJlZDogW3RydWUsICdQbGVhc2UgcHJvdmlkZSBhIHVzZXInXSxcbiAgICB9LFxuICAgIGN1cnJlbnRMb2NhdGlvbjoge1xuICAgICAgdHlwZTogU3RyaW5nLFxuICAgICAgZW51bTogT2JqZWN0LnZhbHVlcyhEaXZpc2lvbiksXG4gICAgICByZXF1aXJlZDogW3RydWUsICdQbGVhc2UgcHJvdmlkZSBhIGN1cnJlbnQgbG9jYXRpb24nXSxcbiAgICB9LFxuICAgIHJlY2lwaWVudElkOiB7XG4gICAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgICByZWY6ICdVc2VyJyxcbiAgICB9LFxuICAgIHJlbGF0ZWREb2N1bWVudElkOiB7XG4gICAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgICByZWY6ICdEb2N1bWVudCcsXG4gICAgfSxcbiAgICBmaWxlVXJsOiB7XG4gICAgICB0eXBlOiBTdHJpbmcsXG4gICAgfSxcbiAgICBmaWxlTmFtZToge1xuICAgICAgdHlwZTogU3RyaW5nLFxuICAgIH0sXG4gICAgZmlsZVR5cGU6IHtcbiAgICAgIHR5cGU6IFN0cmluZyxcbiAgICB9LFxuICAgIHRyYWNraW5nTnVtYmVyOiB7XG4gICAgICB0eXBlOiBTdHJpbmcsXG4gICAgICAvLyBVc2luZyBzcGFyc2UgaW5kZXggdG8gcHJldmVudCBkdXBsaWNhdGUga2V5IGVycm9ycyB3aXRoIG51bGwvdW5kZWZpbmVkIHZhbHVlc1xuICAgICAgaW5kZXg6IHsgc3BhcnNlOiB0cnVlIH0sXG4gICAgICB2YWxpZGF0ZToge1xuICAgICAgICB2YWxpZGF0b3I6IGZ1bmN0aW9uKHY6IHN0cmluZyB8IHVuZGVmaW5lZCB8IG51bGwpIHtcbiAgICAgICAgICAvLyBBbGxvdyB1bmRlZmluZWQgb3IgbnVsbCB2YWx1ZXMgKHdpbGwgYmUgc2V0IGxhdGVyKVxuICAgICAgICAgIGlmICghdikgcmV0dXJuIHRydWU7XG5cbiAgICAgICAgICAvLyBWYWxpZGF0ZSB0aGUgZm9ybWF0OiBNR0JSMi1ZWVlZLU5OTk4tTk5OTlxuICAgICAgICAgIHJldHVybiB0eXBlb2YgdiA9PT0gJ3N0cmluZycgJiYgL15NR0JSMi1cXGR7NH0tXFxkezR9LVxcZHs0fSQvLnRlc3Qodik7XG4gICAgICAgIH0sXG4gICAgICAgIG1lc3NhZ2U6IChwcm9wczogYW55KSA9PiBgJHtwcm9wcy52YWx1ZX0gaXMgbm90IGEgdmFsaWQgdHJhY2tpbmcgbnVtYmVyIGZvcm1hdC4gRXhwZWN0ZWQgZm9ybWF0OiBNR0JSMi1ZWVlZLU5OTk4tTk5OTmBcbiAgICAgIH1cbiAgICB9LFxuICAgIGlzT3JpZ2luYWw6IHtcbiAgICAgIHR5cGU6IEJvb2xlYW4sXG4gICAgICBkZWZhdWx0OiBmYWxzZSxcbiAgICAgIGluZGV4OiB0cnVlLCAvLyBJbmRleCBmb3IgZmFzdGVyIGxvb2t1cHNcbiAgICB9LFxuICAgIGpvdXJuZXk6IFtEb2N1bWVudEpvdXJuZXlTY2hlbWFdLFxuICB9LFxuICB7XG4gICAgdGltZXN0YW1wczogdHJ1ZSxcbiAgfVxuKTtcblxuLy8gUmVnaXN0ZXIgbW9kZWxzXG5leHBvcnQgZnVuY3Rpb24gcmVnaXN0ZXJNb2RlbHMoKSB7XG4gIC8vIE9ubHkgcmVnaXN0ZXIgbW9kZWxzIGlmIHRoZXkgaGF2ZW4ndCBiZWVuIHJlZ2lzdGVyZWQgeWV0XG4gIGlmICghbW9uZ29vc2UubW9kZWxzLkRvY3VtZW50KSB7XG4gICAgbW9uZ29vc2UubW9kZWwoJ0RvY3VtZW50JywgRG9jdW1lbnRTY2hlbWEpO1xuICAgIGNvbnNvbGUubG9nKCdEb2N1bWVudCBtb2RlbCByZWdpc3RlcmVkJyk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJtb25nb29zZSIsIkRvY3VtZW50U3RhdHVzIiwiRG9jdW1lbnRDYXRlZ29yeSIsIkRpdmlzaW9uIiwiRG9jdW1lbnRKb3VybmV5U2NoZW1hIiwiU2NoZW1hIiwiYWN0aW9uIiwidHlwZSIsIlN0cmluZyIsInJlcXVpcmVkIiwiZnJvbURpdmlzaW9uIiwiZW51bSIsIk9iamVjdCIsInZhbHVlcyIsInRvRGl2aXNpb24iLCJieVVzZXIiLCJUeXBlcyIsIk9iamVjdElkIiwicmVmIiwidGltZXN0YW1wIiwiRGF0ZSIsImRlZmF1bHQiLCJub3ciLCJub3RlcyIsIkRvY3VtZW50U2NoZW1hIiwidGl0bGUiLCJtYXhsZW5ndGgiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5Iiwic3RhdHVzIiwiSU5CT1giLCJ2YWxpZGF0ZSIsInZhbGlkYXRvciIsInYiLCJtYXAiLCJzIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsIm1lc3NhZ2UiLCJwcm9wcyIsInZhbHVlIiwiam9pbiIsImNyZWF0ZWRCeSIsImN1cnJlbnRMb2NhdGlvbiIsInJlY2lwaWVudElkIiwicmVsYXRlZERvY3VtZW50SWQiLCJmaWxlVXJsIiwiZmlsZU5hbWUiLCJmaWxlVHlwZSIsInRyYWNraW5nTnVtYmVyIiwiaW5kZXgiLCJzcGFyc2UiLCJ0ZXN0IiwiaXNPcmlnaW5hbCIsIkJvb2xlYW4iLCJqb3VybmV5IiwidGltZXN0YW1wcyIsInJlZ2lzdGVyTW9kZWxzIiwibW9kZWxzIiwiRG9jdW1lbnQiLCJtb2RlbCIsImNvbnNvbGUiLCJsb2ciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/register-models.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/AuditLog.ts":
/*!********************************!*\
  !*** ./src/models/AuditLog.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n\n\nconst AuditLogSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    action: {\n        type: String,\n        enum: Object.values(_types_audit__WEBPACK_IMPORTED_MODULE_1__.AuditLogAction),\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    performedBy: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user who performed the action\"\n        ]\n    },\n    targetId: {\n        type: String\n    },\n    targetType: {\n        type: String\n    },\n    details: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.Mixed\n    },\n    ipAddress: {\n        type: String\n    },\n    userAgent: {\n        type: String\n    }\n}, {\n    timestamps: true\n});\n// Create a text index for searching\nAuditLogSchema.index({\n    action: \"text\",\n    targetType: \"text\"\n});\n// Fix for \"Cannot read properties of undefined (reading 'AuditLog')\" error\n// Check if the model exists in mongoose.models before trying to access it\nlet AuditLog;\ntry {\n    // Try to get the existing model\n    AuditLog = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AuditLog\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    AuditLog = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AuditLog\", AuditLogSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuditLog);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/AuditLog.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Document.ts":
/*!********************************!*\
  !*** ./src/models/Document.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\nconst DocumentJourneySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    action: {\n        type: String,\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    fromDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    toDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    byUser: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    timestamp: {\n        type: Date,\n        default: Date.now\n    },\n    notes: {\n        type: String\n    }\n});\nconst DocumentSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a title\"\n        ],\n        maxlength: [\n            100,\n            \"Title cannot be more than 100 characters\"\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a description\"\n        ]\n    },\n    category: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentCategory),\n        required: [\n            true,\n            \"Please provide a category\"\n        ]\n    },\n    action: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentAction),\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentAction.NONE\n    },\n    status: {\n        type: String,\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX,\n        // Use a custom validator instead of enum to ensure it works properly\n        validate: {\n            validator: function(v) {\n                // Convert both to lowercase for case-insensitive comparison\n                return Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).map((s)=>s.toLowerCase()).includes(v.toLowerCase());\n            },\n            message: (props)=>`${props.value} is not a valid status. Valid statuses are: ${Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).join(\", \")}`\n        }\n    },\n    createdBy: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    currentLocation: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a current location\"\n        ]\n    },\n    recipientId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\"\n    },\n    relatedDocumentId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Document\"\n    },\n    fileUrl: {\n        type: String\n    },\n    fileName: {\n        type: String\n    },\n    fileType: {\n        type: String\n    },\n    trackingNumber: {\n        type: String,\n        // Removed unique constraint to allow multiple documents to share the same tracking number\n        // This is necessary for forwarded documents to maintain the same tracking number\n        // Using sparse index to prevent duplicate key errors with null/undefined values\n        index: {\n            sparse: true\n        },\n        validate: {\n            validator: function(v) {\n                // Allow undefined or null values (will be set later)\n                if (!v) return true;\n                // Validate the format: MGBR2-YYYY-NNNN-NNNN\n                return typeof v === \"string\" && /^MGBR2-\\d{4}-\\d{4}-\\d{4}$/.test(v);\n            },\n            message: (props)=>`${props.value} is not a valid tracking number format. Expected format: MGBR2-YYYY-NNNN-NNNN`\n        }\n    },\n    isOriginal: {\n        type: Boolean,\n        default: false,\n        index: true\n    },\n    journey: [\n        DocumentJourneySchema\n    ]\n}, {\n    timestamps: true\n});\n// Fix for \"Cannot read properties of undefined\" error\nlet DocumentModel;\ntry {\n    // Try to get the existing model\n    DocumentModel = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Document\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    DocumentModel = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Document\", DocumentSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocumentModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Document.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\nconst SessionInfoSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    token: {\n        type: String,\n        required: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    },\n    lastActive: {\n        type: Date,\n        default: Date.now\n    },\n    userAgent: {\n        type: String\n    },\n    ipAddress: {\n        type: String\n    }\n});\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a name\"\n        ],\n        maxlength: [\n            60,\n            \"Name cannot be more than 60 characters\"\n        ],\n        unique: true,\n        trim: true\n    },\n    email: {\n        type: String,\n        required: false,\n        lowercase: true,\n        trim: true,\n        unique: true,\n        sparse: true\n    },\n    password: {\n        type: String,\n        select: false\n    },\n    role: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.UserRole),\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.UserRole.EMPLOYEE\n    },\n    division: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a division\"\n        ]\n    },\n    image: {\n        type: String\n    },\n    activeSessions: {\n        type: [\n            SessionInfoSchema\n        ],\n        default: []\n    }\n}, {\n    timestamps: true\n});\n// Fix for \"Cannot read properties of undefined\" error\nlet User;\ntry {\n    // Try to get the existing model\n    User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (User);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/core/statsClient.ts":
/*!************************************************!*\
  !*** ./src/services/stats/core/statsClient.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsClient: () => (/* binding */ StatsClient)\n/* harmony export */ });\n/* harmony import */ var _models_Document__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/Document */ \"(rsc)/./src/models/Document.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/services/stats/core/types.ts\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_4__);\n/**\n * Stats Client\n * Core database operations and common queries for statistics\n */ \n\n\n\n\nclass StatsClient {\n    /**\n   * Get total unique documents count by tracking number\n   */ static async getTotalUniqueDocuments(query = {}) {\n        const result = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: \"$trackingNumber\",\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $count: \"count\"\n            }\n        ]);\n        return result.length > 0 ? result[0].count : 0;\n    }\n    /**\n   * Get documents by division with deduplication\n   */ static async getDocumentsByDivision(query = {}) {\n        const divisionCounts = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: {\n                        trackingNumber: \"$trackingNumber\",\n                        division: \"$currentLocation\"\n                    },\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: \"$doc.currentLocation\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ]);\n        const result = {\n            ..._types__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DIVISION_COUNTS\n        };\n        divisionCounts.forEach((item)=>{\n            if (item._id && item._id in result) {\n                const division = item._id;\n                result[division] = item.count;\n            }\n        });\n        return result;\n    }\n    /**\n   * Get documents by status with deduplication and user responsibility filtering\n   */ static async getDocumentsByStatus(query = {}) {\n        const statusCounts = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: {\n                        trackingNumber: \"$trackingNumber\",\n                        status: \"$status\"\n                    },\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: \"$_id.status\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ]);\n        const result = {\n            ..._types__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_STATUS_COUNTS\n        };\n        statusCounts.forEach((item)=>{\n            if (item._id && item._id in result) {\n                result[item._id] = item.count;\n            }\n        });\n        return result;\n    }\n    /**\n   * Get documents by status with user responsibility filtering\n   * This method ensures users only see counts for documents they are responsible for\n   */ static async getDocumentsByStatusForUser(userId, userRole, userDivision) {\n        const userObjectId = new (mongoose__WEBPACK_IMPORTED_MODULE_4___default().Types).ObjectId(userId);\n        // Build different queries based on user responsibility for each status\n        const statusQueries = {\n            [_types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.SENT]: {\n                createdBy: userObjectId,\n                status: _types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.SENT\n            },\n            [_types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.PENDING]: {\n                recipientId: userObjectId,\n                status: _types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.PENDING\n            },\n            [_types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.PROCESSED]: {\n                recipientId: userObjectId,\n                status: _types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.PROCESSED\n            },\n            [_types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.RECEIVED]: {\n                recipientId: userObjectId,\n                status: _types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.RECEIVED\n            },\n            [_types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.FORWARDED]: {\n                recipientId: userObjectId,\n                status: _types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.FORWARDED\n            },\n            [_types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.INBOX]: {\n                recipientId: userObjectId,\n                status: _types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.INBOX\n            },\n            [_types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.ARCHIVED]: {\n                $or: [\n                    {\n                        createdBy: userObjectId,\n                        status: _types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.ARCHIVED\n                    },\n                    {\n                        recipientId: userObjectId,\n                        status: _types__WEBPACK_IMPORTED_MODULE_2__.DocumentStatus.ARCHIVED\n                    }\n                ]\n            }\n        };\n        const result = {\n            ..._types__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_STATUS_COUNTS\n        };\n        // Count each status separately with proper user responsibility filtering\n        for (const [status, statusQuery] of Object.entries(statusQueries)){\n            const count = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n                {\n                    $match: statusQuery\n                },\n                {\n                    $group: {\n                        _id: \"$trackingNumber\",\n                        doc: {\n                            $first: \"$$ROOT\"\n                        }\n                    }\n                },\n                {\n                    $count: \"total\"\n                }\n            ]);\n            result[status] = count.length > 0 ? count[0].total : 0;\n        }\n        return result;\n    }\n    /**\n   * Get users by division\n   */ static async getUsersByDivision() {\n        const divisionCounts = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].aggregate([\n            {\n                $group: {\n                    _id: \"$division\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ]);\n        const result = {\n            ..._types__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DIVISION_COUNTS\n        };\n        divisionCounts.forEach((item)=>{\n            if (item._id && item._id in result) {\n                const division = item._id;\n                result[division] = item.count;\n            }\n        });\n        return result;\n    }\n    /**\n   * Get total users count\n   */ static async getTotalUsers() {\n        return await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].countDocuments();\n    }\n    /**\n   * Build query based on user context and role\n   */ static buildUserContextQuery(context) {\n        const { userId, userRole, userDivision } = context;\n        let query = {};\n        // Convert userId to ObjectId for proper MongoDB comparison\n        const userObjectId = new (mongoose__WEBPACK_IMPORTED_MODULE_4___default().Types).ObjectId(userId);\n        if (userRole === \"REGIONAL_DIRECTOR\") {\n            // Regional Director can see all documents\n            return query;\n        } else if (userRole === \"DIVISION_CHIEF\") {\n            query.$or = [\n                {\n                    currentLocation: userDivision\n                },\n                {\n                    recipientId: userObjectId\n                }\n            ];\n        } else {\n            query.$or = [\n                {\n                    createdBy: userObjectId\n                },\n                {\n                    recipientId: userObjectId\n                }\n            ];\n        }\n        return query;\n    }\n    /**\n   * Get documents with journey data\n   */ static async getDocumentsWithJourney(query = {}) {\n        return await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].find({\n            ...query,\n            journey: {\n                $exists: true,\n                $ne: []\n            }\n        }).select(\"journey category currentLocation trackingNumber status createdAt createdBy recipientId\");\n    }\n    /**\n   * Get recent documents for dashboard with deduplication by tracking number\n   * Shows the user's last action with each document, not the document's current status\n   */ static async getRecentDocuments(query, limit = 10, userId) {\n        const recentDocuments = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $sort: {\n                    createdAt: -1\n                }\n            },\n            {\n                $group: {\n                    _id: \"$trackingNumber\",\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $sort: {\n                    \"doc.createdAt\": -1\n                }\n            },\n            {\n                $limit: limit\n            },\n            {\n                $replaceRoot: {\n                    newRoot: \"$doc\"\n                }\n            }\n        ]);\n        // Populate the creator and recipient information\n        const populatedDocuments = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].populate(recentDocuments, [\n            {\n                path: \"createdBy\",\n                select: \"name division\"\n            },\n            {\n                path: \"recipientId\",\n                select: \"name division\"\n            },\n            {\n                path: \"journey.byUser\",\n                select: \"name division\"\n            }\n        ]);\n        // If userId is provided, determine the user's last action with each document\n        if (userId) {\n            return populatedDocuments.map((doc)=>{\n                const userLastAction = this.getUserLastActionWithDocument(doc, userId);\n                // Only include documents where user has actually interacted\n                if (userLastAction) {\n                    return {\n                        ...doc,\n                        userLastAction: userLastAction.action,\n                        userLastActionTimestamp: userLastAction.timestamp,\n                        displayStatus: userLastAction.action // Use user's action instead of document status\n                    };\n                }\n                return null;\n            }).filter((doc)=>doc !== null); // Remove documents where user hasn't interacted\n        }\n        return populatedDocuments;\n    }\n    /**\n   * Determine the user's current relationship with a document\n   * Shows documents where user is involved (created, received, or interacted with)\n   */ static getUserLastActionWithDocument(document, userId) {\n        // Check if user created the document\n        const isCreator = document.createdBy && document.createdBy._id && document.createdBy._id.toString() === userId;\n        // Check if user is the recipient\n        const isRecipient = document.recipientId && document.recipientId._id && document.recipientId._id.toString() === userId;\n        // Find the user's actual interactions in the journey\n        const userJourneyEntries = document.journey.filter((entry)=>entry.byUser && entry.byUser.toString() === userId).sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n        // If user has interacted with the document\n        if (userJourneyEntries.length > 0) {\n            const lastAction = userJourneyEntries[0];\n            // Determine the current state from user's perspective\n            let currentUserAction = lastAction.action;\n            // If user sent/created the document, show current status from sender's perspective\n            if (lastAction.action === \"CREATED_AND_SENT\" || lastAction.action === \"SENT\") {\n                if (document.status === \"PROCESSED\") {\n                    currentUserAction = \"PROCESSED\"; // Document was processed by recipient\n                } else if (document.status === \"FORWARDED\") {\n                    currentUserAction = \"FORWARDED\"; // Document was forwarded by recipient\n                } else {\n                    currentUserAction = \"SENT\"; // Still pending with recipient\n                }\n            }\n            return {\n                action: currentUserAction,\n                timestamp: lastAction.timestamp\n            };\n        }\n        // If user created the document but no journey entries (shouldn't happen, but fallback)\n        if (isCreator) {\n            return {\n                action: \"SENT\",\n                timestamp: document.createdAt\n            };\n        }\n        // If user is recipient but hasn't interacted yet, show based on document status\n        if (isRecipient) {\n            // Show pending documents in user's recent activity\n            if (document.status === \"PENDING\" || document.status === \"SENT\") {\n                return {\n                    action: \"PENDING\",\n                    timestamp: document.createdAt\n                };\n            }\n            // Show other statuses as well if user is the recipient\n            return {\n                action: document.status,\n                timestamp: document.updatedAt || document.createdAt\n            };\n        }\n        // Don't show documents where user has no relationship\n        return null;\n    }\n    /**\n   * Get documents by category with deduplication\n   */ static async getDocumentsByCategory(query = {}) {\n        const categoryCounts = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: {\n                        trackingNumber: \"$trackingNumber\",\n                        category: \"$category\"\n                    },\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: \"$doc.category\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ]);\n        const result = {};\n        categoryCounts.forEach((item)=>{\n            if (item._id) {\n                result[item._id] = item.count;\n            }\n        });\n        return result;\n    }\n    /**\n   * Get time series data with deduplication\n   */ static async getTimeSeriesData(query, groupBy = \"day\") {\n        let timeSeriesGroupBy;\n        switch(groupBy){\n            case \"week\":\n                timeSeriesGroupBy = {\n                    $dateToString: {\n                        format: \"%Y-W%U\",\n                        date: \"$createdAt\"\n                    }\n                };\n                break;\n            case \"month\":\n                timeSeriesGroupBy = {\n                    $dateToString: {\n                        format: \"%Y-%m\",\n                        date: \"$createdAt\"\n                    }\n                };\n                break;\n            default:\n                timeSeriesGroupBy = {\n                    $dateToString: {\n                        format: \"%Y-%m-%d\",\n                        date: \"$createdAt\"\n                    }\n                };\n        }\n        const timeSeriesData = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: {\n                        trackingNumber: \"$trackingNumber\",\n                        date: timeSeriesGroupBy,\n                        status: \"$status\"\n                    },\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: {\n                        date: \"$_id.date\",\n                        status: \"$doc.status\"\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    \"_id.date\": 1\n                }\n            }\n        ]);\n        const result = {};\n        timeSeriesData.forEach((item)=>{\n            const date = item._id.date;\n            const status = item._id.status;\n            if (!result[date]) {\n                result[date] = {};\n            }\n            result[date][status] = item.count;\n        });\n        return result;\n    }\n    /**\n   * Get top users by document count\n   */ static async getTopUsers(query, limit = 10) {\n        const topUsers = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: {\n                        trackingNumber: \"$trackingNumber\",\n                        createdBy: \"$createdBy\"\n                    },\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: \"$doc.createdBy\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    count: -1\n                }\n            },\n            {\n                $limit: limit\n            },\n            {\n                $lookup: {\n                    from: \"users\",\n                    localField: \"_id\",\n                    foreignField: \"_id\",\n                    as: \"userInfo\"\n                }\n            },\n            {\n                $project: {\n                    userId: {\n                        $toString: \"$_id\"\n                    },\n                    count: 1,\n                    name: {\n                        $arrayElemAt: [\n                            \"$userInfo.name\",\n                            0\n                        ]\n                    },\n                    division: {\n                        $arrayElemAt: [\n                            \"$userInfo.division\",\n                            0\n                        ]\n                    }\n                }\n            }\n        ]);\n        return topUsers.map((user)=>({\n                userId: user.userId,\n                name: user.name || \"Unknown\",\n                division: user.division || \"Unknown\",\n                count: user.count\n            }));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/core/statsClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/core/types.ts":
/*!******************************************!*\
  !*** ./src/services/stats/core/types.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_DIVISION_COUNTS: () => (/* binding */ DEFAULT_DIVISION_COUNTS),\n/* harmony export */   DEFAULT_STATUS_COUNTS: () => (/* binding */ DEFAULT_STATUS_COUNTS),\n/* harmony export */   STATS_CONFIG: () => (/* binding */ STATS_CONFIG)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Stats Service Types\n * Centralized type definitions for all statistics operations\n */ \nconst STATS_CONFIG = {\n    defaultLimit: 10,\n    maxDateRange: 365,\n    cacheTimeout: 5 * 60 * 1000 // 5 minutes\n};\nconst DEFAULT_DIVISION_COUNTS = {\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.ORD]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.FAD]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.MMD]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.MSESDD]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.Division.GSD]: 0\n};\nconst DEFAULT_STATUS_COUNTS = {\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.INBOX]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.SENT]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.PENDING]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.PROCESSED]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.FORWARDED]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.RECEIVED]: 0,\n    [_types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.ARCHIVED]: 0\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/core/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/index.ts":
/*!*************************************!*\
  !*** ./src/services/stats/index.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminStatsService: () => (/* reexport safe */ _services_adminStatsService__WEBPACK_IMPORTED_MODULE_2__.AdminStatsService),\n/* harmony export */   AggregationHelpers: () => (/* reexport safe */ _utils_aggregationHelpers__WEBPACK_IMPORTED_MODULE_7__.AggregationHelpers),\n/* harmony export */   DEFAULT_DIVISION_COUNTS: () => (/* reexport safe */ _core_types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_DIVISION_COUNTS),\n/* harmony export */   DEFAULT_STATUS_COUNTS: () => (/* reexport safe */ _core_types__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_STATUS_COUNTS),\n/* harmony export */   DashboardStatsService: () => (/* reexport safe */ _services_dashboardStatsService__WEBPACK_IMPORTED_MODULE_3__.DashboardStatsService),\n/* harmony export */   ProcessingStatsService: () => (/* reexport safe */ _services_processingStatsService__WEBPACK_IMPORTED_MODULE_5__.ProcessingStatsService),\n/* harmony export */   ReportStatsService: () => (/* reexport safe */ _services_reportStatsService__WEBPACK_IMPORTED_MODULE_6__.ReportStatsService),\n/* harmony export */   STATS_CONFIG: () => (/* reexport safe */ _core_types__WEBPACK_IMPORTED_MODULE_1__.STATS_CONFIG),\n/* harmony export */   StatsClient: () => (/* reexport safe */ _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient),\n/* harmony export */   VolumeStatsService: () => (/* reexport safe */ _services_volumeStatsService__WEBPACK_IMPORTED_MODULE_4__.VolumeStatsService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAdminStats: () => (/* binding */ getAdminStats),\n/* harmony export */   getDashboardStats: () => (/* binding */ getDashboardStats),\n/* harmony export */   getDocumentVolumeStats: () => (/* binding */ getDocumentVolumeStats),\n/* harmony export */   getProcessingTimeStats: () => (/* binding */ getProcessingTimeStats),\n/* harmony export */   getVolumeReportStats: () => (/* binding */ getVolumeReportStats)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/* harmony import */ var _core_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core/types */ \"(rsc)/./src/services/stats/core/types.ts\");\n/* harmony import */ var _services_adminStatsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/adminStatsService */ \"(rsc)/./src/services/stats/services/adminStatsService.ts\");\n/* harmony import */ var _services_dashboardStatsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./services/dashboardStatsService */ \"(rsc)/./src/services/stats/services/dashboardStatsService.ts\");\n/* harmony import */ var _services_volumeStatsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./services/volumeStatsService */ \"(rsc)/./src/services/stats/services/volumeStatsService.ts\");\n/* harmony import */ var _services_processingStatsService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./services/processingStatsService */ \"(rsc)/./src/services/stats/services/processingStatsService.ts\");\n/* harmony import */ var _services_reportStatsService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./services/reportStatsService */ \"(rsc)/./src/services/stats/services/reportStatsService.ts\");\n/* harmony import */ var _utils_aggregationHelpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/aggregationHelpers */ \"(rsc)/./src/services/stats/utils/aggregationHelpers.ts\");\n/**\n * Stats Services - Main Export\n * Centralized export for all statistics services and utilities\n */ // Core Infrastructure\n\n\n// Specialized Services\n\n\n\n\n\n// Utilities\n\n// Legacy compatibility wrapper (deprecated)\n\n\n\n\n\n/**\n * @deprecated Use AdminStatsService.getAdminStats() instead\n */ const getAdminStats = async ()=>{\n    console.warn(\"getAdminStats is deprecated. Use AdminStatsService.getAdminStats() instead.\");\n    return _services_adminStatsService__WEBPACK_IMPORTED_MODULE_2__.AdminStatsService.getAdminStats();\n};\n/**\n * @deprecated Use DashboardStatsService.getDashboardStats() instead\n */ const getDashboardStats = async (context)=>{\n    console.warn(\"getDashboardStats is deprecated. Use DashboardStatsService.getDashboardStats() instead.\");\n    return _services_dashboardStatsService__WEBPACK_IMPORTED_MODULE_3__.DashboardStatsService.getDashboardStats(context);\n};\n/**\n * @deprecated Use VolumeStatsService.getDocumentVolumeStats() instead\n */ const getDocumentVolumeStats = async (startDate, endDate, division)=>{\n    console.warn(\"getDocumentVolumeStats is deprecated. Use VolumeStatsService.getDocumentVolumeStats() instead.\");\n    return _services_volumeStatsService__WEBPACK_IMPORTED_MODULE_4__.VolumeStatsService.getDocumentVolumeStats(startDate, endDate, division);\n};\n/**\n * @deprecated Use ProcessingStatsService.getProcessingTimeStats() instead\n */ const getProcessingTimeStats = async (startDate, endDate)=>{\n    console.warn(\"getProcessingTimeStats is deprecated. Use ProcessingStatsService.getProcessingTimeStats() instead.\");\n    return _services_processingStatsService__WEBPACK_IMPORTED_MODULE_5__.ProcessingStatsService.getProcessingTimeStats(startDate, endDate);\n};\n/**\n * @deprecated Use ReportStatsService.getVolumeReport() instead\n */ const getVolumeReportStats = async (filters)=>{\n    console.warn(\"getVolumeReportStats is deprecated. Use ReportStatsService.getVolumeReport() instead.\");\n    return _services_reportStatsService__WEBPACK_IMPORTED_MODULE_6__.ReportStatsService.getVolumeReport(filters);\n};\n// Default export for convenience\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    // Services\n    AdminStatsService: _services_adminStatsService__WEBPACK_IMPORTED_MODULE_2__.AdminStatsService,\n    DashboardStatsService: _services_dashboardStatsService__WEBPACK_IMPORTED_MODULE_3__.DashboardStatsService,\n    VolumeStatsService: _services_volumeStatsService__WEBPACK_IMPORTED_MODULE_4__.VolumeStatsService,\n    ProcessingStatsService: _services_processingStatsService__WEBPACK_IMPORTED_MODULE_5__.ProcessingStatsService,\n    ReportStatsService: _services_reportStatsService__WEBPACK_IMPORTED_MODULE_6__.ReportStatsService,\n    // Legacy functions (deprecated)\n    getAdminStats,\n    getDashboardStats,\n    getDocumentVolumeStats,\n    getProcessingTimeStats,\n    getVolumeReportStats\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/adminStatsService.ts":
/*!**********************************************************!*\
  !*** ./src/services/stats/services/adminStatsService.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminStatsService: () => (/* binding */ AdminStatsService)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/**\n * Admin Stats Service\n * Handles administrative dashboard statistics\n */ \nclass AdminStatsService {\n    /**\n   * Get comprehensive admin statistics\n   * @returns Admin dashboard statistics\n   */ static async getAdminStats() {\n        try {\n            // Get all statistics in parallel for better performance\n            const [totalDocuments, documentsByDivision, totalUsers, usersByDivision] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByDivision(),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUsers(),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getUsersByDivision()\n            ]);\n            return {\n                totalDocuments,\n                documentsByDivision,\n                totalUsers,\n                usersByDivision\n            };\n        } catch (error) {\n            console.error(\"Error getting admin stats:\", error);\n            throw new Error(\"Failed to retrieve admin statistics\");\n        }\n    }\n    /**\n   * Get admin statistics with date range filter\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @returns Filtered admin statistics\n   */ static async getAdminStatsWithDateRange(startDate, endDate) {\n        try {\n            let query = {};\n            if (startDate && endDate) {\n                query.createdAt = {\n                    $gte: startDate,\n                    $lte: endDate\n                };\n            }\n            const [totalDocuments, documentsByDivision, totalUsers, usersByDivision] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByDivision(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUsers(),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getUsersByDivision()\n            ]);\n            return {\n                totalDocuments,\n                documentsByDivision,\n                totalUsers,\n                usersByDivision\n            };\n        } catch (error) {\n            console.error(\"Error getting admin stats with date range:\", error);\n            throw new Error(\"Failed to retrieve filtered admin statistics\");\n        }\n    }\n    /**\n   * Get admin statistics summary for quick overview\n   * @returns Simplified admin statistics\n   */ static async getAdminStatsSummary() {\n        try {\n            const stats = await this.getAdminStats();\n            // Find most and least active divisions\n            const divisionEntries = Object.entries(stats.documentsByDivision);\n            const mostActive = divisionEntries.reduce((max, current)=>current[1] > max[1] ? current : max);\n            const leastActive = divisionEntries.reduce((min, current)=>current[1] < min[1] ? current : min);\n            return {\n                totalDocuments: stats.totalDocuments,\n                totalUsers: stats.totalUsers,\n                mostActiveDivision: mostActive[0],\n                leastActiveDivision: leastActive[0]\n            };\n        } catch (error) {\n            console.error(\"Error getting admin stats summary:\", error);\n            throw new Error(\"Failed to retrieve admin statistics summary\");\n        }\n    }\n    /**\n   * Get division performance comparison\n   * @returns Division performance metrics\n   */ static async getDivisionPerformance() {\n        try {\n            const stats = await this.getAdminStats();\n            const performance = Object.keys(stats.documentsByDivision).map((division)=>{\n                const documentCount = stats.documentsByDivision[division];\n                const userCount = stats.usersByDivision[division];\n                const documentsPerUser = userCount > 0 ? documentCount / userCount : 0;\n                let efficiency = \"low\";\n                if (documentsPerUser > 10) efficiency = \"high\";\n                else if (documentsPerUser > 5) efficiency = \"medium\";\n                return {\n                    division,\n                    documentCount,\n                    userCount,\n                    documentsPerUser: Math.round(documentsPerUser * 100) / 100,\n                    efficiency\n                };\n            });\n            return performance.sort((a, b)=>b.documentsPerUser - a.documentsPerUser);\n        } catch (error) {\n            console.error(\"Error getting division performance:\", error);\n            throw new Error(\"Failed to retrieve division performance metrics\");\n        }\n    }\n    /**\n   * Get system health metrics\n   * @returns System health indicators\n   */ static async getSystemHealth() {\n        try {\n            const stats = await this.getAdminStats();\n            const averageDocumentsPerUser = stats.totalUsers > 0 ? stats.totalDocuments / stats.totalUsers : 0;\n            const activeDivisions = Object.values(stats.documentsByDivision).filter((count)=>count > 0).length;\n            const recommendations = [];\n            let status = \"healthy\";\n            // Health checks\n            if (stats.totalUsers === 0) {\n                status = \"critical\";\n                recommendations.push(\"No users found in the system\");\n            } else if (stats.totalUsers < 5) {\n                status = \"warning\";\n                recommendations.push(\"Low user count - consider user onboarding\");\n            }\n            if (averageDocumentsPerUser < 1) {\n                status = \"warning\";\n                recommendations.push(\"Low document activity - encourage document creation\");\n            }\n            if (activeDivisions < 3) {\n                status = \"warning\";\n                recommendations.push(\"Some divisions have no document activity\");\n            }\n            if (recommendations.length === 0) {\n                recommendations.push(\"System is operating normally\");\n            }\n            return {\n                status,\n                metrics: {\n                    totalDocuments: stats.totalDocuments,\n                    totalUsers: stats.totalUsers,\n                    averageDocumentsPerUser: Math.round(averageDocumentsPerUser * 100) / 100,\n                    activeDivisions\n                },\n                recommendations\n            };\n        } catch (error) {\n            console.error(\"Error getting system health:\", error);\n            throw new Error(\"Failed to retrieve system health metrics\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/adminStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/dashboardStatsService.ts":
/*!**************************************************************!*\
  !*** ./src/services/stats/services/dashboardStatsService.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardStatsService: () => (/* binding */ DashboardStatsService)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * Dashboard Stats Service\n * Handles user dashboard statistics and metrics\n */ \n\n\nclass DashboardStatsService {\n    /**\n   * Get dashboard statistics for a specific user\n   * @param context User context (userId, role, division)\n   * @returns User-specific dashboard statistics\n   */ static async getDashboardStats(context) {\n        try {\n            const { userId, userRole, userDivision } = context;\n            // Build query based on user role and permissions\n            const query = _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(context);\n            // Get all statistics in parallel for better performance\n            const [totalDocuments, statusCounts, recentDocuments] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByStatusForUser(userId, userRole, userDivision),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getRecentDocuments(query, 10, userId) // Pass userId to get user-specific actions\n            ]);\n            return {\n                totalDocuments,\n                pendingDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING],\n                processedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED],\n                archivedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.ARCHIVED],\n                sentDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.SENT],\n                forwardedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.FORWARDED],\n                inboxDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX],\n                recentDocuments\n            };\n        } catch (error) {\n            console.error(\"Error getting dashboard stats:\", error);\n            throw new Error(\"Failed to retrieve dashboard statistics\");\n        }\n    }\n    /**\n   * Get dashboard statistics with date range filter\n   * @param context User context\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @returns Filtered dashboard statistics\n   */ static async getDashboardStatsWithDateRange(context, startDate, endDate) {\n        try {\n            let query = _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(context);\n            if (startDate && endDate) {\n                query.createdAt = {\n                    $gte: startDate,\n                    $lte: endDate\n                };\n            }\n            const [totalDocuments, statusCounts, recentDocuments] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByStatus(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getRecentDocuments(query, 10, context.userId) // Pass userId to get user-specific actions\n            ]);\n            return {\n                totalDocuments,\n                pendingDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING],\n                processedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED],\n                archivedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.ARCHIVED],\n                sentDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.SENT],\n                forwardedDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.FORWARDED],\n                inboxDocuments: statusCounts[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX],\n                recentDocuments\n            };\n        } catch (error) {\n            console.error(\"Error getting dashboard stats with date range:\", error);\n            throw new Error(\"Failed to retrieve filtered dashboard statistics\");\n        }\n    }\n    /**\n   * Get user productivity metrics\n   * @param context User context\n   * @returns User productivity statistics\n   */ static async getUserProductivityMetrics(context) {\n        try {\n            const { userId } = context;\n            // Convert userId to ObjectId for proper MongoDB comparison\n            const userObjectId = new (mongoose__WEBPACK_IMPORTED_MODULE_2___default().Types).ObjectId(userId);\n            // Get documents created by user\n            const createdQuery = {\n                createdBy: userObjectId\n            };\n            const documentsCreated = await _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(createdQuery);\n            // Get documents processed by user (received and processed)\n            const processedQuery = {\n                recipientId: userObjectId,\n                status: {\n                    $in: [\n                        _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED,\n                        _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.FORWARDED,\n                        _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.ARCHIVED\n                    ]\n                }\n            };\n            const documentsProcessed = await _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(processedQuery);\n            // Calculate productivity score (simple formula)\n            const productivityScore = Math.min(100, (documentsCreated * 2 + documentsProcessed) * 5);\n            // Get trend (simplified - compare last 7 days vs previous 7 days)\n            const now = new Date();\n            const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            const fourteenDaysAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);\n            const [recentActivity, previousActivity] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments({\n                    ...createdQuery,\n                    createdAt: {\n                        $gte: sevenDaysAgo\n                    }\n                }),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments({\n                    ...createdQuery,\n                    createdAt: {\n                        $gte: fourteenDaysAgo,\n                        $lt: sevenDaysAgo\n                    }\n                })\n            ]);\n            let trend = \"stable\";\n            if (recentActivity > previousActivity) trend = \"increasing\";\n            else if (recentActivity < previousActivity) trend = \"decreasing\";\n            return {\n                documentsCreated,\n                documentsProcessed,\n                averageProcessingTime: 0,\n                productivityScore,\n                trend\n            };\n        } catch (error) {\n            console.error(\"Error getting user productivity metrics:\", error);\n            throw new Error(\"Failed to retrieve user productivity metrics\");\n        }\n    }\n    /**\n   * Get dashboard summary for quick overview\n   * @param context User context\n   * @returns Simplified dashboard summary\n   */ static async getDashboardSummary(context) {\n        try {\n            const stats = await this.getDashboardStats(context);\n            const pendingActions = stats.pendingDocuments + stats.inboxDocuments;\n            // Get documents completed today\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            const tomorrow = new Date(today);\n            tomorrow.setDate(tomorrow.getDate() + 1);\n            const todayQuery = {\n                ..._core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(context),\n                status: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED,\n                updatedAt: {\n                    $gte: today,\n                    $lt: tomorrow\n                }\n            };\n            const completedToday = await _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(todayQuery);\n            // Determine priority based on pending actions\n            let priority = \"low\";\n            if (pendingActions > 10) priority = \"high\";\n            else if (pendingActions > 5) priority = \"medium\";\n            // Suggest next action\n            let nextAction = \"All caught up!\";\n            if (stats.inboxDocuments > 0) {\n                nextAction = `Review ${stats.inboxDocuments} document(s) in inbox`;\n            } else if (stats.pendingDocuments > 0) {\n                nextAction = `Process ${stats.pendingDocuments} pending document(s)`;\n            }\n            return {\n                totalDocuments: stats.totalDocuments,\n                pendingActions,\n                completedToday,\n                priority,\n                nextAction\n            };\n        } catch (error) {\n            console.error(\"Error getting dashboard summary:\", error);\n            throw new Error(\"Failed to retrieve dashboard summary\");\n        }\n    }\n    /**\n   * Get workload distribution for the user\n   * @param context User context\n   * @returns Workload distribution metrics\n   */ static async getWorkloadDistribution(context) {\n        try {\n            const query = _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(context);\n            const [statusCounts, categoryCounts] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByStatus(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByCategory(query)\n            ]);\n            const totalWorkload = Object.values(statusCounts).reduce((sum, count)=>sum + count, 0);\n            let workloadLevel = \"light\";\n            if (totalWorkload > 20) workloadLevel = \"heavy\";\n            else if (totalWorkload > 10) workloadLevel = \"moderate\";\n            const recommendations = [];\n            if (workloadLevel === \"heavy\") {\n                recommendations.push(\"Consider prioritizing urgent documents\");\n                recommendations.push(\"Delegate tasks if possible\");\n            } else if (workloadLevel === \"moderate\") {\n                recommendations.push(\"Maintain current pace\");\n                recommendations.push(\"Focus on pending documents\");\n            } else {\n                recommendations.push(\"Good workload balance\");\n                recommendations.push(\"Consider taking on additional responsibilities\");\n            }\n            return {\n                byStatus: statusCounts,\n                byCategory: categoryCounts,\n                workloadLevel,\n                recommendations\n            };\n        } catch (error) {\n            console.error(\"Error getting workload distribution:\", error);\n            throw new Error(\"Failed to retrieve workload distribution\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/dashboardStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/processingStatsService.ts":
/*!***************************************************************!*\
  !*** ./src/services/stats/services/processingStatsService.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProcessingStatsService: () => (/* binding */ ProcessingStatsService)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Processing Stats Service\n * Handles document processing time analytics and performance metrics\n */ \n\nclass ProcessingStatsService {\n    /**\n   * Get comprehensive processing time statistics\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @returns Processing time statistics\n   */ static async getProcessingTimeStats(startDate, endDate) {\n        try {\n            // Build query for documents with journey data\n            let query = {\n                journey: {\n                    $exists: true,\n                    $ne: []\n                }\n            };\n            if (startDate && endDate) {\n                query.createdAt = {\n                    $gte: startDate,\n                    $lte: endDate\n                };\n            }\n            // Get documents with journey data\n            const documents = await _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsWithJourney(query);\n            // Calculate processing times\n            const averageInboxTime = await this.calculateAverageProcessingTime(documents, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING);\n            const averagePendingTime = await this.calculateAverageProcessingTime(documents, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED);\n            const averageTotalTime = await this.calculateTotalProcessingTime(documents);\n            // Get processing times by category\n            const processingTimesByCategory = await this.getProcessingTimesByCategory(documents);\n            // Get processing times by division\n            const processingTimesByDivision = await this.getProcessingTimesByDivision(documents);\n            return {\n                averageInboxTime,\n                averagePendingTime,\n                averageTotalTime,\n                processingTimesByCategory,\n                processingTimesByDivision\n            };\n        } catch (error) {\n            console.error(\"Error getting processing time stats:\", error);\n            throw new Error(\"Failed to retrieve processing time statistics\");\n        }\n    }\n    /**\n   * Calculate average processing time between two statuses\n   * @param documents Documents with journey data\n   * @param fromStatus Starting status\n   * @param toStatus Ending status\n   * @returns Average processing time in hours\n   */ static async calculateAverageProcessingTime(documents, fromStatus, toStatus) {\n        const processingTimes = [];\n        documents.forEach((doc)=>{\n            if (!doc.journey || doc.journey.length === 0) return;\n            const fromEntry = doc.journey.find((entry)=>entry.status === fromStatus);\n            const toEntry = doc.journey.find((entry)=>entry.status === toStatus);\n            if (fromEntry && toEntry && fromEntry.timestamp && toEntry.timestamp) {\n                const timeDiff = new Date(toEntry.timestamp).getTime() - new Date(fromEntry.timestamp).getTime();\n                const hours = timeDiff / (1000 * 60 * 60); // Convert to hours\n                if (hours >= 0) {\n                    processingTimes.push(hours);\n                }\n            }\n        });\n        if (processingTimes.length === 0) return 0;\n        const average = processingTimes.reduce((sum, time)=>sum + time, 0) / processingTimes.length;\n        return Math.round(average * 100) / 100; // Round to 2 decimal places\n    }\n    /**\n   * Calculate total processing time from creation to completion\n   * @param documents Documents with journey data\n   * @returns Average total processing time in hours\n   */ static async calculateTotalProcessingTime(documents) {\n        const totalTimes = [];\n        documents.forEach((doc)=>{\n            if (!doc.journey || doc.journey.length === 0) return;\n            // Find first and last entries in journey\n            const sortedJourney = doc.journey.sort((a, b)=>new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());\n            const firstEntry = sortedJourney[0];\n            const lastEntry = sortedJourney[sortedJourney.length - 1];\n            if (firstEntry && lastEntry && firstEntry.timestamp && lastEntry.timestamp) {\n                const timeDiff = new Date(lastEntry.timestamp).getTime() - new Date(firstEntry.timestamp).getTime();\n                const hours = timeDiff / (1000 * 60 * 60);\n                if (hours >= 0) {\n                    totalTimes.push(hours);\n                }\n            }\n        });\n        if (totalTimes.length === 0) return 0;\n        const average = totalTimes.reduce((sum, time)=>sum + time, 0) / totalTimes.length;\n        return Math.round(average * 100) / 100;\n    }\n    /**\n   * Get processing times grouped by category\n   * @param documents Documents with journey data\n   * @returns Processing times by category\n   */ static async getProcessingTimesByCategory(documents) {\n        const categoryGroups = {};\n        // Group documents by category\n        documents.forEach((doc)=>{\n            const category = doc.category || \"Unknown\";\n            if (!categoryGroups[category]) {\n                categoryGroups[category] = [];\n            }\n            categoryGroups[category].push(doc);\n        });\n        // Calculate processing times for each category\n        const results = await Promise.all(Object.entries(categoryGroups).map(async ([category, docs])=>{\n            const avgInboxTime = await this.calculateAverageProcessingTime(docs, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING);\n            const avgPendingTime = await this.calculateAverageProcessingTime(docs, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED);\n            const avgTotalTime = await this.calculateTotalProcessingTime(docs);\n            return {\n                category,\n                avgInboxTime,\n                avgPendingTime,\n                avgTotalTime\n            };\n        }));\n        return results.sort((a, b)=>b.avgTotalTime - a.avgTotalTime);\n    }\n    /**\n   * Get processing times grouped by division\n   * @param documents Documents with journey data\n   * @returns Processing times by division\n   */ static async getProcessingTimesByDivision(documents) {\n        const divisionGroups = {};\n        // Group documents by current location (division)\n        documents.forEach((doc)=>{\n            const division = doc.currentLocation || \"Unknown\";\n            if (!divisionGroups[division]) {\n                divisionGroups[division] = [];\n            }\n            divisionGroups[division].push(doc);\n        });\n        // Calculate processing times for each division\n        const results = await Promise.all(Object.entries(divisionGroups).map(async ([division, docs])=>{\n            const avgInboxTime = await this.calculateAverageProcessingTime(docs, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING);\n            const avgPendingTime = await this.calculateAverageProcessingTime(docs, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING, _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED);\n            const avgTotalTime = await this.calculateTotalProcessingTime(docs);\n            return {\n                division,\n                avgInboxTime,\n                avgPendingTime,\n                avgTotalTime\n            };\n        }));\n        return results.sort((a, b)=>b.avgTotalTime - a.avgTotalTime);\n    }\n    /**\n   * Get processing efficiency metrics\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @returns Processing efficiency analysis\n   */ static async getProcessingEfficiency(startDate, endDate) {\n        try {\n            const stats = await this.getProcessingTimeStats(startDate, endDate);\n            // Calculate overall efficiency (lower average time = higher efficiency)\n            const maxExpectedTime = 72; // 72 hours (3 days) as benchmark\n            const efficiency = Math.max(0, Math.min(100, (maxExpectedTime - stats.averageTotalTime) / maxExpectedTime * 100));\n            // Identify bottlenecks\n            const bottlenecks = [];\n            if (stats.averageInboxTime > 24) {\n                bottlenecks.push(\"Inbox processing takes too long\");\n            }\n            if (stats.averagePendingTime > 48) {\n                bottlenecks.push(\"Pending status duration is excessive\");\n            }\n            // Generate recommendations\n            const recommendations = [];\n            if (efficiency < 50) {\n                recommendations.push(\"Consider process automation\");\n                recommendations.push(\"Review workflow bottlenecks\");\n            } else if (efficiency < 75) {\n                recommendations.push(\"Optimize pending document handling\");\n                recommendations.push(\"Implement priority queues\");\n            } else {\n                recommendations.push(\"Maintain current efficiency levels\");\n                recommendations.push(\"Consider best practice sharing\");\n            }\n            // Find fastest and slowest categories\n            const sortedCategories = stats.processingTimesByCategory.sort((a, b)=>a.avgTotalTime - b.avgTotalTime);\n            const fastestCategory = sortedCategories[0]?.category || \"N/A\";\n            const slowestCategory = sortedCategories[sortedCategories.length - 1]?.category || \"N/A\";\n            return {\n                efficiency: Math.round(efficiency * 100) / 100,\n                bottlenecks,\n                recommendations,\n                fastestCategory,\n                slowestCategory\n            };\n        } catch (error) {\n            console.error(\"Error getting processing efficiency:\", error);\n            throw new Error(\"Failed to retrieve processing efficiency metrics\");\n        }\n    }\n    /**\n   * Get processing time trends\n   * @param days Number of days to analyze\n   * @returns Processing time trend analysis\n   */ static async getProcessingTimeTrends(days = 30) {\n        try {\n            const now = new Date();\n            const periodStart = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);\n            const previousPeriodStart = new Date(now.getTime() - days * 2 * 24 * 60 * 60 * 1000);\n            // Get current and previous period stats\n            const [currentStats, previousStats] = await Promise.all([\n                this.getProcessingTimeStats(periodStart, now),\n                this.getProcessingTimeStats(previousPeriodStart, periodStart)\n            ]);\n            const currentAverage = currentStats.averageTotalTime;\n            const previousAverage = previousStats.averageTotalTime;\n            // Calculate change percentage (negative means improvement)\n            const changePercentage = previousAverage > 0 ? (currentAverage - previousAverage) / previousAverage * 100 : 0;\n            // Determine trend\n            let trend = \"stable\";\n            if (changePercentage < -10) trend = \"improving\";\n            else if (changePercentage > 10) trend = \"declining\";\n            return {\n                currentAverage: Math.round(currentAverage * 100) / 100,\n                previousAverage: Math.round(previousAverage * 100) / 100,\n                trend,\n                changePercentage: Math.round(changePercentage * 100) / 100\n            };\n        } catch (error) {\n            console.error(\"Error getting processing time trends:\", error);\n            throw new Error(\"Failed to retrieve processing time trends\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/processingStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/reportStatsService.ts":
/*!***********************************************************!*\
  !*** ./src/services/stats/services/reportStatsService.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReportStatsService: () => (/* binding */ ReportStatsService)\n/* harmony export */ });\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Report Stats Service\n * Handles volume reporting with advanced filtering and analytics\n */ \n\nclass ReportStatsService {\n    /**\n   * Get comprehensive volume report with filters\n   * @param filters Report filters and parameters\n   * @returns Volume report statistics\n   */ static async getVolumeReport(filters) {\n        try {\n            const { startDate, endDate, division, category, userId, groupBy = \"day\", userRole, userDivision, currentUserId } = filters;\n            // Build base query\n            let query = this.buildReportQuery(filters);\n            // Apply user context restrictions\n            const userContext = {\n                userId: currentUserId,\n                userRole,\n                userDivision\n            };\n            if (userRole !== \"REGIONAL_DIRECTOR\") {\n                const contextQuery = _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.buildUserContextQuery(userContext);\n                query = {\n                    $and: [\n                        query,\n                        contextQuery\n                    ]\n                };\n            }\n            // Get all report data in parallel\n            const [totalDocuments, byStatus, byDivision, byCategory, timeSeries, topUsers] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTotalUniqueDocuments(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByStatus(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByDivision(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getDocumentsByCategory(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTimeSeriesData(query, groupBy),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_0__.StatsClient.getTopUsers(query, 10)\n            ]);\n            return {\n                totalDocuments,\n                totalPending: byStatus[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PENDING] || 0,\n                totalProcessed: byStatus[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.PROCESSED] || 0,\n                byStatus,\n                byDivision,\n                byCategory,\n                timeSeries,\n                topUsers\n            };\n        } catch (error) {\n            console.error(\"Error getting volume report:\", error);\n            throw new Error(\"Failed to generate volume report\");\n        }\n    }\n    /**\n   * Build query based on report filters\n   * @param filters Report filters\n   * @returns MongoDB query object\n   */ static buildReportQuery(filters) {\n        const { startDate, endDate, division, category, userId } = filters;\n        let query = {};\n        // Date range filter\n        if (startDate && endDate) {\n            query.createdAt = {\n                $gte: new Date(startDate),\n                $lte: new Date(endDate)\n            };\n        }\n        // Division filter\n        if (division && division !== \"all\") {\n            query.currentLocation = division;\n        }\n        // Category filter\n        if (category && category !== \"all\") {\n            query.category = category;\n        }\n        // User filter\n        if (userId && userId !== \"all\") {\n            query.$or = [\n                {\n                    createdBy: userId\n                },\n                {\n                    recipientId: userId\n                }\n            ];\n        }\n        return query;\n    }\n    /**\n   * Get detailed report with advanced analytics\n   * @param filters Report filters\n   * @returns Detailed report with insights\n   */ static async getDetailedReport(filters) {\n        try {\n            const summary = await this.getVolumeReport(filters);\n            // Generate insights\n            const insights = await this.generateReportInsights(summary, filters);\n            // Analyze performance\n            const performance = await this.analyzeReportPerformance(summary, filters);\n            return {\n                summary,\n                insights,\n                performance\n            };\n        } catch (error) {\n            console.error(\"Error getting detailed report:\", error);\n            throw new Error(\"Failed to generate detailed report\");\n        }\n    }\n    /**\n   * Generate insights from report data\n   * @param reportData Report statistics\n   * @param filters Original filters\n   * @returns Report insights\n   */ static async generateReportInsights(reportData, filters) {\n        const trends = [];\n        const anomalies = [];\n        const recommendations = [];\n        // Analyze time series for trends\n        const timeSeriesEntries = Object.entries(reportData.timeSeries);\n        if (timeSeriesEntries.length > 1) {\n            const firstPeriod = timeSeriesEntries[0];\n            const lastPeriod = timeSeriesEntries[timeSeriesEntries.length - 1];\n            const firstTotal = Object.values(firstPeriod[1]).reduce((sum, count)=>sum + (count || 0), 0);\n            const lastTotal = Object.values(lastPeriod[1]).reduce((sum, count)=>sum + (count || 0), 0);\n            if (lastTotal > firstTotal * 1.2) {\n                trends.push(\"Document volume is increasing significantly\");\n            } else if (lastTotal < firstTotal * 0.8) {\n                trends.push(\"Document volume is decreasing\");\n            } else {\n                trends.push(\"Document volume is stable\");\n            }\n        }\n        // Analyze status distribution\n        const pendingRatio = reportData.totalDocuments > 0 ? reportData.totalPending / reportData.totalDocuments : 0;\n        if (pendingRatio > 0.3) {\n            anomalies.push(\"High percentage of pending documents detected\");\n            recommendations.push(\"Review pending document processing workflow\");\n        }\n        // Analyze division distribution\n        const divisionEntries = Object.entries(reportData.byDivision);\n        const maxDivisionVolume = Math.max(...divisionEntries.map(([, count])=>count));\n        const minDivisionVolume = Math.min(...divisionEntries.map(([, count])=>count));\n        if (maxDivisionVolume > minDivisionVolume * 3) {\n            anomalies.push(\"Significant workload imbalance between divisions\");\n            recommendations.push(\"Consider redistributing workload across divisions\");\n        }\n        // Analyze top users\n        if (reportData.topUsers.length > 0) {\n            const topUser = reportData.topUsers[0];\n            const averageUserVolume = reportData.totalDocuments / reportData.topUsers.length;\n            if (topUser.count > averageUserVolume * 2) {\n                anomalies.push(`User ${topUser.name} has significantly higher document volume`);\n                recommendations.push(\"Monitor high-volume users for potential burnout\");\n            }\n        }\n        // General recommendations\n        if (reportData.totalDocuments === 0) {\n            recommendations.push(\"No documents found for the selected criteria\");\n        } else if (reportData.totalDocuments < 10) {\n            recommendations.push(\"Low document volume - consider expanding date range\");\n        }\n        return {\n            trends,\n            anomalies,\n            recommendations\n        };\n    }\n    /**\n   * Analyze performance metrics from report data\n   * @param reportData Report statistics\n   * @param filters Original filters\n   * @returns Performance analysis\n   */ static async analyzeReportPerformance(reportData, filters) {\n        const bottlenecks = [];\n        const topPerformers = [];\n        // Calculate efficiency based on processed vs pending ratio\n        const processedRatio = reportData.totalDocuments > 0 ? reportData.totalProcessed / reportData.totalDocuments : 0;\n        const efficiency = Math.round(processedRatio * 100);\n        // Identify bottlenecks\n        if (reportData.totalPending > reportData.totalProcessed) {\n            bottlenecks.push(\"More documents pending than processed\");\n        }\n        const inboxCount = reportData.byStatus[_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX] || 0;\n        if (inboxCount > reportData.totalDocuments * 0.2) {\n            bottlenecks.push(\"High number of documents in inbox\");\n        }\n        // Identify top performers\n        reportData.topUsers.slice(0, 3).forEach((user)=>{\n            topPerformers.push(`${user.name} (${user.division}): ${user.count} documents`);\n        });\n        return {\n            efficiency,\n            bottlenecks,\n            topPerformers\n        };\n    }\n    /**\n   * Export report data in various formats\n   * @param filters Report filters\n   * @param format Export format\n   * @returns Formatted report data\n   */ static async exportReport(filters, format = \"json\") {\n        try {\n            const reportData = await this.getVolumeReport(filters);\n            switch(format){\n                case \"csv\":\n                    return this.formatReportAsCSV(reportData);\n                case \"summary\":\n                    return this.formatReportAsSummary(reportData);\n                default:\n                    return reportData;\n            }\n        } catch (error) {\n            console.error(\"Error exporting report:\", error);\n            throw new Error(\"Failed to export report\");\n        }\n    }\n    /**\n   * Format report data as CSV\n   * @param reportData Report statistics\n   * @returns CSV formatted string\n   */ static formatReportAsCSV(reportData) {\n        const headers = [\n            \"Metric\",\n            \"Value\"\n        ];\n        const rows = [\n            [\n                \"Total Documents\",\n                reportData.totalDocuments.toString()\n            ],\n            [\n                \"Total Pending\",\n                reportData.totalPending.toString()\n            ],\n            [\n                \"Total Processed\",\n                reportData.totalProcessed.toString()\n            ],\n            ...Object.entries(reportData.byStatus).map(([status, count])=>[\n                    status,\n                    count.toString()\n                ]),\n            ...Object.entries(reportData.byDivision).map(([division, count])=>[\n                    division,\n                    count.toString()\n                ]),\n            ...Object.entries(reportData.byCategory).map(([category, count])=>[\n                    category,\n                    count.toString()\n                ])\n        ];\n        return [\n            headers,\n            ...rows\n        ].map((row)=>row.join(\",\")).join(\"\\n\");\n    }\n    /**\n   * Format report data as summary\n   * @param reportData Report statistics\n   * @returns Summary object\n   */ static formatReportAsSummary(reportData) {\n        return {\n            overview: {\n                totalDocuments: reportData.totalDocuments,\n                pendingDocuments: reportData.totalPending,\n                processedDocuments: reportData.totalProcessed,\n                completionRate: reportData.totalDocuments > 0 ? Math.round(reportData.totalProcessed / reportData.totalDocuments * 100) : 0\n            },\n            topDivision: Object.entries(reportData.byDivision).reduce((max, current)=>current[1] > max[1] ? current : max, [\n                \"\",\n                0\n            ]),\n            topCategory: Object.entries(reportData.byCategory).reduce((max, current)=>current[1] > max[1] ? current : max, [\n                \"\",\n                0\n            ]),\n            topUser: reportData.topUsers[0] || null\n        };\n    }\n    /**\n   * Get report comparison between two periods\n   * @param currentFilters Current period filters\n   * @param previousFilters Previous period filters\n   * @returns Comparison analysis\n   */ static async getReportComparison(currentFilters, previousFilters) {\n        try {\n            const [current, previous] = await Promise.all([\n                this.getVolumeReport(currentFilters),\n                this.getVolumeReport(previousFilters)\n            ]);\n            const volumeChange = previous.totalDocuments > 0 ? (current.totalDocuments - previous.totalDocuments) / previous.totalDocuments * 100 : 0;\n            const pendingChange = previous.totalPending > 0 ? (current.totalPending - previous.totalPending) / previous.totalPending * 100 : 0;\n            const processedChange = previous.totalProcessed > 0 ? (current.totalProcessed - previous.totalProcessed) / previous.totalProcessed * 100 : 0;\n            // Determine overall trend\n            let trend = \"stable\";\n            if (processedChange > 10 && pendingChange < -10) trend = \"improving\";\n            else if (processedChange < -10 || pendingChange > 10) trend = \"declining\";\n            return {\n                current,\n                previous,\n                comparison: {\n                    volumeChange: Math.round(volumeChange * 100) / 100,\n                    pendingChange: Math.round(pendingChange * 100) / 100,\n                    processedChange: Math.round(processedChange * 100) / 100,\n                    trend\n                }\n            };\n        } catch (error) {\n            console.error(\"Error getting report comparison:\", error);\n            throw new Error(\"Failed to generate report comparison\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/reportStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/services/volumeStatsService.ts":
/*!***********************************************************!*\
  !*** ./src/services/stats/services/volumeStatsService.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VolumeStatsService: () => (/* binding */ VolumeStatsService)\n/* harmony export */ });\n/* harmony import */ var _models_Document__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/Document */ \"(rsc)/./src/models/Document.ts\");\n/* harmony import */ var _core_statsClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/statsClient */ \"(rsc)/./src/services/stats/core/statsClient.ts\");\n/**\n * Volume Stats Service\n * Handles document volume analytics and trends\n */ \n\nclass VolumeStatsService {\n    /**\n   * Get comprehensive document volume statistics\n   * @param startDate Start date for filtering\n   * @param endDate End date for filtering\n   * @param division Optional division filter\n   * @returns Document volume statistics\n   */ static async getDocumentVolumeStats(startDate, endDate, division) {\n        try {\n            // Build base query\n            let query = {};\n            if (startDate && endDate) {\n                query.createdAt = {\n                    $gte: startDate,\n                    $lte: endDate\n                };\n            }\n            if (division) {\n                query.currentLocation = division;\n            }\n            // Get all volume statistics in parallel\n            const [totalVolume, volumeByPeriod, volumeByDivision, volumeByCategory] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getTotalUniqueDocuments(query),\n                this.getVolumeByPeriod(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByDivision(query),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByCategory(query)\n            ]);\n            return {\n                totalVolume,\n                volumeByPeriod,\n                volumeByDivision,\n                volumeByCategory\n            };\n        } catch (error) {\n            console.error(\"Error getting document volume stats:\", error);\n            throw new Error(\"Failed to retrieve document volume statistics\");\n        }\n    }\n    /**\n   * Get volume by time period (daily for last 30 days)\n   * @param query Base query filter\n   * @returns Volume data by period\n   */ static async getVolumeByPeriod(query) {\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        const volumeByPeriod = await _models_Document__WEBPACK_IMPORTED_MODULE_0__[\"default\"].aggregate([\n            {\n                $match: {\n                    ...query,\n                    createdAt: {\n                        $gte: thirtyDaysAgo\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: {\n                        trackingNumber: \"$trackingNumber\",\n                        date: {\n                            $dateToString: {\n                                format: \"%Y-%m-%d\",\n                                date: \"$createdAt\"\n                            }\n                        }\n                    },\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: \"$_id.date\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    _id: 1\n                }\n            },\n            {\n                $project: {\n                    period: \"$_id\",\n                    count: 1,\n                    _id: 0\n                }\n            }\n        ]);\n        return volumeByPeriod;\n    }\n    /**\n   * Get volume trends and growth analysis\n   * @param dateRange Date range for analysis\n   * @returns Volume trend analysis\n   */ static async getVolumeTrends(dateRange) {\n        try {\n            const now = new Date();\n            const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n            const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);\n            // Get current period (last 30 days) and previous period (30-60 days ago)\n            const [currentPeriod, previousPeriod] = await Promise.all([\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getTotalUniqueDocuments({\n                    createdAt: {\n                        $gte: thirtyDaysAgo,\n                        $lte: now\n                    }\n                }),\n                _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getTotalUniqueDocuments({\n                    createdAt: {\n                        $gte: sixtyDaysAgo,\n                        $lt: thirtyDaysAgo\n                    }\n                })\n            ]);\n            // Calculate growth rate\n            const growthRate = previousPeriod > 0 ? (currentPeriod - previousPeriod) / previousPeriod * 100 : 0;\n            // Determine trend\n            let trend = \"stable\";\n            if (growthRate > 5) trend = \"increasing\";\n            else if (growthRate < -5) trend = \"decreasing\";\n            // Simple forecast (next 30 days based on current trend)\n            const forecast = Math.max(0, Math.round(currentPeriod * (1 + growthRate / 100)));\n            return {\n                currentPeriod,\n                previousPeriod,\n                growthRate: Math.round(growthRate * 100) / 100,\n                trend,\n                forecast\n            };\n        } catch (error) {\n            console.error(\"Error getting volume trends:\", error);\n            throw new Error(\"Failed to retrieve volume trends\");\n        }\n    }\n    /**\n   * Get peak volume analysis\n   * @param days Number of days to analyze\n   * @returns Peak volume analysis\n   */ static async getPeakVolumeAnalysis(days = 30) {\n        try {\n            const startDate = new Date();\n            startDate.setDate(startDate.getDate() - days);\n            const dailyVolumes = await this.getVolumeByPeriod({\n                createdAt: {\n                    $gte: startDate\n                }\n            });\n            if (dailyVolumes.length === 0) {\n                return {\n                    peakDay: \"N/A\",\n                    peakVolume: 0,\n                    averageVolume: 0,\n                    lowDay: \"N/A\",\n                    lowVolume: 0,\n                    recommendations: [\n                        \"No data available for the specified period\"\n                    ]\n                };\n            }\n            // Find peak and low days\n            const peakDay = dailyVolumes.reduce((max, current)=>current.count > max.count ? current : max);\n            const lowDay = dailyVolumes.reduce((min, current)=>current.count < min.count ? current : min);\n            const averageVolume = dailyVolumes.reduce((sum, day)=>sum + day.count, 0) / dailyVolumes.length;\n            // Generate recommendations\n            const recommendations = [];\n            if (peakDay.count > averageVolume * 2) {\n                recommendations.push(\"Consider load balancing during peak days\");\n                recommendations.push(\"Prepare additional resources for high-volume periods\");\n            }\n            if (lowDay.count < averageVolume * 0.5) {\n                recommendations.push(\"Investigate reasons for low-volume days\");\n                recommendations.push(\"Consider redistributing workload from peak days\");\n            }\n            if (recommendations.length === 0) {\n                recommendations.push(\"Volume distribution appears balanced\");\n            }\n            return {\n                peakDay: peakDay.period,\n                peakVolume: peakDay.count,\n                averageVolume: Math.round(averageVolume * 100) / 100,\n                lowDay: lowDay.period,\n                lowVolume: lowDay.count,\n                recommendations\n            };\n        } catch (error) {\n            console.error(\"Error getting peak volume analysis:\", error);\n            throw new Error(\"Failed to retrieve peak volume analysis\");\n        }\n    }\n    /**\n   * Get division volume comparison\n   * @param dateRange Optional date range filter\n   * @returns Division volume comparison\n   */ static async getDivisionVolumeComparison(dateRange) {\n        try {\n            let query = {};\n            if (dateRange?.startDate && dateRange?.endDate) {\n                query.createdAt = {\n                    $gte: dateRange.startDate,\n                    $lte: dateRange.endDate\n                };\n            }\n            const volumeByDivision = await _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByDivision(query);\n            const totalVolume = Object.values(volumeByDivision).reduce((sum, count)=>sum + count, 0);\n            const averageVolume = totalVolume / Object.keys(volumeByDivision).length;\n            const comparison = Object.entries(volumeByDivision).map(([division, volume])=>({\n                    division,\n                    volume,\n                    percentage: totalVolume > 0 ? Math.round(volume / totalVolume * 10000) / 100 : 0,\n                    rank: 0,\n                    status: volume > averageVolume * 1.1 ? \"above_average\" : volume < averageVolume * 0.9 ? \"below_average\" : \"average\"\n                })).sort((a, b)=>b.volume - a.volume).map((item, index)=>({\n                    ...item,\n                    rank: index + 1\n                }));\n            return comparison;\n        } catch (error) {\n            console.error(\"Error getting division volume comparison:\", error);\n            throw new Error(\"Failed to retrieve division volume comparison\");\n        }\n    }\n    /**\n   * Get category volume distribution\n   * @param division Optional division filter\n   * @param dateRange Optional date range filter\n   * @returns Category volume distribution\n   */ static async getCategoryVolumeDistribution(division, dateRange) {\n        try {\n            let query = {};\n            if (division) {\n                query.currentLocation = division;\n            }\n            if (dateRange?.startDate && dateRange?.endDate) {\n                query.createdAt = {\n                    $gte: dateRange.startDate,\n                    $lte: dateRange.endDate\n                };\n            }\n            const volumeByCategory = await _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByCategory(query);\n            const totalVolume = Object.values(volumeByCategory).reduce((sum, count)=>sum + count, 0);\n            // Get previous period for trend analysis\n            const previousQuery = {\n                ...query\n            };\n            if (dateRange?.startDate && dateRange?.endDate) {\n                const periodLength = dateRange.endDate.getTime() - dateRange.startDate.getTime();\n                const previousStart = new Date(dateRange.startDate.getTime() - periodLength);\n                const previousEnd = new Date(dateRange.endDate.getTime() - periodLength);\n                previousQuery.createdAt = {\n                    $gte: previousStart,\n                    $lte: previousEnd\n                };\n            }\n            const previousVolumeByCategory = await _core_statsClient__WEBPACK_IMPORTED_MODULE_1__.StatsClient.getDocumentsByCategory(previousQuery);\n            const distribution = Object.entries(volumeByCategory).map(([category, volume])=>{\n                const percentage = totalVolume > 0 ? Math.round(volume / totalVolume * 10000) / 100 : 0;\n                const previousVolume = previousVolumeByCategory[category] || 0;\n                let trend = \"stable\";\n                if (volume > previousVolume * 1.1) trend = \"up\";\n                else if (volume < previousVolume * 0.9) trend = \"down\";\n                return {\n                    category,\n                    volume,\n                    percentage,\n                    trend\n                };\n            }).sort((a, b)=>b.volume - a.volume);\n            return distribution;\n        } catch (error) {\n            console.error(\"Error getting category volume distribution:\", error);\n            throw new Error(\"Failed to retrieve category volume distribution\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/services/volumeStatsService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/stats/utils/aggregationHelpers.ts":
/*!********************************************************!*\
  !*** ./src/services/stats/utils/aggregationHelpers.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AggregationHelpers: () => (/* binding */ AggregationHelpers)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/**\n * Aggregation Helpers\n * Common MongoDB aggregation pipelines and utilities for statistics\n */ \nclass AggregationHelpers {\n    /**\n   * Create aggregation pipeline for document deduplication by tracking number\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createDeduplicationPipeline(additionalMatch = {}) {\n        return [\n            {\n                $match: additionalMatch\n            },\n            {\n                $group: {\n                    _id: \"$trackingNumber\",\n                    doc: {\n                        $first: \"$$ROOT\"\n                    }\n                }\n            },\n            {\n                $replaceRoot: {\n                    newRoot: \"$doc\"\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for grouping by division\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createDivisionGroupingPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: \"$currentLocation\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for grouping by status\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createStatusGroupingPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: \"$status\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for grouping by category\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createCategoryGroupingPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: \"$category\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for time series data\n   * @param additionalMatch Additional match conditions\n   * @param groupBy Time grouping ('day', 'week', 'month')\n   * @returns Aggregation pipeline\n   */ static createTimeSeriesPipeline(additionalMatch = {}, groupBy = \"day\") {\n        let dateFormat;\n        switch(groupBy){\n            case \"week\":\n                dateFormat = \"%Y-W%U\";\n                break;\n            case \"month\":\n                dateFormat = \"%Y-%m\";\n                break;\n            default:\n                dateFormat = \"%Y-%m-%d\";\n        }\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: {\n                        date: {\n                            $dateToString: {\n                                format: dateFormat,\n                                date: \"$createdAt\"\n                            }\n                        },\n                        status: \"$status\"\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    \"_id.date\": 1\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for top users\n   * @param additionalMatch Additional match conditions\n   * @param limit Number of top users to return\n   * @returns Aggregation pipeline\n   */ static createTopUsersPipeline(additionalMatch = {}, limit = 10) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: \"$createdBy\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    count: -1\n                }\n            },\n            {\n                $limit: limit\n            },\n            {\n                $lookup: {\n                    from: \"users\",\n                    localField: \"_id\",\n                    foreignField: \"_id\",\n                    as: \"userInfo\"\n                }\n            },\n            {\n                $project: {\n                    userId: {\n                        $toString: \"$_id\"\n                    },\n                    count: 1,\n                    name: {\n                        $arrayElemAt: [\n                            \"$userInfo.name\",\n                            0\n                        ]\n                    },\n                    division: {\n                        $arrayElemAt: [\n                            \"$userInfo.division\",\n                            0\n                        ]\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for user statistics\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createUserStatsPipeline(additionalMatch = {}) {\n        return [\n            {\n                $match: additionalMatch\n            },\n            {\n                $group: {\n                    _id: \"$division\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create match query for user context\n   * @param userId User ID\n   * @param userRole User role\n   * @param userDivision User division\n   * @returns Match query object\n   */ static createUserContextMatch(userId, userRole, userDivision) {\n        if (userRole === \"REGIONAL_DIRECTOR\") {\n            return {}; // Regional Director can see all documents\n        } else if (userRole === \"DIVISION_CHIEF\") {\n            return {\n                $or: [\n                    {\n                        currentLocation: userDivision\n                    },\n                    {\n                        recipientId: userId\n                    }\n                ]\n            };\n        } else {\n            return {\n                $or: [\n                    {\n                        createdBy: userId\n                    },\n                    {\n                        recipientId: userId\n                    }\n                ]\n            };\n        }\n    }\n    /**\n   * Create date range match query\n   * @param startDate Start date\n   * @param endDate End date\n   * @param dateField Field to apply date filter (default: 'createdAt')\n   * @returns Date range match query\n   */ static createDateRangeMatch(startDate, endDate, dateField = \"createdAt\") {\n        if (!startDate && !endDate) return {};\n        const dateQuery = {};\n        if (startDate && endDate) {\n            dateQuery[dateField] = {\n                $gte: startDate,\n                $lte: endDate\n            };\n        } else if (startDate) {\n            dateQuery[dateField] = {\n                $gte: startDate\n            };\n        } else if (endDate) {\n            dateQuery[dateField] = {\n                $lte: endDate\n            };\n        }\n        return dateQuery;\n    }\n    /**\n   * Create aggregation pipeline for processing time analysis\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createProcessingTimePipeline(additionalMatch = {}) {\n        return [\n            {\n                $match: {\n                    ...additionalMatch,\n                    journey: {\n                        $exists: true,\n                        $ne: []\n                    }\n                }\n            },\n            {\n                $addFields: {\n                    journeyArray: {\n                        $map: {\n                            input: \"$journey\",\n                            as: \"entry\",\n                            in: {\n                                status: \"$$entry.status\",\n                                timestamp: {\n                                    $toDate: \"$$entry.timestamp\"\n                                }\n                            }\n                        }\n                    }\n                }\n            },\n            {\n                $addFields: {\n                    sortedJourney: {\n                        $sortArray: {\n                            input: \"$journeyArray\",\n                            sortBy: {\n                                timestamp: 1\n                            }\n                        }\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for volume trends\n   * @param days Number of days to analyze\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createVolumeTrendsPipeline(days = 30, additionalMatch = {}) {\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        return [\n            {\n                $match: {\n                    ...additionalMatch,\n                    createdAt: {\n                        $gte: startDate\n                    }\n                }\n            },\n            ...this.createDeduplicationPipeline(),\n            {\n                $group: {\n                    _id: {\n                        $dateToString: {\n                            format: \"%Y-%m-%d\",\n                            date: \"$createdAt\"\n                        }\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    _id: 1\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for efficiency metrics\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createEfficiencyMetricsPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $group: {\n                    _id: null,\n                    totalDocuments: {\n                        $sum: 1\n                    },\n                    pendingCount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        \"$status\",\n                                        _types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.PENDING\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    processedCount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        \"$status\",\n                                        _types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.PROCESSED\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    },\n                    archivedCount: {\n                        $sum: {\n                            $cond: [\n                                {\n                                    $eq: [\n                                        \"$status\",\n                                        _types__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.ARCHIVED\n                                    ]\n                                },\n                                1,\n                                0\n                            ]\n                        }\n                    }\n                }\n            },\n            {\n                $addFields: {\n                    completionRate: {\n                        $cond: [\n                            {\n                                $gt: [\n                                    \"$totalDocuments\",\n                                    0\n                                ]\n                            },\n                            {\n                                $multiply: [\n                                    {\n                                        $divide: [\n                                            {\n                                                $add: [\n                                                    \"$processedCount\",\n                                                    \"$archivedCount\"\n                                                ]\n                                            },\n                                            \"$totalDocuments\"\n                                        ]\n                                    },\n                                    100\n                                ]\n                            },\n                            0\n                        ]\n                    }\n                }\n            }\n        ];\n    }\n    /**\n   * Create aggregation pipeline for workload distribution\n   * @param additionalMatch Additional match conditions\n   * @returns Aggregation pipeline\n   */ static createWorkloadDistributionPipeline(additionalMatch = {}) {\n        return [\n            ...this.createDeduplicationPipeline(additionalMatch),\n            {\n                $facet: {\n                    byStatus: [\n                        {\n                            $group: {\n                                _id: \"$status\",\n                                count: {\n                                    $sum: 1\n                                }\n                            }\n                        }\n                    ],\n                    byDivision: [\n                        {\n                            $group: {\n                                _id: \"$currentLocation\",\n                                count: {\n                                    $sum: 1\n                                }\n                            }\n                        }\n                    ],\n                    byCategory: [\n                        {\n                            $group: {\n                                _id: \"$category\",\n                                count: {\n                                    $sum: 1\n                                }\n                            }\n                        }\n                    ]\n                }\n            }\n        ];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/stats/utils/aggregationHelpers.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/audit.ts":
/*!****************************!*\
  !*** ./src/types/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditLogAction: () => (/* binding */ AuditLogAction)\n/* harmony export */ });\nvar AuditLogAction;\n(function(AuditLogAction) {\n    AuditLogAction[\"USER_CREATED\"] = \"USER_CREATED\";\n    AuditLogAction[\"USER_UPDATED\"] = \"USER_UPDATED\";\n    AuditLogAction[\"USER_DELETED\"] = \"USER_DELETED\";\n    AuditLogAction[\"USER_LOGIN\"] = \"USER_LOGIN\";\n    AuditLogAction[\"DOCUMENT_CREATED\"] = \"DOCUMENT_CREATED\";\n    AuditLogAction[\"DOCUMENT_UPDATED\"] = \"DOCUMENT_UPDATED\";\n    AuditLogAction[\"DOCUMENT_DELETED\"] = \"DOCUMENT_DELETED\";\n    AuditLogAction[\"DOCUMENT_STATUS_CHANGED\"] = \"DOCUMENT_STATUS_CHANGED\";\n    AuditLogAction[\"DOCUMENT_SHARED\"] = \"DOCUMENT_SHARED\";\n    AuditLogAction[\"DOCUMENT_FORWARDED\"] = \"DOCUMENT_FORWARDED\";\n    AuditLogAction[\"DOCUMENT_RECEIVED\"] = \"DOCUMENT_RECEIVED\";\n    AuditLogAction[\"DOCUMENT_PROCESSED\"] = \"DOCUMENT_PROCESSED\";\n    AuditLogAction[\"PROFILE_CHANGE_REQUESTED\"] = \"PROFILE_CHANGE_REQUESTED\";\n    AuditLogAction[\"PROFILE_CHANGE_APPROVED\"] = \"PROFILE_CHANGE_APPROVED\";\n    AuditLogAction[\"PROFILE_CHANGE_REJECTED\"] = \"PROFILE_CHANGE_REJECTED\";\n    AuditLogAction[\"FILE_UPLOADED\"] = \"FILE_UPLOADED\";\n    AuditLogAction[\"FILE_DOWNLOADED\"] = \"FILE_DOWNLOADED\";\n    AuditLogAction[\"FILE_DELETED\"] = \"FILE_DELETED\";\n    AuditLogAction[\"FILE_ACCESSED\"] = \"FILE_ACCESSED\";\n    AuditLogAction[\"FEEDBACK_CREATED\"] = \"FEEDBACK_CREATED\";\n    AuditLogAction[\"FEEDBACK_UPDATED\"] = \"FEEDBACK_UPDATED\";\n    AuditLogAction[\"FEEDBACK_DELETED\"] = \"FEEDBACK_DELETED\";\n    AuditLogAction[\"FEEDBACK_STATUS_CHANGED\"] = \"FEEDBACK_STATUS_CHANGED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_GENERATED\"] = \"FEEDBACK_AI_SUGGESTION_GENERATED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_REMOVED\"] = \"FEEDBACK_AI_SUGGESTION_REMOVED\";\n    AuditLogAction[\"SEARCH_PERFORMED\"] = \"SEARCH_PERFORMED\";\n    AuditLogAction[\"DATA_CLEANUP\"] = \"DATA_CLEANUP\";\n    AuditLogAction[\"SYSTEM_MAINTENANCE\"] = \"SYSTEM_MAINTENANCE\";\n    AuditLogAction[\"ARCHIVE_EXPORTED\"] = \"ARCHIVE_EXPORTED\";\n})(AuditLogAction || (AuditLogAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Division: () => (/* binding */ Division),\n/* harmony export */   DocumentAction: () => (/* binding */ DocumentAction),\n/* harmony export */   DocumentCategory: () => (/* binding */ DocumentCategory),\n/* harmony export */   DocumentStatus: () => (/* binding */ DocumentStatus),\n/* harmony export */   FeedbackCategory: () => (/* binding */ FeedbackCategory),\n/* harmony export */   FeedbackStatus: () => (/* binding */ FeedbackStatus),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\n// User Roles\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n    UserRole[\"REGIONAL_DIRECTOR\"] = \"REGIONAL_DIRECTOR\";\n    UserRole[\"DIVISION_CHIEF\"] = \"DIVISION_CHIEF\";\n    UserRole[\"EMPLOYEE\"] = \"EMPLOYEE\";\n})(UserRole || (UserRole = {}));\nvar Division;\n(function(Division) {\n    Division[\"ORD\"] = \"ORD\";\n    Division[\"FAD\"] = \"FAD\";\n    Division[\"MMD\"] = \"MMD\";\n    Division[\"MSESDD\"] = \"MSESDD\";\n    Division[\"GSD\"] = \"GSD\";\n})(Division || (Division = {}));\nvar DocumentStatus;\n(function(DocumentStatus) {\n    DocumentStatus[\"INBOX\"] = \"INBOX\";\n    DocumentStatus[\"SENT\"] = \"SENT\";\n    DocumentStatus[\"RECEIVED\"] = \"RECEIVED\";\n    DocumentStatus[\"FORWARDED\"] = \"FORWARDED\";\n    DocumentStatus[\"PENDING\"] = \"PENDING\";\n    DocumentStatus[\"PROCESSED\"] = \"PROCESSED\";\n    DocumentStatus[\"ARCHIVED\"] = \"ARCHIVED\"; // Document has been archived for storage\n})(DocumentStatus || (DocumentStatus = {}));\nvar DocumentCategory;\n(function(DocumentCategory) {\n    // Common Office Documents\n    DocumentCategory[\"MEMO\"] = \"MEMO\";\n    DocumentCategory[\"LETTER\"] = \"LETTER\";\n    DocumentCategory[\"REPORT\"] = \"REPORT\";\n    DocumentCategory[\"PROPOSAL\"] = \"PROPOSAL\";\n    DocumentCategory[\"MINUTES\"] = \"MINUTES\";\n    DocumentCategory[\"FORM\"] = \"FORM\";\n    // Official Documents\n    DocumentCategory[\"CIRCULAR\"] = \"CIRCULAR\";\n    DocumentCategory[\"ADVISORY\"] = \"ADVISORY\";\n    DocumentCategory[\"BULLETIN\"] = \"BULLETIN\";\n    DocumentCategory[\"NOTICE\"] = \"NOTICE\";\n    DocumentCategory[\"ANNOUNCEMENT\"] = \"ANNOUNCEMENT\";\n    DocumentCategory[\"RESOLUTION\"] = \"RESOLUTION\";\n    DocumentCategory[\"POLICY\"] = \"POLICY\";\n    DocumentCategory[\"GUIDELINE\"] = \"GUIDELINE\";\n    DocumentCategory[\"DIRECTIVE\"] = \"DIRECTIVE\";\n    DocumentCategory[\"MEMORANDUM_ORDER\"] = \"MEMORANDUM ORDER\";\n    DocumentCategory[\"MEMORANDUM_CIRCULAR\"] = \"MEMORANDUM CIRCULAR\";\n    DocumentCategory[\"EXECUTIVE_ORDER\"] = \"EXECUTIVE ORDER\";\n    DocumentCategory[\"ADMINISTRATIVE_ORDER\"] = \"ADMINISTRATIVE ORDER\";\n    // Legal & Financial Documents\n    DocumentCategory[\"CONTRACT\"] = \"CONTRACT\";\n    DocumentCategory[\"CERTIFICATE\"] = \"CERTIFICATE\";\n    DocumentCategory[\"ENDORSEMENT\"] = \"ENDORSEMENT\";\n    DocumentCategory[\"MANUAL\"] = \"MANUAL\";\n    DocumentCategory[\"INVOICE\"] = \"INVOICE\";\n    DocumentCategory[\"RECEIPT\"] = \"RECEIPT\";\n    DocumentCategory[\"VOUCHER\"] = \"VOUCHER\";\n    DocumentCategory[\"REQUISITION\"] = \"REQUISITION\";\n    DocumentCategory[\"PURCHASE_ORDER\"] = \"PURCHASE ORDER\";\n    DocumentCategory[\"BUDGET_REQUEST\"] = \"BUDGET REQUEST\";\n    DocumentCategory[\"TRAVEL_ORDER\"] = \"TRAVEL ORDER\";\n    DocumentCategory[\"LEAVE_FORM\"] = \"LEAVE FORM\";\n    // Other\n    DocumentCategory[\"OTHER\"] = \"OTHER\";\n})(DocumentCategory || (DocumentCategory = {}));\nvar DocumentAction;\n(function(DocumentAction) {\n    DocumentAction[\"NONE\"] = \"No specific action required\";\n    // Actions from the image (A-S)\n    DocumentAction[\"FOR_INFO\"] = \"A - For information/guidance/reference\";\n    DocumentAction[\"FOR_COMMENTS\"] = \"B - For comments/recommendations\";\n    DocumentAction[\"TAKE_UP\"] = \"C - Pls. take up with me\";\n    DocumentAction[\"DRAFT_ANSWER\"] = \"D - Pls. draft answer/memo/acknow.\";\n    DocumentAction[\"FOR_ACTION\"] = \"E - For appropriate action\";\n    DocumentAction[\"IMMEDIATE_INVESTIGATION\"] = \"F - Pls. immediate investigation\";\n    DocumentAction[\"ATTACH_SUPPORTING\"] = \"G - Pls. attach supporting papers\";\n    DocumentAction[\"FOR_APPROVAL\"] = \"H - For approval\";\n    DocumentAction[\"FOR_SIGNATURE\"] = \"I - For initial/signature\";\n    DocumentAction[\"STUDY_EVALUATE\"] = \"J - Pls. study / evaluate\";\n    DocumentAction[\"RELEASE_FILE\"] = \"K - Pls. release/file\";\n    DocumentAction[\"UPDATE_STATUS\"] = \"L - Update status of case\";\n    DocumentAction[\"FILE_CLOSE\"] = \"M - Filed / Close\";\n    DocumentAction[\"FOR_ADA\"] = \"N - For ADA / Check Preparation\";\n    DocumentAction[\"FOR_DISCUSSION\"] = \"O - FOD (For Discussion)\";\n    DocumentAction[\"FOR_REVISION\"] = \"P - For Revision\";\n    DocumentAction[\"ATTACH_DRAFT\"] = \"Q - Pls. Attach Draft File\";\n    DocumentAction[\"SAVED\"] = \"R - Saved\";\n    DocumentAction[\"FOR_SCANNING\"] = \"S - For Scanning\";\n    // Additional useful actions for office work\n    DocumentAction[\"URGENT\"] = \"URGENT - Requires immediate attention\";\n    DocumentAction[\"CONFIDENTIAL\"] = \"CONFIDENTIAL - Restricted access\";\n    DocumentAction[\"FOR_REVIEW\"] = \"FOR REVIEW - Please review and provide feedback\";\n    DocumentAction[\"FOR_COORDINATION\"] = \"FOR COORDINATION - Coordinate with relevant departments\";\n    DocumentAction[\"FOR_COMPLIANCE\"] = \"FOR COMPLIANCE - Ensure compliance with regulations\";\n    DocumentAction[\"FOR_IMPLEMENTATION\"] = \"FOR IMPLEMENTATION - Implement the described actions\";\n    DocumentAction[\"FOR_FILING\"] = \"FOR FILING - File for future reference\";\n    DocumentAction[\"FOR_DISTRIBUTION\"] = \"FOR DISTRIBUTION - Distribute to concerned parties\";\n    DocumentAction[\"FOR_ENDORSEMENT\"] = \"FOR ENDORSEMENT - Endorse to appropriate authority\";\n    DocumentAction[\"FOR_VERIFICATION\"] = \"FOR VERIFICATION - Verify information/data\";\n    DocumentAction[\"FOR_RECORDING\"] = \"FOR RECORDING - Record in the system\";\n})(DocumentAction || (DocumentAction = {}));\nvar FeedbackCategory;\n(function(FeedbackCategory) {\n    FeedbackCategory[\"BUG\"] = \"bug\";\n    FeedbackCategory[\"FEATURE\"] = \"feature\";\n    FeedbackCategory[\"IMPROVEMENT\"] = \"improvement\";\n    FeedbackCategory[\"OTHER\"] = \"other\";\n})(FeedbackCategory || (FeedbackCategory = {}));\nvar FeedbackStatus;\n(function(FeedbackStatus) {\n    FeedbackStatus[\"PENDING\"] = \"pending\";\n    FeedbackStatus[\"REVIEWED\"] = \"reviewed\";\n    FeedbackStatus[\"IMPLEMENTED\"] = \"implemented\";\n    FeedbackStatus[\"REJECTED\"] = \"rejected\";\n})(FeedbackStatus || (FeedbackStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/audit.ts":
/*!****************************!*\
  !*** ./src/utils/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logAuditEvent: () => (/* binding */ logAuditEvent)\n/* harmony export */ });\n/* harmony import */ var _models_AuditLog__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/AuditLog */ \"(rsc)/./src/models/AuditLog.ts\");\n\nasync function logAuditEvent({ action, performedBy, targetId, targetType, details, request }) {\n    try {\n        const auditLogData = {\n            action,\n            performedBy,\n            targetId,\n            targetType,\n            details\n        };\n        // Add request information if available\n        if (request) {\n            auditLogData.ipAddress = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n            auditLogData.userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        }\n        // Create the audit log entry\n        await _models_AuditLog__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create(auditLogData);\n    } catch (error) {\n        console.error(\"Error creating audit log:\", error);\n    // Don't throw the error - we don't want to break the main functionality\n    // if audit logging fails\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/cacheUtils.ts":
/*!*********************************!*\
  !*** ./src/utils/cacheUtils.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAllStatsCache: () => (/* binding */ clearAllStatsCache),\n/* harmony export */   clearDashboardCache: () => (/* binding */ clearDashboardCache),\n/* harmony export */   clearUserStatsCache: () => (/* binding */ clearUserStatsCache),\n/* harmony export */   forceDashboardRefresh: () => (/* binding */ forceDashboardRefresh),\n/* harmony export */   getUserStatsFromCache: () => (/* binding */ getUserStatsFromCache),\n/* harmony export */   refreshDashboardAfterAction: () => (/* binding */ refreshDashboardAfterAction),\n/* harmony export */   setUserStatsInCache: () => (/* binding */ setUserStatsInCache)\n/* harmony export */ });\n/**\n * Utility functions for managing cache in the application\n */ // Server-side cache for dashboard stats\nconst statsCache = new Map();\n/**\n * Function to clear the server-side cache for a specific user\n * @param userId The ID of the user whose cache should be cleared\n */ function clearUserStatsCache(userId) {\n    const cacheKey = `dashboard_stats_${userId}`;\n    statsCache.delete(cacheKey);\n    console.log(`Cleared dashboard stats cache for user: ${userId}`);\n}\n/**\n * Clear all server-side cache\n */ function clearAllStatsCache() {\n    statsCache.clear();\n    console.log(\"Cleared all dashboard stats cache\");\n}\n/**\n * Get a value from the server-side stats cache\n * @param userId The user ID to get stats for\n * @param timestamp The current timestamp\n * @param cacheDuration The cache duration in milliseconds\n * @returns The cached data or null if not found or expired\n */ function getUserStatsFromCache(userId, timestamp, cacheDuration) {\n    const cacheKey = `dashboard_stats_${userId}`;\n    const cachedData = statsCache.get(cacheKey);\n    // Return cached data if it's still valid\n    if (cachedData && timestamp - cachedData.timestamp < cacheDuration) {\n        return cachedData.data;\n    }\n    return null;\n}\n/**\n * Set a value in the server-side stats cache\n * @param userId The user ID to set stats for\n * @param data The data to cache\n * @param timestamp The current timestamp\n */ function setUserStatsInCache(userId, data, timestamp) {\n    const cacheKey = `dashboard_stats_${userId}`;\n    statsCache.set(cacheKey, {\n        data,\n        timestamp\n    });\n}\n// Client-side cache functions\n/**\n * Clear dashboard stats cache from localStorage\n */ function clearDashboardCache() {\n    try {\n        localStorage.removeItem(\"dashboard_stats_cache\");\n        localStorage.removeItem(\"dashboard_stats_expiry\");\n        console.log(\"Dashboard cache cleared from localStorage\");\n    } catch (error) {\n        console.error(\"Error clearing dashboard cache:\", error);\n    }\n}\n/**\n * Force refresh dashboard stats by clearing cache and making a new request\n */ async function forceDashboardRefresh() {\n    try {\n        // Clear local storage cache\n        clearDashboardCache();\n        // Make a direct API call to refresh server-side cache\n        const response = await fetch(\"/api/dashboard/stats?forceRefresh=true\", {\n            method: \"GET\",\n            headers: {\n                \"Cache-Control\": \"no-cache\",\n                \"Pragma\": \"no-cache\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to refresh dashboard: ${response.status}`);\n        }\n        console.log(\"Dashboard data refreshed successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"Error refreshing dashboard:\", error);\n        return false;\n    }\n}\n/**\n * Trigger a dashboard refresh after document actions\n * @param delay Optional delay in milliseconds before refreshing\n */ function refreshDashboardAfterAction(delay = 500) {\n    // Clear cache immediately\n    clearDashboardCache();\n    // Refresh after a short delay to allow server-side changes to propagate\n    setTimeout(()=>{\n        forceDashboardRefresh().then((success)=>{\n            if (success) {\n                console.log(\"Dashboard refreshed after document action\");\n            }\n        }).catch((error)=>{\n            console.error(\"Failed to refresh dashboard after action:\", error);\n        });\n    }, delay);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/cacheUtils.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/serverTimestamp.ts":
/*!**************************************!*\
  !*** ./src/utils/serverTimestamp.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerTimestamp: () => (/* binding */ getServerTimestamp)\n/* harmony export */ });\n/**\n * This utility manages the server timestamp used to invalidate sessions on server restart\n */ // Use a more stable identifier that doesn't change on every development hot reload\n// In production, this will still change on server restart\nlet SERVER_START_TIMESTAMP;\n// Try to use a timestamp that persists across hot reloads in development\nif (true) {\n    // In development, use a timestamp that changes daily instead of on every restart\n    // This prevents constant logouts during development\n    const today = new Date();\n    const dateString = `${today.getFullYear()}-${today.getMonth()}-${today.getDate()}`;\n    SERVER_START_TIMESTAMP = dateString;\n} else {}\n// Function to get the current server timestamp\nfunction getServerTimestamp() {\n    return SERVER_START_TIMESTAMP;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/serverTimestamp.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/sessionToken.ts":
/*!***********************************!*\
  !*** ./src/utils/sessionToken.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupAllExpiredSessions: () => (/* binding */ cleanupAllExpiredSessions),\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   removeAllOtherSessions: () => (/* binding */ removeAllOtherSessions),\n/* harmony export */   removeSession: () => (/* binding */ removeSession),\n/* harmony export */   validateSession: () => (/* binding */ validateSession)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n\n\n\n// Maximum number of sessions per user\nconst MAX_SESSIONS_PER_USER = 5;\n// Session expiration time in milliseconds (24 hours)\nconst SESSION_EXPIRATION_MS = 24 * 60 * 60 * 1000;\n/**\n * Generate a unique session token\n * @returns A random session token\n */ function generateSessionToken() {\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes)(32).toString(\"hex\");\n}\n/**\n * Create a new session for a user\n * @param userId The user ID\n * @param userAgent The user agent string\n * @param ipAddress The IP address\n * @returns The session token\n */ async function createSession(userId, userAgent, ipAddress) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const sessionToken = generateSessionToken();\n    const now = new Date();\n    const sessionInfo = {\n        token: sessionToken,\n        createdAt: now,\n        lastActive: now,\n        userAgent,\n        ipAddress\n    };\n    // Get the user with their current sessions\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    if (!user) {\n        throw new Error(\"User not found\");\n    }\n    // Clean up expired sessions first\n    await cleanupExpiredSessions(userId);\n    // If the user has too many active sessions, remove the oldest ones\n    if (user.activeSessions && user.activeSessions.length >= MAX_SESSIONS_PER_USER) {\n        // Sort sessions by lastActive (oldest first)\n        const sortedSessions = [\n            ...user.activeSessions\n        ].sort((a, b)=>a.lastActive.getTime() - b.lastActive.getTime());\n        // Calculate how many sessions to remove\n        const sessionsToRemove = Math.max(0, sortedSessions.length - MAX_SESSIONS_PER_USER + 1);\n        if (sessionsToRemove > 0) {\n            // Get the tokens of the oldest sessions\n            const tokensToRemove = sortedSessions.slice(0, sessionsToRemove).map((session)=>session.token);\n            // Remove the oldest sessions\n            await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n                _id: userId\n            }, {\n                $pull: {\n                    activeSessions: {\n                        token: {\n                            $in: tokensToRemove\n                        }\n                    }\n                }\n            });\n        }\n    }\n    // Add the new session\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findByIdAndUpdate(userId, {\n        $push: {\n            activeSessions: sessionInfo\n        }\n    }, {\n        new: true\n    });\n    return sessionToken;\n}\n/**\n * Clean up expired sessions for a user\n * @param userId The user ID\n */ async function cleanupExpiredSessions(userId) {\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                lastActive: {\n                    $lt: expirationThreshold\n                }\n            }\n        }\n    });\n}\n/**\n * Validate a session token for a user\n * @param userId The user ID\n * @param sessionToken The session token to validate\n * @returns True if the session is valid, false otherwise\n */ async function validateSession(userId, sessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // Clean up expired sessions first\n    await cleanupExpiredSessions(userId);\n    // Find the user and check if they have this session token\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findOne({\n        _id: userId,\n        \"activeSessions.token\": sessionToken\n    });\n    if (!user) {\n        return false;\n    }\n    // Find the specific session\n    const session = user.activeSessions.find((s)=>s.token === sessionToken);\n    if (!session) {\n        return false;\n    }\n    // Check if the session has expired\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    if (session.lastActive < expirationThreshold) {\n        // Session has expired, remove it\n        await removeSession(userId, sessionToken);\n        return false;\n    }\n    // Update the last active time for this session\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId,\n        \"activeSessions.token\": sessionToken\n    }, {\n        $set: {\n            \"activeSessions.$.lastActive\": now\n        }\n    });\n    return true;\n}\n/**\n * Remove a session for a user\n * @param userId The user ID\n * @param sessionToken The session token to remove\n */ async function removeSession(userId, sessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: sessionToken\n            }\n        }\n    });\n}\n/**\n * Get all active sessions for a user\n * @param userId The user ID\n * @returns Array of active sessions\n */ async function getUserSessions(userId) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    return user?.activeSessions || [];\n}\n/**\n * Remove all sessions for a user except the current one\n * @param userId The user ID\n * @param currentSessionToken The current session token to keep\n * @returns The number of sessions removed\n */ async function removeAllOtherSessions(userId, currentSessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // First clean up expired sessions\n    await cleanupExpiredSessions(userId);\n    // Get the user to count sessions before removal\n    const userBefore = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    const sessionCountBefore = userBefore?.activeSessions?.length || 0;\n    // Remove all other sessions\n    const result = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: {\n                    $ne: currentSessionToken\n                }\n            }\n        }\n    });\n    // Get the user again to count sessions after removal\n    const userAfter = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    const sessionCountAfter = userAfter?.activeSessions?.length || 0;\n    // Return the number of sessions removed\n    return sessionCountBefore - sessionCountAfter;\n}\n/**\n * Cleanup all expired sessions in the database\n * This can be run as a scheduled task\n */ async function cleanupAllExpiredSessions() {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    // Find all users with expired sessions\n    const users = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].find({\n        \"activeSessions.lastActive\": {\n            $lt: expirationThreshold\n        }\n    });\n    let totalRemoved = 0;\n    // Clean up expired sessions for each user\n    for (const user of users){\n        const sessionCountBefore = user.activeSessions.length;\n        // Remove expired sessions\n        await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n            _id: user._id\n        }, {\n            $pull: {\n                activeSessions: {\n                    lastActive: {\n                        $lt: expirationThreshold\n                    }\n                }\n            }\n        });\n        // Get the user again to count sessions after removal\n        const updatedUser = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(user._id);\n        const sessionCountAfter = updatedUser?.activeSessions?.length || 0;\n        totalRemoved += sessionCountBefore - sessionCountAfter;\n    }\n    return totalRemoved;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/sessionToken.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@babel","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();