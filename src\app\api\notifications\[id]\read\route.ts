import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/options';
import dbConnect from '@/lib/db/mongodb';
import Notification from '@/models/Notification';
import SmartNotification from '@/models/SmartNotification';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    await dbConnect();

    const notificationId = params.id;
    const { searchParams } = new URL(request.url);
    const isSmartNotification = searchParams.get('smart') === 'true';

    // Try to convert to ObjectId if needed
    let userId;
    try {
      const mongoose = require('mongoose');
      userId = mongoose.Types.ObjectId.isValid(session.user.id)
        ? new mongoose.Types.ObjectId(session.user.id)
        : session.user.id;
    } catch (error) {
      console.error('Error converting user ID to ObjectId:', error);
      userId = session.user.id;
    }

    // Update the appropriate notification type
    let updatedNotification;
    if (isSmartNotification) {
      updatedNotification = await SmartNotification.findOneAndUpdate(
        { _id: notificationId, userId },
        { 
          isRead: true,
          responseTime: Date.now() - new Date(request.headers.get('x-request-start') || Date.now()).getTime()
        },
        { new: true }
      );
    } else {
      updatedNotification = await Notification.findOneAndUpdate(
        { _id: notificationId, userId },
        { isRead: true },
        { new: true }
      );
    }

    if (!updatedNotification) {
      return NextResponse.json(
        { message: 'Notification not found or unauthorized' },
        { status: 404 }
      );
    }

    console.log(`Marked notification ${notificationId} as read for user ${session.user.id}`);

    return NextResponse.json({
      success: true,
      message: 'Notification marked as read',
      notification: updatedNotification
    });

  } catch (error: any) {
    console.error('Error marking notification as read:', error);
    return NextResponse.json(
      { message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    await dbConnect();

    const notificationId = params.id;
    const { searchParams } = new URL(request.url);
    const isSmartNotification = searchParams.get('smart') === 'true';

    // Try to convert to ObjectId if needed
    let userId;
    try {
      const mongoose = require('mongoose');
      userId = mongoose.Types.ObjectId.isValid(session.user.id)
        ? new mongoose.Types.ObjectId(session.user.id)
        : session.user.id;
    } catch (error) {
      console.error('Error converting user ID to ObjectId:', error);
      userId = session.user.id;
    }

    // Delete the appropriate notification type
    let deletedNotification;
    if (isSmartNotification) {
      deletedNotification = await SmartNotification.findOneAndDelete({
        _id: notificationId,
        userId
      });
    } else {
      deletedNotification = await Notification.findOneAndDelete({
        _id: notificationId,
        userId
      });
    }

    if (!deletedNotification) {
      return NextResponse.json(
        { message: 'Notification not found or unauthorized' },
        { status: 404 }
      );
    }

    console.log(`Deleted notification ${notificationId} for user ${session.user.id}`);

    return NextResponse.json({
      success: true,
      message: 'Notification deleted successfully'
    });

  } catch (error: any) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}
