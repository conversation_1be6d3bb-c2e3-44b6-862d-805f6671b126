"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wojtekmaj";
exports.ids = ["vendor-chunks/@wojtekmaj"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wojtekmaj/date-utils/dist/esm/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wojtekmaj/date-utils/dist/esm/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCenturyEnd: () => (/* binding */ getCenturyEnd),\n/* harmony export */   getCenturyRange: () => (/* binding */ getCenturyRange),\n/* harmony export */   getCenturyStart: () => (/* binding */ getCenturyStart),\n/* harmony export */   getDate: () => (/* binding */ getDate),\n/* harmony export */   getDayEnd: () => (/* binding */ getDayEnd),\n/* harmony export */   getDayRange: () => (/* binding */ getDayRange),\n/* harmony export */   getDayStart: () => (/* binding */ getDayStart),\n/* harmony export */   getDaysInMonth: () => (/* binding */ getDaysInMonth),\n/* harmony export */   getDecadeEnd: () => (/* binding */ getDecadeEnd),\n/* harmony export */   getDecadeRange: () => (/* binding */ getDecadeRange),\n/* harmony export */   getDecadeStart: () => (/* binding */ getDecadeStart),\n/* harmony export */   getHours: () => (/* binding */ getHours),\n/* harmony export */   getHoursMinutes: () => (/* binding */ getHoursMinutes),\n/* harmony export */   getHoursMinutesSeconds: () => (/* binding */ getHoursMinutesSeconds),\n/* harmony export */   getISOLocalDate: () => (/* binding */ getISOLocalDate),\n/* harmony export */   getISOLocalDateTime: () => (/* binding */ getISOLocalDateTime),\n/* harmony export */   getISOLocalMonth: () => (/* binding */ getISOLocalMonth),\n/* harmony export */   getMilliseconds: () => (/* binding */ getMilliseconds),\n/* harmony export */   getMinutes: () => (/* binding */ getMinutes),\n/* harmony export */   getMonth: () => (/* binding */ getMonth),\n/* harmony export */   getMonthEnd: () => (/* binding */ getMonthEnd),\n/* harmony export */   getMonthHuman: () => (/* binding */ getMonthHuman),\n/* harmony export */   getMonthRange: () => (/* binding */ getMonthRange),\n/* harmony export */   getMonthStart: () => (/* binding */ getMonthStart),\n/* harmony export */   getNextCenturyEnd: () => (/* binding */ getNextCenturyEnd),\n/* harmony export */   getNextCenturyStart: () => (/* binding */ getNextCenturyStart),\n/* harmony export */   getNextDayEnd: () => (/* binding */ getNextDayEnd),\n/* harmony export */   getNextDayStart: () => (/* binding */ getNextDayStart),\n/* harmony export */   getNextDecadeEnd: () => (/* binding */ getNextDecadeEnd),\n/* harmony export */   getNextDecadeStart: () => (/* binding */ getNextDecadeStart),\n/* harmony export */   getNextMonthEnd: () => (/* binding */ getNextMonthEnd),\n/* harmony export */   getNextMonthStart: () => (/* binding */ getNextMonthStart),\n/* harmony export */   getNextYearEnd: () => (/* binding */ getNextYearEnd),\n/* harmony export */   getNextYearStart: () => (/* binding */ getNextYearStart),\n/* harmony export */   getPreviousCenturyEnd: () => (/* binding */ getPreviousCenturyEnd),\n/* harmony export */   getPreviousCenturyStart: () => (/* binding */ getPreviousCenturyStart),\n/* harmony export */   getPreviousDayEnd: () => (/* binding */ getPreviousDayEnd),\n/* harmony export */   getPreviousDayStart: () => (/* binding */ getPreviousDayStart),\n/* harmony export */   getPreviousDecadeEnd: () => (/* binding */ getPreviousDecadeEnd),\n/* harmony export */   getPreviousDecadeStart: () => (/* binding */ getPreviousDecadeStart),\n/* harmony export */   getPreviousMonthEnd: () => (/* binding */ getPreviousMonthEnd),\n/* harmony export */   getPreviousMonthStart: () => (/* binding */ getPreviousMonthStart),\n/* harmony export */   getPreviousYearEnd: () => (/* binding */ getPreviousYearEnd),\n/* harmony export */   getPreviousYearStart: () => (/* binding */ getPreviousYearStart),\n/* harmony export */   getSeconds: () => (/* binding */ getSeconds),\n/* harmony export */   getYear: () => (/* binding */ getYear),\n/* harmony export */   getYearEnd: () => (/* binding */ getYearEnd),\n/* harmony export */   getYearRange: () => (/* binding */ getYearRange),\n/* harmony export */   getYearStart: () => (/* binding */ getYearStart)\n/* harmony export */ });\n/**\n * Utils\n */\nfunction makeGetEdgeOfNeighbor(getPeriod, getEdgeOfPeriod, defaultOffset) {\n    return function makeGetEdgeOfNeighborInternal(date, offset) {\n        if (offset === void 0) { offset = defaultOffset; }\n        var previousPeriod = getPeriod(date) + offset;\n        return getEdgeOfPeriod(previousPeriod);\n    };\n}\nfunction makeGetEnd(getBeginOfNextPeriod) {\n    return function makeGetEndInternal(date) {\n        return new Date(getBeginOfNextPeriod(date).getTime() - 1);\n    };\n}\nfunction makeGetRange(getStart, getEnd) {\n    return function makeGetRangeInternal(date) {\n        return [getStart(date), getEnd(date)];\n    };\n}\n/**\n * Simple getters - getting a property of a given point in time\n */\n/**\n * Gets year from a given date.\n *\n * @param {DateLike} date Date to get year from\n * @returns {number} Year\n */\nfunction getYear(date) {\n    if (date instanceof Date) {\n        return date.getFullYear();\n    }\n    if (typeof date === 'number') {\n        return date;\n    }\n    var year = parseInt(date, 10);\n    if (typeof date === 'string' && !isNaN(year)) {\n        return year;\n    }\n    throw new Error(\"Failed to get year from date: \".concat(date, \".\"));\n}\n/**\n * Gets month from a given date.\n *\n * @param {Date} date Date to get month from\n * @returns {number} Month\n */\nfunction getMonth(date) {\n    if (date instanceof Date) {\n        return date.getMonth();\n    }\n    throw new Error(\"Failed to get month from date: \".concat(date, \".\"));\n}\n/**\n * Gets human-readable month from a given date.\n *\n * @param {Date} date Date to get human-readable month from\n * @returns {number} Human-readable month\n */\nfunction getMonthHuman(date) {\n    if (date instanceof Date) {\n        return date.getMonth() + 1;\n    }\n    throw new Error(\"Failed to get human-readable month from date: \".concat(date, \".\"));\n}\n/**\n * Gets day of the month from a given date.\n *\n * @param {Date} date Date to get day of the month from\n * @returns {number} Day of the month\n */\nfunction getDate(date) {\n    if (date instanceof Date) {\n        return date.getDate();\n    }\n    throw new Error(\"Failed to get year from date: \".concat(date, \".\"));\n}\n/**\n * Gets hours from a given date.\n *\n * @param {Date | string} date Date to get hours from\n * @returns {number} Hours\n */\nfunction getHours(date) {\n    if (date instanceof Date) {\n        return date.getHours();\n    }\n    if (typeof date === 'string') {\n        var datePieces = date.split(':');\n        if (datePieces.length >= 2) {\n            var hoursString = datePieces[0];\n            if (hoursString) {\n                var hours = parseInt(hoursString, 10);\n                if (!isNaN(hours)) {\n                    return hours;\n                }\n            }\n        }\n    }\n    throw new Error(\"Failed to get hours from date: \".concat(date, \".\"));\n}\n/**\n * Gets minutes from a given date.\n *\n * @param {Date | string} date Date to get minutes from\n * @returns {number} Minutes\n */\nfunction getMinutes(date) {\n    if (date instanceof Date) {\n        return date.getMinutes();\n    }\n    if (typeof date === 'string') {\n        var datePieces = date.split(':');\n        if (datePieces.length >= 2) {\n            var minutesString = datePieces[1] || '0';\n            var minutes = parseInt(minutesString, 10);\n            if (!isNaN(minutes)) {\n                return minutes;\n            }\n        }\n    }\n    throw new Error(\"Failed to get minutes from date: \".concat(date, \".\"));\n}\n/**\n * Gets seconds from a given date.\n *\n * @param {Date | string} date Date to get seconds from\n * @returns {number} Seconds\n */\nfunction getSeconds(date) {\n    if (date instanceof Date) {\n        return date.getSeconds();\n    }\n    if (typeof date === 'string') {\n        var datePieces = date.split(':');\n        if (datePieces.length >= 2) {\n            var secondsWithMillisecondsString = datePieces[2] || '0';\n            var seconds = parseInt(secondsWithMillisecondsString, 10);\n            if (!isNaN(seconds)) {\n                return seconds;\n            }\n        }\n    }\n    throw new Error(\"Failed to get seconds from date: \".concat(date, \".\"));\n}\n/**\n * Gets milliseconds from a given date.\n *\n * @param {Date | string} date Date to get milliseconds from\n * @returns {number} Milliseconds\n */\nfunction getMilliseconds(date) {\n    if (date instanceof Date) {\n        return date.getMilliseconds();\n    }\n    if (typeof date === 'string') {\n        var datePieces = date.split(':');\n        if (datePieces.length >= 2) {\n            var secondsWithMillisecondsString = datePieces[2] || '0';\n            var millisecondsString = secondsWithMillisecondsString.split('.')[1] || '0';\n            var milliseconds = parseInt(millisecondsString, 10);\n            if (!isNaN(milliseconds)) {\n                return milliseconds;\n            }\n        }\n    }\n    throw new Error(\"Failed to get seconds from date: \".concat(date, \".\"));\n}\n/**\n * Century\n */\n/**\n * Gets century start date from a given date.\n *\n * @param {DateLike} date Date to get century start from\n * @returns {Date} Century start date\n */\nfunction getCenturyStart(date) {\n    var year = getYear(date);\n    var centuryStartYear = year + ((-year + 1) % 100);\n    var centuryStartDate = new Date();\n    centuryStartDate.setFullYear(centuryStartYear, 0, 1);\n    centuryStartDate.setHours(0, 0, 0, 0);\n    return centuryStartDate;\n}\n/**\n * Gets previous century start date from a given date.\n *\n * @param {DateLike} date Date to get previous century start from\n * @returns {Date} Previous century start date\n */\nvar getPreviousCenturyStart = makeGetEdgeOfNeighbor(getYear, getCenturyStart, -100);\n/**\n * Gets next century start date from a given date.\n *\n * @param {DateLike} date Date to get next century start from\n * @returns {Date} Next century start date\n */\nvar getNextCenturyStart = makeGetEdgeOfNeighbor(getYear, getCenturyStart, 100);\n/**\n * Gets century end date from a given date.\n *\n * @param {DateLike} date Date to get century end from\n * @returns {Date} Century end date\n */\nvar getCenturyEnd = makeGetEnd(getNextCenturyStart);\n/**\n * Gets previous century end date from a given date.\n *\n * @param {DateLike} date Date to get previous century end from\n * @returns {Date} Previous century end date\n */\nvar getPreviousCenturyEnd = makeGetEdgeOfNeighbor(getYear, getCenturyEnd, -100);\n/**\n * Gets next century end date from a given date.\n *\n * @param {DateLike} date Date to get next century end from\n * @returns {Date} Next century end date\n */\nvar getNextCenturyEnd = makeGetEdgeOfNeighbor(getYear, getCenturyEnd, 100);\n/**\n * Gets century start and end dates from a given date.\n *\n * @param {DateLike} date Date to get century start and end from\n * @returns {[Date, Date]} Century start and end dates\n */\nvar getCenturyRange = makeGetRange(getCenturyStart, getCenturyEnd);\n/**\n * Decade\n */\n/**\n * Gets decade start date from a given date.\n *\n * @param {DateLike} date Date to get decade start from\n * @returns {Date} Decade start date\n */\nfunction getDecadeStart(date) {\n    var year = getYear(date);\n    var decadeStartYear = year + ((-year + 1) % 10);\n    var decadeStartDate = new Date();\n    decadeStartDate.setFullYear(decadeStartYear, 0, 1);\n    decadeStartDate.setHours(0, 0, 0, 0);\n    return decadeStartDate;\n}\n/**\n * Gets previous decade start date from a given date.\n *\n * @param {DateLike} date Date to get previous decade start from\n * @returns {Date} Previous decade start date\n */\nvar getPreviousDecadeStart = makeGetEdgeOfNeighbor(getYear, getDecadeStart, -10);\n/**\n * Gets next decade start date from a given date.\n *\n * @param {DateLike} date Date to get next decade start from\n * @returns {Date} Next decade start date\n */\nvar getNextDecadeStart = makeGetEdgeOfNeighbor(getYear, getDecadeStart, 10);\n/**\n * Gets decade end date from a given date.\n *\n * @param {DateLike} date Date to get decade end from\n * @returns {Date} Decade end date\n */\nvar getDecadeEnd = makeGetEnd(getNextDecadeStart);\n/**\n * Gets previous decade end date from a given date.\n *\n * @param {DateLike} date Date to get previous decade end from\n * @returns {Date} Previous decade end date\n */\nvar getPreviousDecadeEnd = makeGetEdgeOfNeighbor(getYear, getDecadeEnd, -10);\n/**\n * Gets next decade end date from a given date.\n *\n * @param {DateLike} date Date to get next decade end from\n * @returns {Date} Next decade end date\n */\nvar getNextDecadeEnd = makeGetEdgeOfNeighbor(getYear, getDecadeEnd, 10);\n/**\n * Gets decade start and end dates from a given date.\n *\n * @param {DateLike} date Date to get decade start and end from\n * @returns {[Date, Date]} Decade start and end dates\n */\nvar getDecadeRange = makeGetRange(getDecadeStart, getDecadeEnd);\n/**\n * Year\n */\n/**\n * Gets year start date from a given date.\n *\n * @param {DateLike} date Date to get year start from\n * @returns {Date} Year start date\n */\nfunction getYearStart(date) {\n    var year = getYear(date);\n    var yearStartDate = new Date();\n    yearStartDate.setFullYear(year, 0, 1);\n    yearStartDate.setHours(0, 0, 0, 0);\n    return yearStartDate;\n}\n/**\n * Gets previous year start date from a given date.\n *\n * @param {DateLike} date Date to get previous year start from\n * @returns {Date} Previous year start date\n */\nvar getPreviousYearStart = makeGetEdgeOfNeighbor(getYear, getYearStart, -1);\n/**\n * Gets next year start date from a given date.\n *\n * @param {DateLike} date Date to get next year start from\n * @returns {Date} Next year start date\n */\nvar getNextYearStart = makeGetEdgeOfNeighbor(getYear, getYearStart, 1);\n/**\n * Gets year end date from a given date.\n *\n * @param {DateLike} date Date to get year end from\n * @returns {Date} Year end date\n */\nvar getYearEnd = makeGetEnd(getNextYearStart);\n/**\n * Gets previous year end date from a given date.\n *\n * @param {DateLike} date Date to get previous year end from\n * @returns {Date} Previous year end date\n */\nvar getPreviousYearEnd = makeGetEdgeOfNeighbor(getYear, getYearEnd, -1);\n/**\n * Gets next year end date from a given date.\n *\n * @param {DateLike} date Date to get next year end from\n * @returns {Date} Next year end date\n */\nvar getNextYearEnd = makeGetEdgeOfNeighbor(getYear, getYearEnd, 1);\n/**\n * Gets year start and end dates from a given date.\n *\n * @param {DateLike} date Date to get year start and end from\n * @returns {[Date, Date]} Year start and end dates\n */\nvar getYearRange = makeGetRange(getYearStart, getYearEnd);\n/**\n * Month\n */\nfunction makeGetEdgeOfNeighborMonth(getEdgeOfPeriod, defaultOffset) {\n    return function makeGetEdgeOfNeighborMonthInternal(date, offset) {\n        if (offset === void 0) { offset = defaultOffset; }\n        var year = getYear(date);\n        var month = getMonth(date) + offset;\n        var previousPeriod = new Date();\n        previousPeriod.setFullYear(year, month, 1);\n        previousPeriod.setHours(0, 0, 0, 0);\n        return getEdgeOfPeriod(previousPeriod);\n    };\n}\n/**\n * Gets month start date from a given date.\n *\n * @param {DateLike} date Date to get month start from\n * @returns {Date} Month start date\n */\nfunction getMonthStart(date) {\n    var year = getYear(date);\n    var month = getMonth(date);\n    var monthStartDate = new Date();\n    monthStartDate.setFullYear(year, month, 1);\n    monthStartDate.setHours(0, 0, 0, 0);\n    return monthStartDate;\n}\n/**\n * Gets previous month start date from a given date.\n *\n * @param {DateLike} date Date to get previous month start from\n * @returns {Date} Previous month start date\n */\nvar getPreviousMonthStart = makeGetEdgeOfNeighborMonth(getMonthStart, -1);\n/**\n * Gets next month start date from a given date.\n *\n * @param {DateLike} date Date to get next month start from\n * @returns {Date} Next month start date\n */\nvar getNextMonthStart = makeGetEdgeOfNeighborMonth(getMonthStart, 1);\n/**\n * Gets month end date from a given date.\n *\n * @param {DateLike} date Date to get month end from\n * @returns {Date} Month end date\n */\nvar getMonthEnd = makeGetEnd(getNextMonthStart);\n/**\n * Gets previous month end date from a given date.\n *\n * @param {DateLike} date Date to get previous month end from\n * @returns {Date} Previous month end date\n */\nvar getPreviousMonthEnd = makeGetEdgeOfNeighborMonth(getMonthEnd, -1);\n/**\n * Gets next month end date from a given date.\n *\n * @param {DateLike} date Date to get next month end from\n * @returns {Date} Next month end date\n */\nvar getNextMonthEnd = makeGetEdgeOfNeighborMonth(getMonthEnd, 1);\n/**\n * Gets month start and end dates from a given date.\n *\n * @param {DateLike} date Date to get month start and end from\n * @returns {[Date, Date]} Month start and end dates\n */\nvar getMonthRange = makeGetRange(getMonthStart, getMonthEnd);\n/**\n * Day\n */\nfunction makeGetEdgeOfNeighborDay(getEdgeOfPeriod, defaultOffset) {\n    return function makeGetEdgeOfNeighborDayInternal(date, offset) {\n        if (offset === void 0) { offset = defaultOffset; }\n        var year = getYear(date);\n        var month = getMonth(date);\n        var day = getDate(date) + offset;\n        var previousPeriod = new Date();\n        previousPeriod.setFullYear(year, month, day);\n        previousPeriod.setHours(0, 0, 0, 0);\n        return getEdgeOfPeriod(previousPeriod);\n    };\n}\n/**\n * Gets day start date from a given date.\n *\n * @param {DateLike} date Date to get day start from\n * @returns {Date} Day start date\n */\nfunction getDayStart(date) {\n    var year = getYear(date);\n    var month = getMonth(date);\n    var day = getDate(date);\n    var dayStartDate = new Date();\n    dayStartDate.setFullYear(year, month, day);\n    dayStartDate.setHours(0, 0, 0, 0);\n    return dayStartDate;\n}\n/**\n * Gets previous day start date from a given date.\n *\n * @param {DateLike} date Date to get previous day start from\n * @returns {Date} Previous day start date\n */\nvar getPreviousDayStart = makeGetEdgeOfNeighborDay(getDayStart, -1);\n/**\n * Gets next day start date from a given date.\n *\n * @param {DateLike} date Date to get next day start from\n * @returns {Date} Next day start date\n */\nvar getNextDayStart = makeGetEdgeOfNeighborDay(getDayStart, 1);\n/**\n * Gets day end date from a given date.\n *\n * @param {DateLike} date Date to get day end from\n * @returns {Date} Day end date\n */\nvar getDayEnd = makeGetEnd(getNextDayStart);\n/**\n * Gets previous day end date from a given date.\n *\n * @param {DateLike} date Date to get previous day end from\n * @returns {Date} Previous day end date\n */\nvar getPreviousDayEnd = makeGetEdgeOfNeighborDay(getDayEnd, -1);\n/**\n * Gets next day end date from a given date.\n *\n * @param {DateLike} date Date to get next day end from\n * @returns {Date} Next day end date\n */\nvar getNextDayEnd = makeGetEdgeOfNeighborDay(getDayEnd, 1);\n/**\n * Gets day start and end dates from a given date.\n *\n * @param {DateLike} date Date to get day start and end from\n * @returns {[Date, Date]} Day start and end dates\n */\nvar getDayRange = makeGetRange(getDayStart, getDayEnd);\n/**\n * Other\n */\n/**\n * Returns a number of days in a month of a given date.\n *\n * @param {Date} date Date\n * @returns {number} Number of days in a month\n */\nfunction getDaysInMonth(date) {\n    return getDate(getMonthEnd(date));\n}\nfunction padStart(num, val) {\n    if (val === void 0) { val = 2; }\n    var numStr = \"\".concat(num);\n    if (numStr.length >= val) {\n        return num;\n    }\n    return \"0000\".concat(numStr).slice(-val);\n}\n/**\n * Returns local hours and minutes (hh:mm).\n *\n * @param {Date | string} date Date to get hours and minutes from\n * @returns {string} Local hours and minutes\n */\nfunction getHoursMinutes(date) {\n    var hours = padStart(getHours(date));\n    var minutes = padStart(getMinutes(date));\n    return \"\".concat(hours, \":\").concat(minutes);\n}\n/**\n * Returns local hours, minutes and seconds (hh:mm:ss).\n *\n * @param {Date | string} date Date to get hours, minutes and seconds from\n * @returns {string} Local hours, minutes and seconds\n */\nfunction getHoursMinutesSeconds(date) {\n    var hours = padStart(getHours(date));\n    var minutes = padStart(getMinutes(date));\n    var seconds = padStart(getSeconds(date));\n    return \"\".concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n}\n/**\n * Returns local month in ISO-like format (YYYY-MM).\n *\n * @param {Date} date Date to get month in ISO-like format from\n * @returns {string} Local month in ISO-like format\n */\nfunction getISOLocalMonth(date) {\n    var year = padStart(getYear(date), 4);\n    var month = padStart(getMonthHuman(date));\n    return \"\".concat(year, \"-\").concat(month);\n}\n/**\n * Returns local date in ISO-like format (YYYY-MM-DD).\n *\n * @param {Date} date Date to get date in ISO-like format from\n * @returns {string} Local date in ISO-like format\n */\nfunction getISOLocalDate(date) {\n    var year = padStart(getYear(date), 4);\n    var month = padStart(getMonthHuman(date));\n    var day = padStart(getDate(date));\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n}\n/**\n * Returns local date & time in ISO-like format (YYYY-MM-DDThh:mm:ss).\n *\n * @param {Date} date Date to get date & time in ISO-like format from\n * @returns {string} Local date & time in ISO-like format\n */\nfunction getISOLocalDateTime(date) {\n    return \"\".concat(getISOLocalDate(date), \"T\").concat(getHoursMinutesSeconds(date));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wojtekmaj/date-utils/dist/esm/index.js\n");

/***/ })

};
;