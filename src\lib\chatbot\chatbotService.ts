/**
 * Chatbot Service
 * Manages conversation state and integrates with Gemini AI
 */

import { ChatbotService as GeminiChatbotService } from '@/lib/gemini';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  actions?: Array<{
    type: 'navigate' | 'search' | 'create' | 'help' | 'info';
    label: string;
    data?: any;
  }>;
  quickReplies?: string[];
}

export interface ChatSession {
  id: string;
  userId: string;
  messages: ChatMessage[];
  context: {
    user?: any;
    currentPage?: string;
    systemState?: any;
    documentData?: {
      currentDocument?: any;
      documentJourney?: any[];
      routingSlip?: any[];
      relatedDocuments?: any[];
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

class ChatbotService {
  private sessions: Map<string, ChatSession> = new Map();

  /**
   * Start a new chat session
   */
  async startSession(userId: string, userContext: any): Promise<ChatSession> {
    const sessionId = `chat_${userId}_${Date.now()}`;

    const session: ChatSession = {
      id: sessionId,
      userId,
      messages: [],
      context: {
        user: userContext.user,
        currentPage: userContext.currentPage,
        systemState: userContext.systemState
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add welcome message
    const welcomeMessage = await this.generateWelcomeMessage(userContext);
    session.messages.push({
      id: `msg_${Date.now()}`,
      role: 'assistant',
      content: welcomeMessage.message,
      timestamp: new Date(),
      actions: welcomeMessage.actions,
      quickReplies: welcomeMessage.quickReplies
    });

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Send a message and get response
   */
  async sendMessage(sessionId: string, userMessage: string): Promise<ChatMessage> {
    let session = this.sessions.get(sessionId);

    // If session not found, try to recover or create a new one
    if (!session) {
      console.warn(`Chat session ${sessionId} not found, attempting to recover...`);

      // Extract userId from sessionId if possible
      const userIdMatch = sessionId.match(/^chat_(.+)_\d+$/);
      if (userIdMatch) {
        const userId = userIdMatch[1];
        console.log(`Creating new session for user ${userId}`);

        // Create a minimal session to continue the conversation
        session = {
          id: sessionId,
          userId,
          messages: [],
          context: {
            user: { id: userId },
            currentPage: '/documents',
            systemState: { timestamp: new Date().toISOString() }
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Add a recovery message
        const recoveryMessage: ChatMessage = {
          id: `msg_${Date.now()}_recovery`,
          role: 'assistant',
          content: `👋 **Welcome back!** I noticed our conversation was interrupted, but I'm here to help you with the Document Tracking System.

**🔄 Session Recovered** - You can continue asking questions about:
• Document management and workflows
• System navigation and features
• Routing slips and document journeys
• Troubleshooting and support

How can I assist you today?`,
          timestamp: new Date(),
          actions: [
            { type: 'navigate', label: '📊 Dashboard', data: { url: '/dashboard' } },
            { type: 'navigate', label: '📝 Create Document', data: { url: '/documents/add' } },
            { type: 'help', label: '📚 Help Guide', data: { topic: 'help' } }
          ],
          quickReplies: ['How to send documents?', 'Check my inbox', 'Document workflow', 'System features']
        };

        session.messages.push(recoveryMessage);
        this.sessions.set(sessionId, session);
      } else {
        throw new Error('Chat session not found and cannot be recovered. Please refresh the page to start a new session.');
      }
    }

    // Add user message to session
    const userMsg: ChatMessage = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: userMessage,
      timestamp: new Date()
    };
    session.messages.push(userMsg);

    // Prepare conversation history for AI
    const conversationHistory = session.messages.slice(-10).map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // Generate AI response with error handling
    let aiResponse;
    try {
      aiResponse = await GeminiChatbotService.generateChatResponse(userMessage, {
        user: session.context.user,
        conversationHistory,
        systemState: session.context.systemState,
        currentPage: session.context.currentPage
      });
    } catch (aiError: any) {
      console.error('Error generating AI response:', aiError);
      // Fallback response
      aiResponse = {
        message: `I apologize, but I'm having trouble processing your message right now. However, I can still help you!

Here are some things I can assist with:
• **Document Management**: Creating, sending, and receiving documents
• **System Navigation**: Finding features and understanding workflows
• **Troubleshooting**: Solving common issues and problems
• **General Help**: Answering questions about the system

Please try asking your question in a different way, or choose from the quick replies below.`,
        actions: [
          { type: 'navigate', label: '📊 Dashboard', data: { url: '/dashboard' } },
          { type: 'navigate', label: '📝 Create Document', data: { url: '/documents/add' } },
          { type: 'help', label: '📚 Help Guide', data: { topic: 'help' } }
        ],
        quickReplies: ['How to send documents?', 'Check my inbox', 'System help', 'Contact support']
      };
    }

    // Create assistant message
    const assistantMsg: ChatMessage = {
      id: `msg_${Date.now()}_assistant`,
      role: 'assistant',
      content: aiResponse.message,
      timestamp: new Date(),
      actions: aiResponse.actions,
      quickReplies: aiResponse.quickReplies
    };

    session.messages.push(assistantMsg);
    session.updatedAt = new Date();

    return assistantMsg;
  }

  /**
   * Get chat session
   */
  getSession(sessionId: string): ChatSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Update session context
   */
  updateSessionContext(sessionId: string, context: Partial<ChatSession['context']>): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.context = { ...session.context, ...context };
      session.updatedAt = new Date();
    }
  }

  /**
   * Update session with document context for routing slip and journey data
   */
  updateDocumentContext(sessionId: string, documentData: {
    currentDocument?: any;
    documentJourney?: any[];
    routingSlip?: any[];
    relatedDocuments?: any[];
  }): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.context.documentData = documentData;
      session.updatedAt = new Date();
    }
  }

  /**
   * Get contextual help for current situation
   */
  async getContextualHelp(userId: string, context: any): Promise<any> {
    return await GeminiChatbotService.generateContextualHelp({
      currentPage: context.currentPage,
      userRole: context.user?.role,
      recentActions: context.recentActions,
      pendingDocuments: context.pendingDocuments,
      commonIssues: context.commonIssues
    });
  }

  /**
   * Analyze user intent
   */
  async analyzeIntent(userMessage: string, context: any): Promise<any> {
    return await GeminiChatbotService.analyzeIntent(userMessage);
  }

  /**
   * Generate intelligent welcome message based on user context
   */
  private async generateWelcomeMessage(userContext: any): Promise<{
    message: string;
    actions?: any[];
    quickReplies?: string[];
  }> {
    const user = userContext.user;
    const currentPage = userContext.currentPage;
    const userRole = user?.role || 'EMPLOYEE';
    const userDivision = user?.division || 'Unknown';

    let message = `👋 **Hello ${user?.name || 'there'}!** I'm MGB Bot, your intelligent AI assistant for the **Mines and Geosciences Bureau Regional Office No. II** Document Tracking System.`;

    // Role-based welcome
    if (userRole === 'ADMIN' || userRole === 'REGIONAL_DIRECTOR') {
      message += `\n\n🔧 **Administrative Access Detected** - I can help you with user management, system reports, and administrative functions.`;
    } else if (userRole === 'DIVISION_CHIEF') {
      message += `\n\n👥 **Division Chief Access** - I can assist with division management, workflow oversight, and team coordination.`;
    } else {
      message += `\n\n📄 **Employee Access** - I'm here to help with document management, workflow processes, and system navigation.`;
    }

    // Division-specific context
    if (userDivision !== 'Unknown') {
      const divisionInfo = {
        'ORD': 'Office of the Regional Director - Leadership and policy coordination',
        'FAD': 'Finance and Administrative Division - Financial and HR management',
        'MMD': 'Mines Management Division - Mining operations and permits',
        'MSESDD': 'Mines Safety, Environment and Social Development Division - Safety and environmental compliance',
        'GSD': 'Geological Survey Division - Geological research and surveys'
      };

      if (divisionInfo[userDivision]) {
        message += `\n\n🏢 **${userDivision} Division** - ${divisionInfo[userDivision]}`;
      }
    }

    // Contextual welcome based on current page
    if (currentPage?.includes('/documents/add')) {
      message += '\n\n📝 **Creating a Document** - I can guide you through the document creation process, help you choose the right category and action type, and explain the workflow.';
    } else if (currentPage?.includes('/documents') && currentPage?.includes('filter=inbox')) {
      message += '\n\n📥 **Inbox Management** - I can help you understand document statuses, process received documents, and manage your workflow efficiently.';
    } else if (currentPage?.includes('/dashboard')) {
      message += '\n\n📊 **Dashboard Overview** - I can explain the statistics, help you navigate to different sections, and provide insights about your document activity.';
    } else if (currentPage?.includes('/admin')) {
      message += '\n\n🔧 **Administrative Panel** - I can assist with user management, system configuration, reports generation, and administrative tasks.';
    } else {
      message += '\n\n🎯 **Ready to Help** - Ask me about any aspect of the document tracking system!';
    }

    message += '\n\n**💡 What I can help you with:**\n• Document creation and management\n• Workflow processes and status tracking\n• Advanced search and filtering\n• System features and navigation\n• Role-specific functions and permissions';

    const actions = [
      { type: 'help', label: '🚀 Quick Tour', data: { topic: 'getting_started' } },
      { type: 'navigate', label: '📊 Dashboard', data: { url: '/dashboard' } },
      { type: 'help', label: '🎯 System Features', data: { topic: 'features' } }
    ];

    // Role-specific quick replies
    let quickReplies = [];
    if (userRole === 'ADMIN' || userRole === 'REGIONAL_DIRECTOR') {
      quickReplies = [
        'User management help',
        'System reports and analytics',
        'Administrative functions',
        'How to send documents?'
      ];
    } else if (userRole === 'DIVISION_CHIEF') {
      quickReplies = [
        'Division workflow management',
        'Team coordination',
        'How to send documents?',
        'Check my inbox'
      ];
    } else {
      quickReplies = [
        'How do I send a document?',
        'Check my inbox',
        'Document workflow help',
        'Advanced search tips'
      ];
    }

    return { message, actions, quickReplies };
  }

  /**
   * Clean up old sessions (call periodically)
   */
  cleanupOldSessions(): void {
    // Increase session lifetime to 4 hours to reduce session loss
    const fourHoursAgo = new Date(Date.now() - 4 * 60 * 60 * 1000);

    let cleanedCount = 0;
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.updatedAt < fourHoursAgo) {
        this.sessions.delete(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} old chat sessions`);
    }
  }

  /**
   * Check if session exists and is valid
   */
  hasValidSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    // Check if session is not too old (4 hours)
    const fourHoursAgo = new Date(Date.now() - 4 * 60 * 60 * 1000);
    return session.updatedAt > fourHoursAgo;
  }

  /**
   * Get session statistics
   */
  getStats(): {
    activeSessions: number;
    totalMessages: number;
    averageSessionLength: number;
  } {
    const activeSessions = this.sessions.size;
    let totalMessages = 0;
    let totalSessionLength = 0;

    for (const session of this.sessions.values()) {
      totalMessages += session.messages.length;
      totalSessionLength += session.messages.length;
    }

    return {
      activeSessions,
      totalMessages,
      averageSessionLength: activeSessions > 0 ? totalSessionLength / activeSessions : 0
    };
  }
}

// Export singleton instance
export const chatbotService = new ChatbotService();

// Auto-cleanup old sessions every 30 minutes
if (typeof window === 'undefined') { // Server-side only
  setInterval(() => {
    chatbotService.cleanupOldSessions();
  }, 30 * 60 * 1000);
}
