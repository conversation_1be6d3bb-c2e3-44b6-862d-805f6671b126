<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Initialize Database</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background-color: #2563eb;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Database Initialization</h1>
        <p>Click the button below to initialize the MongoDB database with test data. This will create:</p>
        <ul>
            <li>✅ A test user</li>
            <li>📄 A test document</li>
            <li>🔔 A test notification</li>
        </ul>
        <p>After running this, refresh MongoDB Compass to see the <strong>"document-tracker"</strong> database appear.</p>
        
        <button onclick="initializeDatabase()">🚀 Initialize Database</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function initializeDatabase() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '⏳ Initializing database...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch('/api/init-db', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `✅ Database initialized successfully!

📊 Created:
• User ID: ${data.data.userId}
• Document ID: ${data.data.documentId}  
• Notification ID: ${data.data.notificationId}

🔄 Now refresh MongoDB Compass to see the "document-tracker" database!`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `❌ Error: ${data.message}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
