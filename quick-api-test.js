// Quick API test
require('dotenv').config({ path: '.env.local' });

const apiKey = process.env.GEMINI_API_KEY;
console.log('Testing API key:', apiKey ? apiKey.substring(0, 10) + '...' : 'NOT FOUND');

if (!apiKey) {
  console.log('No API key found');
  process.exit(1);
}

const testAPI = async () => {
  try {
    const response = await fetch('https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent?key=' + apiKey, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{ parts: [{ text: 'Say hello' }] }],
        generationConfig: { temperature: 0.1, maxOutputTokens: 50 }
      })
    });
    
    console.log('Response status:', response.status);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ SUCCESS! API key is working');
      console.log('Response:', data.candidates?.[0]?.content?.parts?.[0]?.text || 'No response text');
    } else {
      console.log('❌ FAILED');
      console.log('Error:', JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.log('❌ ERROR:', error.message);
  }
};

testAPI();
