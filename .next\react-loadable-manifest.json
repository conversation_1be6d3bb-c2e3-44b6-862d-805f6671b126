{"app\\(user)\\dashboard\\page.tsx -> @/components/PhilippineCalendar": {"id": "app\\(user)\\dashboard\\page.tsx -> @/components/PhilippineCalendar", "files": ["static/css/_app-pages-browser_src_components_PhilippineCalendar_tsx.css", "static/chunks/_app-pages-browser_src_components_PhilippineCalendar_tsx.js"]}, "app\\(user)\\dashboard\\page.tsx -> @/components/QuickActionCard": {"id": "app\\(user)\\dashboard\\page.tsx -> @/components/QuickActionCard", "files": ["static/chunks/_app-pages-browser_src_components_QuickActionCard_tsx.js"]}, "app\\(user)\\dashboard\\page.tsx -> @/components/RecentDocumentCard": {"id": "app\\(user)\\dashboard\\page.tsx -> @/components/RecentDocumentCard", "files": ["static/chunks/_app-pages-browser_src_components_RecentDocumentCard_tsx.js"]}, "components\\PerformanceMonitor.tsx -> web-vitals": {"id": "components\\PerformanceMonitor.tsx -> web-vitals", "files": ["static/chunks/_app-pages-browser_node_modules_web-vitals_dist_web-vitals_js.js"]}, "components\\UnifiedHeader.tsx -> ./ColorSchemeToggle": {"id": "components\\UnifiedHeader.tsx -> ./ColorSchemeToggle", "files": ["static/chunks/_app-pages-browser_src_components_ColorSchemeToggle_tsx.js"]}, "components\\UnifiedHeader.tsx -> ./NotificationDropdown": {"id": "components\\UnifiedHeader.tsx -> ./NotificationDropdown", "files": ["static/chunks/_app-pages-browser_src_components_NotificationDropdown_tsx.js"]}}