import mongoose from 'mongoose';
import { registerModels } from './register-models';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/document-tracker';

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable');
}

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */
// Define the type for our cached mongoose connection
interface MongooseCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
  isConnecting: boolean;
  lastConnectionAttempt: number;
}

// Declare global variable with mongoose property
declare global {
  var mongoose: MongooseCache | undefined;
}

// Optimized connection options for better performance
const connectionOptions = {
  bufferCommands: false,
  serverSelectionTimeoutMS: 5000, // Reduced timeout for faster failures
  socketTimeoutMS: 30000, // Reduced socket timeout
  family: 4, // Use IPv4, skip trying IPv6
  // Connection pooling for better performance
  maxPoolSize: 20, // Increased pool size for concurrent requests
  minPoolSize: 5, // Maintain minimum connections
  maxIdleTimeMS: 30000, // Close idle connections after 30 seconds
  // Enable auto reconnect
  autoIndex: false, // Disable auto-indexing in production for performance
  autoCreate: true,
  // Connection health monitoring
  heartbeatFrequencyMS: 10000, // Check connection every 10 seconds
  // Performance optimizations
  retryWrites: true,
  retryReads: true,
  readPreference: 'primaryPreferred', // Allow reading from secondaries
  // Compression for better network performance
  compressors: ['zlib'],
};

// Initialize cached connection object
let cached: MongooseCache = global.mongoose || {
  conn: null,
  promise: null,
  isConnecting: false,
  lastConnectionAttempt: 0
};

if (!global.mongoose) {
  global.mongoose = cached;
}

// Set up connection event listeners
function setupConnectionMonitoring() {
  // Only set up listeners once
  if (mongoose.connection.listenerCount('connected') > 0) return;

  mongoose.connection.on('connected', () => {
    console.log('MongoDB connection established successfully');
  });

  mongoose.connection.on('error', (err) => {
    console.error('MongoDB connection error:', err);
  });

  mongoose.connection.on('disconnected', () => {
    console.warn('MongoDB disconnected. Will attempt to reconnect automatically.');
  });

  // Handle process termination
  process.on('SIGINT', async () => {
    try {
      await mongoose.connection.close();
      console.log('MongoDB connection closed due to application termination');
      process.exit(0);
    } catch (err) {
      console.error('Error closing MongoDB connection:', err);
      process.exit(1);
    }
  });
}

// Check if connection is healthy
function isConnectionHealthy(): boolean {
  return mongoose.connection.readyState === 1; // 1 = connected
}

// Main connection function
async function dbConnect() {
  // Set up connection monitoring
  setupConnectionMonitoring();

  // If we already have a connection and it's healthy, return it
  if (cached.conn && isConnectionHealthy()) {
    console.log('Using existing MongoDB connection');
    return cached.conn;
  }

  console.log('No healthy MongoDB connection found, creating a new one');

  // If we're already trying to connect, wait for that promise
  if (cached.isConnecting && cached.promise) {
    try {
      cached.conn = await cached.promise;
      return cached.conn;
    } catch (error) {
      // If the current connection attempt fails, we'll try again below
      console.error('Ongoing connection attempt failed:', error);
    }
  }

  // Prevent connection attempts in rapid succession (throttle to once per 5 seconds)
  const now = Date.now();
  const minTimeBetweenAttempts = 5000; // 5 seconds
  if (now - cached.lastConnectionAttempt < minTimeBetweenAttempts) {
    console.warn('Connection attempt throttled. Waiting before retrying...');
    await new Promise(resolve => setTimeout(resolve, minTimeBetweenAttempts));
  }

  // Start a new connection attempt
  cached.isConnecting = true;
  cached.lastConnectionAttempt = Date.now();
  cached.promise = mongoose.connect(MONGODB_URI, connectionOptions)
    .then((mongoose) => {
      console.log('MongoDB connected successfully');
      cached.isConnecting = false;
      // Register models after successful connection
      registerModels();
      return mongoose;
    })
    .catch((error) => {
      console.error('MongoDB connection error:', error);
      cached.isConnecting = false;
      cached.promise = null; // Reset the promise on error
      throw error;
    });

  try {
    cached.conn = await cached.promise;
    return cached.conn;
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    // Implement exponential backoff for retries in production
    if (process.env.NODE_ENV === 'production') {
      console.log('Will retry connection automatically...');
    }
    throw error;
  }
}

// Export the connection function and health check
export default dbConnect;
export { isConnectionHealthy };
