"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/components/UnifiedHeader.tsx":
/*!******************************************!*\
  !*** ./src/components/UnifiedHeader.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _DarkModeToggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./DarkModeToggle */ \"(app-pages-browser)/./src/components/DarkModeToggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Dynamically import non-critical components\nvar RealTimeNotifications = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n    return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_notifications_RealTimeNotifications_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./notifications/RealTimeNotifications */ \"(app-pages-browser)/./src/components/notifications/RealTimeNotifications.tsx\"));\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\UnifiedHeader.tsx -> \" + \"./notifications/RealTimeNotifications\"\n        ]\n    },\n    ssr: false,\n    loading: function() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-8 h-8 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n            lineNumber: 17,\n            columnNumber: 5\n        }, _this);\n    }\n});\n_c = RealTimeNotifications;\nvar ColorSchemeToggle = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n    return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_ColorSchemeToggle_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./ColorSchemeToggle */ \"(app-pages-browser)/./src/components/ColorSchemeToggle.tsx\"));\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\UnifiedHeader.tsx -> \" + \"./ColorSchemeToggle\"\n        ]\n    },\n    ssr: false,\n    loading: function() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-8 h-8 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n            lineNumber: 26,\n            columnNumber: 5\n        }, _this);\n    }\n});\n_c1 = ColorSchemeToggle;\nfunction UnifiedHeader(param) {\n    var _param_isAdminSection = param.isAdminSection, isAdminSection = _param_isAdminSection === void 0 ? false : _param_isAdminSection;\n    var _session_user_name, _session_user, _session_user1, _session_user2, _session_user3, _session_user4, _session_user5, _session_user6, _session_user7;\n    _s();\n    var _useSession = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)(), session = _useSession.data;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_9__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), 2), isProfileOpen = _useState[0], setIsProfileOpen = _useState[1];\n    var dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setIsProfileOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-40 transition-colors duration-200 shadow-sm border-b\",\n        style: {\n            backgroundColor: \"rgba(var(--theme-bg-secondary), 1)\",\n            borderColor: \"rgba(var(--theme-border-primary), 1)\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: isAdminSection ? \"/admin\" : \"/dashboard\",\n                            className: \"flex-shrink-0 flex items-center group pl-10 md:pl-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-10 w-10 overflow-hidden rounded-xl shadow-sm transition-all duration-200 group-hover:shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/MGB Logo_Proxy.png\",\n                                        alt: \"MGB Logo\",\n                                        fill: true,\n                                        sizes: \"40px\",\n                                        priority: true,\n                                        fetchPriority: \"high\",\n                                        className: \"object-cover transition-transform duration-200 group-hover:scale-105\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-secondary-500 dark:text-secondary-400 font-bold text-lg transition-colors\",\n                                            children: isAdminSection ? \"MGB Admin\" : \"MGB\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-accent-green-600 dark:text-accent-green-400 transition-colors\",\n                                            children: isAdminSection ? \"Administration Panel\" : \"Document Management System\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RealTimeNotifications, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorSchemeToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DarkModeToggle__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                ref: dropdownRef,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return setIsProfileOpen(!isProfileOpen);\n                                        },\n                                        className: \"flex items-center space-x-3 focus:outline-none\",\n                                        \"aria-expanded\": isProfileOpen,\n                                        \"aria-haspopup\": \"true\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-9 w-9 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center text-white shadow-sm overflow-hidden transition-transform hover:scale-105\",\n                                                children: (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : (_session_user_name = _session_user.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.charAt(0).toUpperCase()) || \"U\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden md:block text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors\",\n                                                        children: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 transition-colors\",\n                                                        children: (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.role) === _types__WEBPACK_IMPORTED_MODULE_6__.UserRole.ADMIN ? \"Administrator\" : session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.division\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 text-gray-400 transition-transform duration-200 \".concat(isProfileOpen ? \"rotate-180\" : \"\"),\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"origin-top-right absolute right-0 mt-2 w-56 rounded-xl shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50 transition-all duration-200 ease-out animate-dropdown\",\n                                        role: \"menu\",\n                                        \"aria-orientation\": \"vertical\",\n                                        \"aria-labelledby\": \"user-menu\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-2\",\n                                            role: \"none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"block px-4 py-3 text-sm border-b border-gray-100 dark:border-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: session === null || session === void 0 ? void 0 : (_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : _session_user4.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 dark:text-gray-400 text-xs mt-0.5\",\n                                                            children: session === null || session === void 0 ? void 0 : (_session_user5 = session.user) === null || _session_user5 === void 0 ? void 0 : _session_user5.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/profile\",\n                                                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                    role: \"menuitem\",\n                                                    onClick: function() {\n                                                        return setIsProfileOpen(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-4 w-4 mr-2 text-gray-500 dark:text-gray-400\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"My Profile\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/settings\",\n                                                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                    role: \"menuitem\",\n                                                    onClick: function() {\n                                                        return setIsProfileOpen(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-4 w-4 mr-2 text-gray-500 dark:text-gray-400\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Settings\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !isAdminSection && ((session === null || session === void 0 ? void 0 : (_session_user6 = session.user) === null || _session_user6 === void 0 ? void 0 : _session_user6.role) === _types__WEBPACK_IMPORTED_MODULE_6__.UserRole.ADMIN || (session === null || session === void 0 ? void 0 : (_session_user7 = session.user) === null || _session_user7 === void 0 ? void 0 : _session_user7.role) === _types__WEBPACK_IMPORTED_MODULE_6__.UserRole.REGIONAL_DIRECTOR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/admin\",\n                                                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                    role: \"menuitem\",\n                                                    onClick: function() {\n                                                        return setIsProfileOpen(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-4 w-4 mr-2 text-gray-500 dark:text-gray-400\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Admin Panel\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-100 dark:border-gray-700 my-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        var callbackUrl = window.location.origin;\n                                                        console.log(\"Signing out with callback URL:\", callbackUrl);\n                                                        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n                                                            callbackUrl: callbackUrl\n                                                        });\n                                                    },\n                                                    className: \"flex items-center w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\",\n                                                    role: \"menuitem\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-4 w-4 mr-2\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm6.293 11.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L11.586 9H5a1 1 0 100 2h6.586l-2.293 2.293z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Sign Out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(UnifiedHeader, \"wGlkxzGmN8Dwm3g/cdyEQ0osjzg=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c2 = UnifiedHeader;\n// Memoize the component to prevent unnecessary re-renders\n/* harmony default export */ __webpack_exports__[\"default\"] = (/*#__PURE__*/_c3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.memo)(UnifiedHeader));\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"RealTimeNotifications\");\n$RefreshReg$(_c1, \"ColorSchemeToggle\");\n$RefreshReg$(_c2, \"UnifiedHeader\");\n$RefreshReg$(_c3, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UnifiedHeader.tsx\n"));

/***/ })

});