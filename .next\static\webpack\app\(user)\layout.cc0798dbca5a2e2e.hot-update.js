"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(user)/layout",{

/***/ "(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx":
/*!**************************************************!*\
  !*** ./src/components/chatbot/ChatInterface.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AlertProvider */ \"(app-pages-browser)/./src/components/AlertProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatInterface(param) {\n    var _this = this;\n    var isOpen = param.isOpen, onClose = param.onClose, documentContext = param.documentContext;\n    _s();\n    var _useSession = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)(), session = _useSession.data;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var showAlert = (0,_components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__.useAlert)().showAlert;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), messages = _useState[0], setMessages = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), inputMessage = _useState1[0], setInputMessage = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isLoading = _useState2[0], setIsLoading = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), sessionId = _useState3[0], setSessionId = _useState3[1];\n    var _useState4 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isTyping = _useState4[0], setIsTyping = _useState4[1];\n    var messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        messages\n    ]);\n    // Focus input when chat opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isOpen && inputRef.current) {\n            inputRef.current.focus();\n        }\n    }, [\n        isOpen\n    ]);\n    // Start chat session when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isOpen && !sessionId && (session === null || session === void 0 ? void 0 : session.user)) {\n            // Always start a fresh session instead of trying to restore\n            // This ensures compatibility with browser restarts and auth session changes\n            console.log(\"Starting fresh chat session for user:\", session.user.name);\n            // Clear any old session data to prevent conflicts\n            localStorage.removeItem(\"chatbot_session_id\");\n            localStorage.removeItem(\"chatbot_session_time\");\n            startChatSession();\n        }\n    }, [\n        isOpen,\n        session\n    ]);\n    // Update document context when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (sessionId && documentContext) {\n            updateDocumentContext();\n        }\n    }, [\n        sessionId,\n        documentContext\n    ]);\n    var startChatSession = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var response, errorData, data, _session_user, welcomeMessage, error, fallbackMessage;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            5,\n                            6,\n                            7\n                        ]);\n                        setIsLoading(true);\n                        console.log(\"Starting chat session...\");\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"start_session\",\n                                    context: {\n                                        currentPage: pathname,\n                                        systemState: {\n                                            timestamp: new Date().toISOString()\n                                        }\n                                    }\n                                })\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!!response.ok) return [\n                            3,\n                            3\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        errorData = _state.sent();\n                        console.error(\"Failed to start chat session:\", errorData);\n                        throw new Error(errorData.message || \"Failed to start chat session\");\n                    case 3:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 4:\n                        data = _state.sent();\n                        console.log(\"Chat session started:\", data);\n                        setSessionId(data.data.sessionId);\n                        // If no messages returned, add a welcome message\n                        if (!data.data.messages || data.data.messages.length === 0) {\n                            ;\n                            welcomeMessage = {\n                                id: \"welcome_\".concat(Date.now()),\n                                role: \"assistant\",\n                                content: \"Hello \".concat((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || \"there\", \"! \\uD83D\\uDC4B\\n\\nI'm MGB Bot, your AI assistant for the Document Tracker system. I can help you with:\\n\\n• **Document workflows** - How to send, receive, and process documents\\n• **System navigation** - Finding features and pages\\n• **Status explanations** - Understanding document statuses\\n• **Troubleshooting** - Solving common issues\\n\\nWhat would you like to know?\"),\n                                timestamp: new Date(),\n                                quickReplies: [\n                                    \"How to send documents?\",\n                                    \"Check my inbox\",\n                                    \"Document workflow\",\n                                    \"System help\"\n                                ]\n                            };\n                            setMessages([\n                                welcomeMessage\n                            ]);\n                        } else {\n                            setMessages(data.data.messages);\n                        }\n                        return [\n                            3,\n                            7\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        console.error(\"Error starting chat session:\", error);\n                        // Show fallback welcome message even if session creation fails\n                        fallbackMessage = {\n                            id: \"fallback_\".concat(Date.now()),\n                            role: \"assistant\",\n                            content: \"Hello! \\uD83D\\uDC4B\\n\\nI'm MGB Bot, your AI assistant. I'm having trouble connecting to the server right now, but I can still help you with basic information about the Document Tracker system.\\n\\nTry typing a message and I'll do my best to assist you!\",\n                            timestamp: new Date(),\n                            quickReplies: [\n                                \"How to send documents?\",\n                                \"Check my inbox\",\n                                \"Document workflow\"\n                            ]\n                        };\n                        setMessages([\n                            fallbackMessage\n                        ]);\n                        showAlert({\n                            message: \"Chat session started in offline mode\",\n                            type: \"warning\"\n                        });\n                        return [\n                            3,\n                            7\n                        ];\n                    case 6:\n                        setIsLoading(false);\n                        return [\n                            7\n                        ];\n                    case 7:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function startChatSession() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var updateDocumentContext = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!sessionId || !documentContext) return [\n                            2\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"update_document_context\",\n                                    sessionId: sessionId,\n                                    documentData: documentContext\n                                })\n                            })\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error updating document context:\", error);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function updateDocumentContext() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var sendMessage = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function(message) {\n            var userMessage, response, errorData, data, error, _error_message;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!message.trim() || isLoading) return [\n                            2\n                        ];\n                        if (!!sessionId) return [\n                            3,\n                            2\n                        ];\n                        showAlert({\n                            message: \"Starting chat session...\",\n                            type: \"info\"\n                        });\n                        return [\n                            4,\n                            startChatSession()\n                        ];\n                    case 1:\n                        _state.sent();\n                        // Wait a bit for session to be created, then try again\n                        setTimeout(function() {\n                            if (sessionId) {\n                                sendMessage(message);\n                            }\n                        }, 1000);\n                        return [\n                            2\n                        ];\n                    case 2:\n                        userMessage = {\n                            id: \"temp_\".concat(Date.now()),\n                            role: \"user\",\n                            content: message,\n                            timestamp: new Date()\n                        };\n                        setMessages(function(prev) {\n                            return (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(prev).concat([\n                                userMessage\n                            ]);\n                        });\n                        setInputMessage(\"\");\n                        setIsLoading(true);\n                        setIsTyping(true);\n                        _state.label = 3;\n                    case 3:\n                        _state.trys.push([\n                            3,\n                            8,\n                            9,\n                            10\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"send_message\",\n                                    sessionId: sessionId,\n                                    message: message,\n                                    context: {\n                                        currentPage: pathname\n                                    }\n                                })\n                            })\n                        ];\n                    case 4:\n                        response = _state.sent();\n                        if (!!response.ok) return [\n                            3,\n                            6\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 5:\n                        errorData = _state.sent();\n                        // Handle session expiration\n                        if (response.status === 410 && errorData.error === \"SESSION_EXPIRED\") {\n                            showAlert({\n                                message: \"Chat session expired. Starting a new conversation...\",\n                                type: \"warning\"\n                            });\n                            // Clear current session and start a new one\n                            setSessionId(null);\n                            setMessages([]);\n                            // Clear localStorage\n                            localStorage.removeItem(\"chatbot_session_id\");\n                            localStorage.removeItem(\"chatbot_session_time\");\n                            // Restart the session\n                            setTimeout(function() {\n                                if (session === null || session === void 0 ? void 0 : session.user) {\n                                    startChatSession();\n                                }\n                            }, 1000);\n                            return [\n                                2\n                            ];\n                        }\n                        throw new Error(errorData.message || \"Failed to send message\");\n                    case 6:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 7:\n                        data = _state.sent();\n                        setMessages(data.data.messages);\n                        return [\n                            3,\n                            10\n                        ];\n                    case 8:\n                        error = _state.sent();\n                        console.error(\"Error sending message:\", error);\n                        // Don't show error for session expiration as we handle it above\n                        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"session expired\"))) {\n                            showAlert({\n                                message: error.message || \"Failed to send message\",\n                                type: \"error\"\n                            });\n                        }\n                        return [\n                            3,\n                            10\n                        ];\n                    case 9:\n                        setIsLoading(false);\n                        setIsTyping(false);\n                        return [\n                            7\n                        ];\n                    case 10:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function sendMessage(message) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var handleQuickReply = function(reply) {\n        sendMessage(reply);\n    };\n    var handleAction = function(action) {\n        switch(action.type){\n            case \"navigate\":\n                var _action_data;\n                if ((_action_data = action.data) === null || _action_data === void 0 ? void 0 : _action_data.url) {\n                    window.location.href = action.data.url;\n                }\n                break;\n            case \"search\":\n                // Implement search functionality\n                console.log(\"Search action:\", action.data);\n                break;\n            case \"help\":\n                // Show help modal or navigate to help\n                console.log(\"Help action:\", action.data);\n                break;\n            default:\n                console.log(\"Unknown action:\", action);\n        }\n    };\n    var formatMessage = function(content) {\n        // Handle undefined, null, or empty content\n        if (!content || typeof content !== \"string\") {\n            return \"<div>No content available</div>\";\n        }\n        // Split content by lines and format each line separately to avoid nesting issues\n        var lines = content.split(\"\\n\");\n        var formattedLines = lines.map(function(line) {\n            return line.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\").replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n        });\n        // Join with div elements instead of br tags to avoid nesting issues\n        return formattedLines.map(function(line) {\n            return line.trim() ? \"<div>\".concat(line, \"</div>\") : \"<div>&nbsp;</div>\";\n        }).join(\"\");\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-end justify-end p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black bg-opacity-25\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-96 h-[600px] flex flex-col border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-lg\",\n                                                    children: \"\\uD83E\\uDD16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-white\",\n                                                children: \"MGB Bot\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"Typing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\",\n                                                                    style: {\n                                                                        animationDelay: \"0.1s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\",\n                                                                    style: {\n                                                                        animationDelay: \"0.2s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this) : \"AI Assistant • Online\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-white hover:text-blue-200 transition-all duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-20 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50\",\n                                title: \"Close MGB Bot\",\n                                \"aria-label\": \"Close chatbot\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2.5,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                        children: [\n                            messages.map(function(message) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                    children: [\n                                        message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"\\uD83E\\uDD16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm \".concat(message.role === \"user\" ? \"bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md\" : \"bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 rounded-bl-md\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"prose prose-sm max-w-none\",\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: formatMessage(message.content)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                message.actions && message.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 space-y-2\",\n                                                    children: message.actions.map(function(action, index) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: function() {\n                                                                return handleAction(action);\n                                                            },\n                                                            className: \"flex items-center justify-center w-full px-3 py-2 text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1\",\n                                                                    children: \"\\uD83D\\uDD17\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                action.label\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                message.quickReplies && message.quickReplies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2\",\n                                                    children: message.quickReplies.map(function(reply, index) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: function() {\n                                                                return handleQuickReply(reply);\n                                                            },\n                                                            className: \"px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-sm\",\n                                                            children: [\n                                                                \"\\uD83D\\uDCAC \",\n                                                                reply\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, _this);\n                            }),\n                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"\\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                    children: \"MGB Bot is thinking\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 ml-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.1s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.2s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: inputRef,\n                                                type: \"text\",\n                                                value: inputMessage,\n                                                onChange: function(e) {\n                                                    return setInputMessage(e.target.value);\n                                                },\n                                                onKeyDown: function(e) {\n                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                        e.preventDefault();\n                                                        sendMessage(inputMessage);\n                                                    }\n                                                },\n                                                placeholder: \"Type your message...\",\n                                                disabled: isLoading,\n                                                className: \"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return sendMessage(inputMessage);\n                                        },\n                                        disabled: isLoading || !inputMessage.trim(),\n                                        className: \"px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-sm\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 flex flex-wrap gap-2\",\n                                children: [\n                                    \"How to send documents?\",\n                                    \"Check my inbox\",\n                                    \"Document workflow\"\n                                ].map(function(suggestion, index) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return sendMessage(suggestion);\n                                        },\n                                        disabled: isLoading,\n                                        className: \"px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-full transition-colors duration-200 disabled:opacity-50\",\n                                        children: suggestion\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"V6NTTLWUzvNZRus1A3jSe2LATHk=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__.useAlert\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx\n"));

/***/ })

});