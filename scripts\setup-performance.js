#!/usr/bin/env node

/**
 * Performance Setup Script
 * This script sets up database indexes and performance optimizations
 * for the Document Tracker system.
 */

const mongoose = require('mongoose');

// MongoDB connection
async function connectToDatabase() {
  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/document-tracker';

  try {
    await mongoose.connect(MONGODB_URI, {
      bufferCommands: false,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 30000,
      maxPoolSize: 20,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      retryWrites: true,
      retryReads: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    throw error;
  }
}

// Create performance indexes with error handling
async function createPerformanceIndexes() {
  const db = mongoose.connection.db;

  console.log('🚀 Creating performance indexes...');

  const indexes = [
    // Notification indexes
    {
      collection: 'notifications',
      index: { userId: 1, createdAt: -1 },
      options: { name: 'user_notifications_idx', background: true }
    },
    {
      collection: 'notifications',
      index: { userId: 1, isRead: 1, createdAt: -1 },
      options: { name: 'user_unread_notifications_idx', background: true }
    },
    // Smart notification indexes
    {
      collection: 'smartnotifications',
      index: { userId: 1, createdAt: -1 },
      options: { name: 'user_smart_notifications_idx', background: true }
    },
    {
      collection: 'smartnotifications',
      index: { userId: 1, isRead: 1, createdAt: -1 },
      options: { name: 'user_unread_smart_notifications_idx', background: true }
    },
    // Document indexes
    {
      collection: 'documents',
      index: { createdBy: 1, createdAt: -1 },
      options: { name: 'user_documents_idx', background: true }
    },
    {
      collection: 'documents',
      index: { status: 1, createdAt: -1 },
      options: { name: 'status_documents_idx', background: true }
    },
    // User indexes
    {
      collection: 'users',
      index: { email: 1 },
      options: { name: 'email_idx', unique: true, background: true }
    }
  ];

  let created = 0;
  let skipped = 0;

  for (const { collection, index, options } of indexes) {
    try {
      const coll = db.collection(collection);
      await coll.createIndex(index, options);
      console.log(`✅ Created index ${options.name} on ${collection}`);
      created++;
    } catch (error) {
      if (error.code === 85 || error.codeName === 'IndexOptionsConflict') {
        console.log(`⚠️  Index ${options.name} already exists on ${collection}`);
        skipped++;
      } else {
        console.error(`❌ Error creating index ${options.name} on ${collection}:`, error.message);
      }
    }
  }

  console.log(`\n📊 Index Summary: ${created} created, ${skipped} skipped`);
  console.log('✅ Performance indexes setup completed!');
}

// Get index statistics
async function getIndexStats() {
  const db = mongoose.connection.db;
  const collections = ['notifications', 'smartnotifications', 'documents', 'users'];
  const stats = {};

  for (const collectionName of collections) {
    try {
      const collection = db.collection(collectionName);
      const indexes = await collection.listIndexes().toArray();
      const collStats = await db.command({ collStats: collectionName });

      stats[collectionName] = {
        indexCount: indexes.length,
        indexes: indexes.map(idx => ({
          name: idx.name,
          keys: idx.key,
          unique: idx.unique || false
        })),
        totalSize: collStats.size,
        totalIndexSize: collStats.totalIndexSize,
        avgObjSize: collStats.avgObjSize
      };
    } catch (error) {
      stats[collectionName] = { error: 'Collection not found or error occurred' };
    }
  }

  return stats;
}

async function setupPerformance() {
  console.log('🚀 Setting up performance optimizations...\n');

  try {
    // Connect to database
    await connectToDatabase();

    // Create database indexes
    console.log('📊 Creating database indexes...');
    await createPerformanceIndexes();
    console.log('✅ Database indexes created successfully!\n');

    // Get and display index statistics
    console.log('📈 Index Statistics:');
    const stats = await getIndexStats();

    Object.entries(stats).forEach(([collection, data]) => {
      console.log(`\n📁 ${collection.toUpperCase()}:`);
      console.log(`   Documents: ${data.totalSize ? Math.round(data.totalSize / 1024) + ' KB' : 'N/A'}`);
      console.log(`   Indexes: ${data.indexCount}`);
      console.log(`   Index Size: ${data.totalIndexSize ? Math.round(data.totalIndexSize / 1024) + ' KB' : 'N/A'}`);

      if (data.indexes && data.indexes.length > 0) {
        console.log('   Index Details:');
        data.indexes.forEach(idx => {
          const keys = Object.keys(idx.keys).join(', ');
          const unique = idx.unique ? ' (unique)' : '';
          console.log(`     - ${idx.name}: ${keys}${unique}`);
        });
      }
    });

    console.log('\n🎉 Performance setup completed successfully!');
    console.log('\n📋 Performance Recommendations:');
    console.log('   1. Monitor API response times regularly');
    console.log('   2. Use the /api/performance endpoint to track metrics');
    console.log('   3. Consider adding more indexes if queries become slow');
    console.log('   4. Enable MongoDB profiling for production monitoring');
    console.log('   5. Set up proper caching strategies for frequently accessed data');

  } catch (error) {
    console.error('❌ Error setting up performance optimizations:', error);
    process.exit(1);
  } finally {
    // Close database connection
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupPerformance()
    .then(() => {
      console.log('\n✨ Setup complete! Your system is now optimized for better performance.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Setup failed:', error);
      process.exit(1);
    });
}

module.exports = { setupPerformance };
