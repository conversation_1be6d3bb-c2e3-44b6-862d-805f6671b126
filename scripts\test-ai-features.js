/**
 * AI Features Diagnostic Script
 * Tests all AI components and provides troubleshooting information
 */

require('dotenv').config({ path: '.env.local' });

console.log('🤖 AI Features Diagnostic Tool');
console.log('================================');
console.log('');

// Test 1: Check Environment Variables
console.log('📋 Step 1: Checking Environment Variables...');
const geminiApiKey = process.env.GEMINI_API_KEY;

if (!geminiApiKey) {
  console.log('❌ GEMINI_API_KEY not found in environment variables');
  console.log('');
  console.log('🔧 Fix: Add your Gemini API key to .env.local:');
  console.log('   GEMINI_API_KEY=your_api_key_here');
  console.log('');
  console.log('📖 Get your API key from: https://makersuite.google.com/app/apikey');
  process.exit(1);
} else {
  console.log('✅ GEMINI_API_KEY found');
  console.log(`   Key: ${geminiApiKey.substring(0, 10)}...${geminiApiKey.substring(geminiApiKey.length - 4)}`);
}

// Test 2: Test Gemini API Connection
console.log('');
console.log('🔌 Step 2: Testing Gemini API Connection...');

async function testGeminiAPI() {
  try {
    const apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
    
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: 'Hello! Please respond with "API connection successful" to confirm the connection is working.'
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 100
      }
    };

    console.log('   Making test request to Gemini API...');
    
    const response = await fetch(`${apiUrl}?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Gemini API request failed');
      console.log(`   Status: ${response.status} ${response.statusText}`);
      console.log(`   Error: ${errorText}`);
      
      if (response.status === 400) {
        console.log('');
        console.log('🔧 Possible fixes:');
        console.log('   1. Check if your API key is valid');
        console.log('   2. Ensure Gemini Pro API is enabled for your key');
        console.log('   3. Verify you have API quota remaining');
      } else if (response.status === 403) {
        console.log('');
        console.log('🔧 Possible fixes:');
        console.log('   1. Your API key may be invalid or expired');
        console.log('   2. Generate a new API key from Google AI Studio');
        console.log('   3. Check if the Gemini API is enabled for your project');
      }
      
      return false;
    }

    const data = await response.json();
    
    if (!data.candidates || data.candidates.length === 0) {
      console.log('❌ No response from Gemini API');
      console.log('   Response:', JSON.stringify(data, null, 2));
      return false;
    }

    const responseText = data.candidates[0].content.parts[0].text;
    console.log('✅ Gemini API connection successful!');
    console.log(`   Response: ${responseText}`);
    
    return true;
  } catch (error) {
    console.log('❌ Error testing Gemini API:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('');
      console.log('🔧 Network issue detected:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify firewall settings');
      console.log('   3. Try again in a few minutes');
    }
    
    return false;
  }
}

// Test 3: Test AI Services
async function testAIServices() {
  console.log('');
  console.log('🧠 Step 3: Testing AI Services...');
  
  try {
    // Import the Gemini client
    const { geminiClient } = require('../src/lib/gemini/core/geminiClient');
    
    console.log('   Testing Gemini client availability...');
    const isAvailable = geminiClient.isAvailable();
    
    if (isAvailable) {
      console.log('✅ Gemini client is available');
      
      // Test a simple generation
      try {
        console.log('   Testing content generation...');
        const testResponse = await geminiClient.generateContent('Say "Hello from Document Tracker AI!"');
        console.log('✅ Content generation successful');
        console.log(`   Response: ${testResponse}`);
      } catch (genError) {
        console.log('❌ Content generation failed:', genError.message);
      }
    } else {
      console.log('❌ Gemini client is not available');
    }
    
  } catch (importError) {
    console.log('❌ Error importing AI services:', importError.message);
    console.log('');
    console.log('🔧 This might indicate a code issue. Try:');
    console.log('   1. npm install');
    console.log('   2. Restart the application');
  }
}

// Test 4: Check AI Features Status
function checkAIFeaturesStatus() {
  console.log('');
  console.log('📊 Step 4: AI Features Status...');
  console.log('');
  
  const features = [
    { name: 'Intelligent Notifications', status: 'Available', description: 'AI-powered notification optimization' },
    { name: 'Smart Chatbot', status: 'Available', description: 'Document assistance chatbot' },
    { name: 'Behavior Analysis', status: 'Available', description: 'User behavior pattern analysis' },
    { name: 'Smart Reminders', status: 'Available', description: 'Intelligent reminder scheduling' },
    { name: 'Feedback Analysis', status: 'Available', description: 'AI-powered feedback suggestions' }
  ];
  
  features.forEach(feature => {
    console.log(`   ${feature.status === 'Available' ? '✅' : '❌'} ${feature.name}`);
    console.log(`      ${feature.description}`);
  });
}

// Main diagnostic function
async function runDiagnostics() {
  try {
    // Test API connection
    const apiWorking = await testGeminiAPI();
    
    // Test AI services
    await testAIServices();
    
    // Show features status
    checkAIFeaturesStatus();
    
    console.log('');
    console.log('🎯 Summary:');
    
    if (apiWorking) {
      console.log('✅ AI features should be working correctly!');
      console.log('');
      console.log('🚀 You can now use:');
      console.log('   • Chatbot (click the chat icon in the app)');
      console.log('   • Intelligent notifications');
      console.log('   • Smart document suggestions');
      console.log('   • Behavior analysis');
    } else {
      console.log('❌ AI features are not working properly');
      console.log('');
      console.log('🔧 Next steps:');
      console.log('   1. Get a new API key from: https://makersuite.google.com/app/apikey');
      console.log('   2. Update your .env.local file');
      console.log('   3. Restart the application');
      console.log('   4. Run this test again');
    }
    
  } catch (error) {
    console.log('💥 Diagnostic failed:', error.message);
  }
}

// Run diagnostics
if (require.main === module) {
  runDiagnostics();
}

module.exports = { runDiagnostics, testGeminiAPI };
