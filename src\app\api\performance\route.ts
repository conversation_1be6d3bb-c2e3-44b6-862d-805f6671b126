import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/options';
import { getIndexStats } from '@/lib/db/indexes';
import dbConnect from '@/lib/db/mongodb';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';

// Performance metrics storage
const performanceMetrics = {
  apiCalls: {
    total: 0,
    successful: 0,
    failed: 0,
    avgResponseTime: 0,
    slowestEndpoint: '',
    slowestTime: 0
  },
  database: {
    connections: 0,
    queries: 0,
    avgQueryTime: 0,
    slowestQuery: '',
    slowestQueryTime: 0
  },
  cache: {
    hits: 0,
    misses: 0,
    hitRate: 0
  },
  memory: {
    used: 0,
    total: 0,
    percentage: 0
  },
  lastReset: Date.now()
};

// GET /api/performance - Get system performance metrics
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    await dbConnect();

    // Get database statistics
    const dbStats = await getDatabaseStats();
    
    // Get index statistics
    const indexStats = await getIndexStats();
    
    // Get memory usage
    const memoryUsage = process.memoryUsage();
    
    // Update memory metrics
    performanceMetrics.memory = {
      used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
      total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
      percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
    };

    // Calculate cache hit rate
    const totalCacheRequests = performanceMetrics.cache.hits + performanceMetrics.cache.misses;
    performanceMetrics.cache.hitRate = totalCacheRequests > 0 
      ? Math.round((performanceMetrics.cache.hits / totalCacheRequests) * 100)
      : 0;

    // Get MongoDB connection stats
    const connectionStats = mongoose.connection.readyState === 1 ? {
      state: 'connected',
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    } : {
      state: 'disconnected'
    };

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      uptime: Math.round((Date.now() - performanceMetrics.lastReset) / 1000), // seconds
      metrics: performanceMetrics,
      database: {
        connection: connectionStats,
        stats: dbStats,
        indexes: indexStats
      },
      recommendations: generatePerformanceRecommendations(performanceMetrics, dbStats)
    });

  } catch (error: any) {
    console.error('Error fetching performance metrics:', error);
    return NextResponse.json(
      { message: error.message || 'Failed to fetch performance metrics' },
      { status: 500 }
    );
  }
}

// POST /api/performance/reset - Reset performance metrics
export async function POST() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Reset metrics
    performanceMetrics.apiCalls = {
      total: 0,
      successful: 0,
      failed: 0,
      avgResponseTime: 0,
      slowestEndpoint: '',
      slowestTime: 0
    };
    
    performanceMetrics.database = {
      connections: 0,
      queries: 0,
      avgQueryTime: 0,
      slowestQuery: '',
      slowestQueryTime: 0
    };
    
    performanceMetrics.cache = {
      hits: 0,
      misses: 0,
      hitRate: 0
    };
    
    performanceMetrics.lastReset = Date.now();

    return NextResponse.json({
      success: true,
      message: 'Performance metrics reset successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('Error resetting performance metrics:', error);
    return NextResponse.json(
      { message: error.message || 'Failed to reset performance metrics' },
      { status: 500 }
    );
  }
}

// Helper function to get database statistics
async function getDatabaseStats() {
  try {
    const db = mongoose.connection.db;
    
    // Get database stats
    const dbStats = await db.admin().serverStatus();
    
    // Get collection stats
    const collections = ['documents', 'notifications', 'smartnotifications', 'users'];
    const collectionStats: Record<string, any> = {};
    
    for (const collectionName of collections) {
      try {
        const stats = await db.command({ collStats: collectionName });
        collectionStats[collectionName] = {
          count: stats.count,
          size: Math.round(stats.size / 1024), // KB
          avgObjSize: Math.round(stats.avgObjSize),
          indexSize: Math.round(stats.totalIndexSize / 1024) // KB
        };
      } catch (error) {
        // Collection might not exist
        collectionStats[collectionName] = { count: 0, size: 0, avgObjSize: 0, indexSize: 0 };
      }
    }

    return {
      version: dbStats.version,
      uptime: dbStats.uptime,
      connections: {
        current: dbStats.connections?.current || 0,
        available: dbStats.connections?.available || 0,
        totalCreated: dbStats.connections?.totalCreated || 0
      },
      opcounters: dbStats.opcounters,
      collections: collectionStats,
      memory: {
        resident: Math.round(dbStats.mem?.resident || 0), // MB
        virtual: Math.round(dbStats.mem?.virtual || 0), // MB
        mapped: Math.round(dbStats.mem?.mapped || 0) // MB
      }
    };

  } catch (error) {
    console.error('Error getting database stats:', error);
    return {
      error: 'Failed to fetch database statistics',
      collections: {}
    };
  }
}

// Helper function to generate performance recommendations
function generatePerformanceRecommendations(metrics: any, dbStats: any) {
  const recommendations: string[] = [];

  // API performance recommendations
  if (metrics.apiCalls.avgResponseTime > 1000) {
    recommendations.push('API response times are high (>1s). Consider optimizing database queries and adding caching.');
  }

  if (metrics.apiCalls.failed / metrics.apiCalls.total > 0.05) {
    recommendations.push('High API failure rate (>5%). Check error logs and improve error handling.');
  }

  // Cache recommendations
  if (metrics.cache.hitRate < 70) {
    recommendations.push('Low cache hit rate (<70%). Consider increasing cache TTL or improving cache strategy.');
  }

  // Memory recommendations
  if (metrics.memory.percentage > 80) {
    recommendations.push('High memory usage (>80%). Consider optimizing memory usage or increasing server resources.');
  }

  // Database recommendations
  if (dbStats.connections?.current > dbStats.connections?.available * 0.8) {
    recommendations.push('High database connection usage. Consider connection pooling optimization.');
  }

  if (metrics.database.avgQueryTime > 100) {
    recommendations.push('Slow database queries (>100ms). Consider adding indexes or optimizing queries.');
  }

  // Default recommendation if everything looks good
  if (recommendations.length === 0) {
    recommendations.push('System performance looks good! Continue monitoring for optimal performance.');
  }

  return recommendations;
}

// Export function to update metrics from other parts of the application
export function updatePerformanceMetrics(type: string, data: any) {
  switch (type) {
    case 'api_call':
      performanceMetrics.apiCalls.total++;
      if (data.success) {
        performanceMetrics.apiCalls.successful++;
      } else {
        performanceMetrics.apiCalls.failed++;
      }
      
      // Update average response time
      const newAvg = (performanceMetrics.apiCalls.avgResponseTime * (performanceMetrics.apiCalls.total - 1) + data.responseTime) / performanceMetrics.apiCalls.total;
      performanceMetrics.apiCalls.avgResponseTime = Math.round(newAvg);
      
      // Track slowest endpoint
      if (data.responseTime > performanceMetrics.apiCalls.slowestTime) {
        performanceMetrics.apiCalls.slowestTime = data.responseTime;
        performanceMetrics.apiCalls.slowestEndpoint = data.endpoint;
      }
      break;
      
    case 'cache_hit':
      performanceMetrics.cache.hits++;
      break;
      
    case 'cache_miss':
      performanceMetrics.cache.misses++;
      break;
      
    case 'db_query':
      performanceMetrics.database.queries++;
      
      // Update average query time
      const queryAvg = (performanceMetrics.database.avgQueryTime * (performanceMetrics.database.queries - 1) + data.queryTime) / performanceMetrics.database.queries;
      performanceMetrics.database.avgQueryTime = Math.round(queryAvg);
      
      // Track slowest query
      if (data.queryTime > performanceMetrics.database.slowestQueryTime) {
        performanceMetrics.database.slowestQueryTime = data.queryTime;
        performanceMetrics.database.slowestQuery = data.query;
      }
      break;
  }
}
