{"version": 3, "file": "debug.umd.js", "sources": ["../src/check-props.js", "../src/component-stack.js", "../src/debug.js", "../src/constants.js", "../src/util.js", "../src/index.js"], "sourcesContent": ["const ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nlet loggedTypeFailures = {};\n\n/**\n * Reset the history of which prop type warnings have been logged.\n */\nexport function resetPropWarnings() {\n\tloggedTypeFailures = {};\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * Adapted from https://github.com/facebook/prop-types/blob/master/checkPropTypes.js\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n */\nexport function checkPropTypes(\n\ttypeSpecs,\n\tvalues,\n\tlocation,\n\tcomponentName,\n\tgetStack\n) {\n\tObject.keys(typeSpecs).forEach(typeSpecName => {\n\t\tlet error;\n\t\ttry {\n\t\t\terror = typeSpecs[typeSpecName](\n\t\t\t\tvalues,\n\t\t\t\ttypeSpecName,\n\t\t\t\tcomponentName,\n\t\t\t\tlocation,\n\t\t\t\tnull,\n\t\t\t\tReactPropTypesSecret\n\t\t\t);\n\t\t} catch (e) {\n\t\t\terror = e;\n\t\t}\n\t\tif (error && !(error.message in loggedTypeFailures)) {\n\t\t\tloggedTypeFailures[error.message] = true;\n\t\t\tconsole.error(\n\t\t\t\t`Failed ${location} type: ${error.message}${(getStack &&\n\t\t\t\t\t`\\n${getStack()}`) ||\n\t\t\t\t\t''}`\n\t\t\t);\n\t\t}\n\t});\n}\n", "import { options, Fragment } from 'preact';\n\n/**\n * Get human readable name of the component/dom node\n * @param {import('./internal').VNode} vnode\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function getDisplayName(vnode) {\n\tif (vnode.type === Fragment) {\n\t\treturn 'Fragment';\n\t} else if (typeof vnode.type == 'function') {\n\t\treturn vnode.type.displayName || vnode.type.name;\n\t} else if (typeof vnode.type == 'string') {\n\t\treturn vnode.type;\n\t}\n\n\treturn '#text';\n}\n\n/**\n * Used to keep track of the currently rendered `vnode` and print it\n * in debug messages.\n */\nlet renderStack = [];\n\n/**\n * Keep track of the current owners. An owner describes a component\n * which was responsible to render a specific `vnode`. This exclude\n * children that are passed via `props.children`, because they belong\n * to the parent owner.\n *\n * ```jsx\n * const Foo = props => <div>{props.children}</div> // div's owner is Foo\n * const Bar = props => {\n *   return (\n *     <Foo><span /></Foo> // Foo's owner is Bar, span's owner is Bar\n *   )\n * }\n * ```\n *\n * Note: A `vnode` may be hoisted to the root scope due to compiler\n * optimiztions. In these cases the `_owner` will be different.\n */\nlet ownerStack = [];\n\n/**\n * Get the currently rendered `vnode`\n * @returns {import('./internal').VNode | null}\n */\nexport function getCurrentVNode() {\n\treturn renderStack.length > 0 ? renderStack[renderStack.length - 1] : null;\n}\n\n/**\n * If the user doesn't have `@babel/plugin-transform-react-jsx-source`\n * somewhere in his tool chain we can't print the filename and source\n * location of a component. In that case we just omit that, but we'll\n * print a helpful message to the console, notifying the user of it.\n */\nlet hasBabelPlugin = false;\n\n/**\n * Check if a `vnode` is a possible owner.\n * @param {import('./internal').VNode} vnode\n */\nfunction isPossibleOwner(vnode) {\n\treturn typeof vnode.type == 'function' && vnode.type != Fragment;\n}\n\n/**\n * Return the component stack that was captured up to this point.\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function getOwnerStack(vnode) {\n\tconst stack = [vnode];\n\tlet next = vnode;\n\twhile (next._owner != null) {\n\t\tstack.push(next._owner);\n\t\tnext = next._owner;\n\t}\n\n\treturn stack.reduce((acc, owner) => {\n\t\tacc += `  in ${getDisplayName(owner)}`;\n\n\t\tconst source = owner.__source;\n\t\tif (source) {\n\t\t\tacc += ` (at ${source.fileName}:${source.lineNumber})`;\n\t\t} else if (!hasBabelPlugin) {\n\t\t\thasBabelPlugin = true;\n\t\t\tconsole.warn(\n\t\t\t\t'Add @babel/plugin-transform-react-jsx-source to get a more detailed component stack. Note that you should not add it to production builds of your App for bundle size reasons.'\n\t\t\t);\n\t\t}\n\n\t\treturn (acc += '\\n');\n\t}, '');\n}\n\n/**\n * Setup code to capture the component trace while rendering. Note that\n * we cannot simply traverse `vnode._parent` upwards, because we have some\n * debug messages for `this.setState` where the `vnode` is `undefined`.\n */\nexport function setupComponentStack() {\n\tlet oldDiff = options._diff;\n\tlet oldDiffed = options.diffed;\n\tlet oldRoot = options._root;\n\tlet oldVNode = options.vnode;\n\tlet oldRender = options._render;\n\n\toptions.diffed = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\townerStack.pop();\n\t\t}\n\t\trenderStack.pop();\n\t\tif (oldDiffed) oldDiffed(vnode);\n\t};\n\n\toptions._diff = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\trenderStack.push(vnode);\n\t\t}\n\t\tif (oldDiff) oldDiff(vnode);\n\t};\n\n\toptions._root = (vnode, parent) => {\n\t\townerStack = [];\n\t\tif (oldRoot) oldRoot(vnode, parent);\n\t};\n\n\toptions.vnode = vnode => {\n\t\tvnode._owner =\n\t\t\townerStack.length > 0 ? ownerStack[ownerStack.length - 1] : null;\n\t\tif (oldVNode) oldVNode(vnode);\n\t};\n\n\toptions._render = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\townerStack.push(vnode);\n\t\t}\n\n\t\tif (oldRender) oldRender(vnode);\n\t};\n}\n", "import { checkPropTypes } from './check-props';\nimport { options, Component } from 'preact';\nimport {\n\tELEMENT_NODE,\n\tDOCUMENT_NODE,\n\tDOCUMENT_FRAGMENT_NODE\n} from './constants';\nimport {\n\tgetOwnerStack,\n\tsetupComponentStack,\n\tgetCurrentVNode,\n\tgetDisplayName\n} from './component-stack';\nimport { assign } from './util';\n\nconst isWeakMapSupported = typeof WeakMap == 'function';\n\nfunction getClosestDomNodeParent(parent) {\n\tif (!parent) return {};\n\tif (typeof parent.type == 'function') {\n\t\treturn getClosestDomNodeParent(parent._parent);\n\t}\n\treturn parent;\n}\n\nexport function initDebug() {\n\tsetupComponentStack();\n\n\tlet hooksAllowed = false;\n\n\t/* eslint-disable no-console */\n\tlet oldBeforeDiff = options._diff;\n\tlet oldDiffed = options.diffed;\n\tlet oldVnode = options.vnode;\n\tlet oldCatchError = options._catchError;\n\tlet oldRoot = options._root;\n\tlet oldHook = options._hook;\n\tconst warnedComponents = !isWeakMapSupported\n\t\t? null\n\t\t: {\n\t\t\t\tuseEffect: new WeakMap(),\n\t\t\t\tuseLayoutEffect: new WeakMap(),\n\t\t\t\tlazyPropTypes: new WeakMap()\n\t\t  };\n\tconst deprecations = [];\n\n\toptions._catchError = (error, vnode, oldVNode, errorInfo) => {\n\t\tlet component = vnode && vnode._component;\n\t\tif (component && typeof error.then == 'function') {\n\t\t\tconst promise = error;\n\t\t\terror = new Error(\n\t\t\t\t`Missing Suspense. The throwing component was: ${getDisplayName(vnode)}`\n\t\t\t);\n\n\t\t\tlet parent = vnode;\n\t\t\tfor (; parent; parent = parent._parent) {\n\t\t\t\tif (parent._component && parent._component._childDidSuspend) {\n\t\t\t\t\terror = promise;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// We haven't recovered and we know at this point that there is no\n\t\t\t// Suspense component higher up in the tree\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\ttry {\n\t\t\terrorInfo = errorInfo || {};\n\t\t\terrorInfo.componentStack = getOwnerStack(vnode);\n\t\t\toldCatchError(error, vnode, oldVNode, errorInfo);\n\n\t\t\t// when an error was handled by an ErrorBoundary we will nontheless emit an error\n\t\t\t// event on the window object. This is to make up for react compatibility in dev mode\n\t\t\t// and thus make the Next.js dev overlay work.\n\t\t\tif (typeof error.then != 'function') {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthrow error;\n\t\t\t\t});\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tthrow e;\n\t\t}\n\t};\n\n\toptions._root = (vnode, parentNode) => {\n\t\tif (!parentNode) {\n\t\t\tthrow new Error(\n\t\t\t\t'Undefined parent passed to render(), this is the second argument.\\n' +\n\t\t\t\t\t'Check if the element is available in the DOM/has the correct id.'\n\t\t\t);\n\t\t}\n\n\t\tlet isValid;\n\t\tswitch (parentNode.nodeType) {\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\tcase DOCUMENT_NODE:\n\t\t\t\tisValid = true;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tisValid = false;\n\t\t}\n\n\t\tif (!isValid) {\n\t\t\tlet componentName = getDisplayName(vnode);\n\t\t\tthrow new Error(\n\t\t\t\t`Expected a valid HTML node as a second argument to render.\tReceived ${parentNode} instead: render(<${componentName} />, ${parentNode});`\n\t\t\t);\n\t\t}\n\n\t\tif (oldRoot) oldRoot(vnode, parentNode);\n\t};\n\n\toptions._diff = vnode => {\n\t\tlet { type, _parent: parent } = vnode;\n\t\tlet parentVNode = getClosestDomNodeParent(parent);\n\n\t\thooksAllowed = true;\n\n\t\tif (type === undefined) {\n\t\t\tthrow new Error(\n\t\t\t\t'Undefined component passed to createElement()\\n\\n' +\n\t\t\t\t\t'You likely forgot to export your component or might have mixed up default and named imports' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (type != null && typeof type == 'object') {\n\t\t\tif (type._children !== undefined && type._dom !== undefined) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Invalid type passed to createElement(): ${type}\\n\\n` +\n\t\t\t\t\t\t'Did you accidentally pass a JSX literal as JSX twice?\\n\\n' +\n\t\t\t\t\t\t`  let My${getDisplayName(vnode)} = ${serializeVNode(type)};\\n` +\n\t\t\t\t\t\t`  let vnode = <My${getDisplayName(vnode)} />;\\n\\n` +\n\t\t\t\t\t\t'This usually happens when you export a JSX literal and not the component.' +\n\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthrow new Error(\n\t\t\t\t'Invalid type passed to createElement(): ' +\n\t\t\t\t\t(Array.isArray(type) ? 'array' : type)\n\t\t\t);\n\t\t}\n\n\t\tif (\n\t\t\t(type === 'thead' || type === 'tfoot' || type === 'tbody') &&\n\t\t\tparentVNode.type !== 'table'\n\t\t) {\n\t\t\tconsole.error(\n\t\t\t\t'Improper nesting of table. Your <thead/tbody/tfoot> should have a <table> parent.' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (\n\t\t\ttype === 'tr' &&\n\t\t\tparentVNode.type !== 'thead' &&\n\t\t\tparentVNode.type !== 'tfoot' &&\n\t\t\tparentVNode.type !== 'tbody' &&\n\t\t\tparentVNode.type !== 'table'\n\t\t) {\n\t\t\tconsole.error(\n\t\t\t\t'Improper nesting of table. Your <tr> should have a <thead/tbody/tfoot/table> parent.' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (type === 'td' && parentVNode.type !== 'tr') {\n\t\t\tconsole.error(\n\t\t\t\t'Improper nesting of table. Your <td> should have a <tr> parent.' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (type === 'th' && parentVNode.type !== 'tr') {\n\t\t\tconsole.error(\n\t\t\t\t'Improper nesting of table. Your <th> should have a <tr>.' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t}\n\n\t\tif (\n\t\t\tvnode.ref !== undefined &&\n\t\t\ttypeof vnode.ref != 'function' &&\n\t\t\ttypeof vnode.ref != 'object' &&\n\t\t\t!('$$typeof' in vnode) // allow string refs when preact-compat is installed\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t`Component's \"ref\" property should be a function, or an object created ` +\n\t\t\t\t\t`by createRef(), but got [${typeof vnode.ref}] instead\\n` +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t}\n\n\t\tif (typeof vnode.type == 'string') {\n\t\t\tfor (const key in vnode.props) {\n\t\t\t\tif (\n\t\t\t\t\tkey[0] === 'o' &&\n\t\t\t\t\tkey[1] === 'n' &&\n\t\t\t\t\ttypeof vnode.props[key] != 'function' &&\n\t\t\t\t\tvnode.props[key] != null\n\t\t\t\t) {\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`Component's \"${key}\" property should be a function, ` +\n\t\t\t\t\t\t\t`but got [${typeof vnode.props[key]}] instead\\n` +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Check prop-types if available\n\t\tif (typeof vnode.type == 'function' && vnode.type.propTypes) {\n\t\t\tif (\n\t\t\t\tvnode.type.displayName === 'Lazy' &&\n\t\t\t\twarnedComponents &&\n\t\t\t\t!warnedComponents.lazyPropTypes.has(vnode.type)\n\t\t\t) {\n\t\t\t\tconst m =\n\t\t\t\t\t'PropTypes are not supported on lazy(). Use propTypes on the wrapped component itself. ';\n\t\t\t\ttry {\n\t\t\t\t\tconst lazyVNode = vnode.type();\n\t\t\t\t\twarnedComponents.lazyPropTypes.set(vnode.type, true);\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\tm + `Component wrapped in lazy() is ${getDisplayName(lazyVNode)}`\n\t\t\t\t\t);\n\t\t\t\t} catch (promise) {\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\tm + \"We will log the wrapped component's name once it is loaded.\"\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet values = vnode.props;\n\t\t\tif (vnode.type._forwarded) {\n\t\t\t\tvalues = assign({}, values);\n\t\t\t\tdelete values.ref;\n\t\t\t}\n\n\t\t\tcheckPropTypes(\n\t\t\t\tvnode.type.propTypes,\n\t\t\t\tvalues,\n\t\t\t\t'prop',\n\t\t\t\tgetDisplayName(vnode),\n\t\t\t\t() => getOwnerStack(vnode)\n\t\t\t);\n\t\t}\n\n\t\tif (oldBeforeDiff) oldBeforeDiff(vnode);\n\t};\n\n\toptions._hook = (comp, index, type) => {\n\t\tif (!comp || !hooksAllowed) {\n\t\t\tthrow new Error('Hook can only be invoked from render methods.');\n\t\t}\n\n\t\tif (oldHook) oldHook(comp, index, type);\n\t};\n\n\t// Ideally we'd want to print a warning once per component, but we\n\t// don't have access to the vnode that triggered it here. As a\n\t// compromise and to avoid flooding the console with warnings we\n\t// print each deprecation warning only once.\n\tconst warn = (property, message) => ({\n\t\tget() {\n\t\t\tconst key = 'get' + property + message;\n\t\t\tif (deprecations && deprecations.indexOf(key) < 0) {\n\t\t\t\tdeprecations.push(key);\n\t\t\t\tconsole.warn(`getting vnode.${property} is deprecated, ${message}`);\n\t\t\t}\n\t\t},\n\t\tset() {\n\t\t\tconst key = 'set' + property + message;\n\t\t\tif (deprecations && deprecations.indexOf(key) < 0) {\n\t\t\t\tdeprecations.push(key);\n\t\t\t\tconsole.warn(`setting vnode.${property} is not allowed, ${message}`);\n\t\t\t}\n\t\t}\n\t});\n\n\tconst deprecatedAttributes = {\n\t\tnodeName: warn('nodeName', 'use vnode.type'),\n\t\tattributes: warn('attributes', 'use vnode.props'),\n\t\tchildren: warn('children', 'use vnode.props.children')\n\t};\n\n\tconst deprecatedProto = Object.create({}, deprecatedAttributes);\n\n\toptions.vnode = vnode => {\n\t\tconst props = vnode.props;\n\t\tif (\n\t\t\tvnode.type !== null &&\n\t\t\tprops != null &&\n\t\t\t('__source' in props || '__self' in props)\n\t\t) {\n\t\t\tconst newProps = (vnode.props = {});\n\t\t\tfor (let i in props) {\n\t\t\t\tconst v = props[i];\n\t\t\t\tif (i === '__source') vnode.__source = v;\n\t\t\t\telse if (i === '__self') vnode.__self = v;\n\t\t\t\telse newProps[i] = v;\n\t\t\t}\n\t\t}\n\n\t\t// eslint-disable-next-line\n\t\tvnode.__proto__ = deprecatedProto;\n\t\tif (oldVnode) oldVnode(vnode);\n\t};\n\n\toptions.diffed = vnode => {\n\t\t// Check if the user passed plain objects as children. Note that we cannot\n\t\t// move this check into `options.vnode` because components can receive\n\t\t// children in any shape they want (e.g.\n\t\t// `<MyJSONFormatter>{{ foo: 123, bar: \"abc\" }}</MyJSONFormatter>`).\n\t\t// Putting this check in `options.diffed` ensures that\n\t\t// `vnode._children` is set and that we only validate the children\n\t\t// that were actually rendered.\n\t\tif (vnode._children) {\n\t\t\tvnode._children.forEach(child => {\n\t\t\t\tif (child && child.type === undefined) {\n\t\t\t\t\t// Remove internal vnode keys that will always be patched\n\t\t\t\t\tdelete child._parent;\n\t\t\t\t\tdelete child._depth;\n\t\t\t\t\tconst keys = Object.keys(child).join(',');\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`Objects are not valid as a child. Encountered an object with the keys {${keys}}.` +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\thooksAllowed = false;\n\n\t\tif (oldDiffed) oldDiffed(vnode);\n\n\t\tif (vnode._children != null) {\n\t\t\tconst keys = [];\n\t\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\t\tconst child = vnode._children[i];\n\t\t\t\tif (!child || child.key == null) continue;\n\n\t\t\t\tconst key = child.key;\n\t\t\t\tif (keys.indexOf(key) !== -1) {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Following component has two or more children with the ' +\n\t\t\t\t\t\t\t`same key attribute: \"${key}\". This may cause glitches and misbehavior ` +\n\t\t\t\t\t\t\t'in rendering process. Component: \\n\\n' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\n\t\t\t\t\t// Break early to not spam the console\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tkeys.push(key);\n\t\t\t}\n\t\t}\n\t};\n}\n\nconst setState = Component.prototype.setState;\nComponent.prototype.setState = function(update, callback) {\n\tif (this._vnode == null) {\n\t\t// `this._vnode` will be `null` during componentWillMount. But it\n\t\t// is perfectly valid to call `setState` during cWM. So we\n\t\t// need an additional check to verify that we are dealing with a\n\t\t// call inside constructor.\n\t\tif (this.state == null) {\n\t\t\tconsole.warn(\n\t\t\t\t`Calling \"this.setState\" inside the constructor of a component is a ` +\n\t\t\t\t\t`no-op and might be a bug in your application. Instead, set ` +\n\t\t\t\t\t`\"this.state = {}\" directly.\\n\\n${getOwnerStack(getCurrentVNode())}`\n\t\t\t);\n\t\t}\n\t}\n\n\treturn setState.call(this, update, callback);\n};\n\nconst forceUpdate = Component.prototype.forceUpdate;\nComponent.prototype.forceUpdate = function(callback) {\n\tif (this._vnode == null) {\n\t\tconsole.warn(\n\t\t\t`Calling \"this.forceUpdate\" inside the constructor of a component is a ` +\n\t\t\t\t`no-op and might be a bug in your application.\\n\\n${getOwnerStack(\n\t\t\t\t\tgetCurrentVNode()\n\t\t\t\t)}`\n\t\t);\n\t} else if (this._parentDom == null) {\n\t\tconsole.warn(\n\t\t\t`Can't call \"this.forceUpdate\" on an unmounted component. This is a no-op, ` +\n\t\t\t\t`but it indicates a memory leak in your application. To fix, cancel all ` +\n\t\t\t\t`subscriptions and asynchronous tasks in the componentWillUnmount method.` +\n\t\t\t\t`\\n\\n${getOwnerStack(this._vnode)}`\n\t\t);\n\t}\n\treturn forceUpdate.call(this, callback);\n};\n\n/**\n * Serialize a vnode tree to a string\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function serializeVNode(vnode) {\n\tlet { props } = vnode;\n\tlet name = getDisplayName(vnode);\n\n\tlet attrs = '';\n\tfor (let prop in props) {\n\t\tif (props.hasOwnProperty(prop) && prop !== 'children') {\n\t\t\tlet value = props[prop];\n\n\t\t\t// If it is an object but doesn't have toString(), use Object.toString\n\t\t\tif (typeof value == 'function') {\n\t\t\t\tvalue = `function ${value.displayName || value.name}() {}`;\n\t\t\t}\n\n\t\t\tvalue =\n\t\t\t\tObject(value) === value && !value.toString\n\t\t\t\t\t? Object.prototype.toString.call(value)\n\t\t\t\t\t: value + '';\n\n\t\t\tattrs += ` ${prop}=${JSON.stringify(value)}`;\n\t\t}\n\t}\n\n\tlet children = props.children;\n\treturn `<${name}${attrs}${\n\t\tchildren && children.length ? '>..</' + name + '>' : ' />'\n\t}`;\n}\n", "export const ELEMENT_NODE = 1;\nexport const DOCUMENT_NODE = 9;\nexport const DOCUMENT_FRAGMENT_NODE = 11;\n", "/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n", "import { initDebug } from './debug';\nimport 'preact/devtools';\n\ninitDebug();\n\nexport { resetPropWarnings } from './check-props';\n"], "names": ["loggedTypeFailures", "getDisplayName", "vnode", "type", "Fragment", "displayName", "name", "renderStack", "ownerStack", "getCurrentVNode", "length", "hasBabelPlugin", "isPossibleOwner", "getOwnerStack", "stack", "next", "__o", "push", "reduce", "acc", "owner", "source", "__source", "fileName", "lineNumber", "console", "warn", "isWeakMapSupported", "WeakMap", "getClosestDomNodeParent", "parent", "__", "setState", "Component", "prototype", "update", "callback", "this", "__v", "state", "call", "forceUpdate", "serializeVNode", "props", "attrs", "prop", "hasOwnProperty", "value", "Object", "toString", "JSON", "stringify", "children", "__P", "oldDiff", "options", "__b", "oldDiffed", "diffed", "oldRoot", "oldVNode", "old<PERSON><PERSON>", "__r", "pop", "setupComponentStack", "hooksAllowed", "oldBeforeDiff", "oldVnode", "oldCatchError", "__e", "oldHook", "__h", "warnedComponents", "useEffect", "useLayoutEffect", "lazyPropTypes", "deprecations", "error", "errorInfo", "__c", "then", "promise", "Error", "componentStack", "setTimeout", "e", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "componentName", "parentVNode", "undefined", "__k", "Array", "isArray", "ref", "key", "propTypes", "has", "m", "lazyVNode", "set", "values", "__f", "assign", "obj", "i", "checkPropTypes", "typeSpecs", "location", "getStack", "keys", "for<PERSON>ach", "typeSpecName", "message", "comp", "index", "property", "get", "indexOf", "deprecatedAttributes", "nodeName", "attributes", "deprecatedProto", "create", "newProps", "v", "__self", "__proto__", "child", "join", "initDebug", "resetPropWarnings"], "mappings": "wTAAA,IAEIA,EAAqB,CAAA,ECMTC,SAAAA,EAAeC,GAC9B,OAAIA,EAAMC,OAASC,EAAAA,SACX,WACwB,mBAAdF,EAAMC,KAChBD,EAAMC,KAAKE,aAAeH,EAAMC,KAAKG,KACb,iBAAdJ,EAAMC,KAChBD,EAAMC,KAGP,OACP,CAMD,IAAII,EAAc,GAoBdC,EAAa,GAMDC,SAAAA,IACf,OAAOF,EAAYG,OAAS,EAAIH,EAAYA,EAAYG,OAAS,GAAK,IACtE,CAQD,IAAIC,GAAiB,EAMrB,SAASC,EAAgBV,GACxB,MAA4B,mBAAdA,EAAMC,MAAsBD,EAAMC,MAAQC,EACxDA,QAAA,CAOeS,SAAAA,EAAcX,GAG7B,IAFA,IAAMY,EAAQ,CAACZ,GACXa,EAAOb,EACW,MAAfa,EAAAC,KACNF,EAAMG,KAAKF,EAAXC,KACAD,EAAOA,EACPC,IAED,OAAOF,EAAMI,OAAO,SAACC,EAAKC,GACzBD,GAAG,QAAYlB,EAAemB,GAE9B,IAAMC,EAASD,EAAME,SAUrB,OATID,EACHF,GAAG,QAAYE,EAAOE,SAAnB,IAA+BF,EAAOG,WACzC,IAAWb,IACXA,GAAiB,EACjBc,QAAQC,KACP,mLAIMP,EAAO,IACf,EAAE,GACH,CCnFD,IAAMQ,EAAuC,mBAAXC,QAElC,SAASC,EAAwBC,GAChC,OAAKA,EACqB,mBAAfA,EAAO3B,KACV0B,EAAwBC,EAADC,IAExBD,EAJa,CAAA,CAKpB,CAsVD,IAAME,EAAWC,EAASA,UAACC,UAAUF,SACrCC,EAAAA,UAAUC,UAAUF,SAAW,SAASG,EAAQC,GAe/C,OAdmB,MAAfC,KAAeC,KAKA,MAAdD,KAAKE,OACRd,QAAQC,KACP,gKAEmCb,EAAcJ,MAK7CuB,EAASQ,KAAKH,KAAMF,EAAQC,EACnC,EAED,IAAMK,EAAcR,EAASA,UAACC,UAAUO,YAyBjC,SAASC,EAAexC,GAC9B,IAAMyC,EAAUzC,EAAVyC,MACFrC,EAAOL,EAAeC,GAEtB0C,EAAQ,GACZ,IAAK,IAAIC,KAAQF,EAChB,GAAIA,EAAMG,eAAeD,IAAkB,aAATA,EAAqB,CACtD,IAAIE,EAAQJ,EAAME,GAGE,mBAATE,IACVA,EAAK,aAAeA,EAAM1C,aAAe0C,EAAMzC,eAGhDyC,EACCC,OAAOD,KAAWA,GAAUA,EAAME,SAE/BF,EAAQ,GADRC,OAAOd,UAAUe,SAAST,KAAKO,GAGnCH,GAAK,IAAQC,EAAR,IAAgBK,KAAKC,UAAUJ,EACpC,CAGF,IAAIK,EAAWT,EAAMS,SACrB,MAAA,IAAW9C,EAAOsC,GACjBQ,GAAYA,EAAS1C,OAAS,QAAUJ,EAAO,IAAM,MAEtD,CAnDD2B,EAAAA,UAAUC,UAAUO,YAAc,SAASL,GAgB1C,OAfmB,MAAfC,KAAAC,IACHb,QAAQC,KACP,0HACqDb,EACnDJ,MAG0B,MAAnB4B,KAAAgB,KACV5B,QAAQC,KACP,iOAGQb,EAAcwB,KAADC,MAGhBG,EAAYD,KAAKH,KAAMD,EAC9B,EAzXM,YDgFA,WACN,IAAIkB,EAAUC,EAAAA,QAAHC,IACPC,EAAYF,EAAOA,QAACG,OACpBC,EAAUJ,EAAHA,QAAXxB,GACI6B,EAAWL,EAAOA,QAACrD,MACnB2D,EAAYN,EAAHA,QAAAO,IAEbP,EAAAA,QAAQG,OAAS,SAAAxD,GACZU,EAAgBV,IACnBM,EAAWuD,MAEZxD,EAAYwD,MACRN,GAAWA,EAAUvD,EACzB,EAEDqD,EAAOA,QAAPC,IAAgB,SAAAtD,GACXU,EAAgBV,IACnBK,EAAYU,KAAKf,GAEdoD,GAASA,EAAQpD,EACrB,EAEDqD,UAAAxB,GAAgB,SAAC7B,EAAO4B,GACvBtB,EAAa,GACTmD,GAASA,EAAQzD,EAAO4B,EAC5B,EAEDyB,EAAAA,QAAQrD,MAAQ,SAAAA,GACfA,EAAAc,IACCR,EAAWE,OAAS,EAAIF,EAAWA,EAAWE,OAAS,GAAK,KACzDkD,GAAUA,EAAS1D,EACvB,EAEDqD,EAAOA,QAAAO,IAAW,SAAA5D,GACbU,EAAgBV,IACnBM,EAAWS,KAAKf,GAGb2D,GAAWA,EAAU3D,EACzB,CACD,CCvHA8D,GAEA,IAAIC,GAAe,EAGfC,EAAgBX,EAAHA,QAAAC,IACbC,EAAYF,EAAOA,QAACG,OACpBS,EAAWZ,EAAOA,QAACrD,MACnBkE,EAAgBb,EAAHA,QAAAc,IACbV,EAAUJ,EAAHA,QAAAxB,GACPuC,EAAUf,EAAHA,QAAAgB,IACLC,EAAoB7C,EAEvB,CACA8C,UAAW,IAAI7C,QACf8C,gBAAiB,IAAI9C,QACrB+C,cAAe,IAAI/C,SAJnB,KAMGgD,EAAe,GAErBrB,EAAAA,QAAOc,IAAe,SAACQ,EAAO3E,EAAO0D,EAAUkB,GAE9C,GADgB5E,GAASA,EAAJ6E,KACiB,mBAAdF,EAAMG,KAAoB,CACjD,IAAMC,EAAUJ,EAChBA,EAAQ,IAAIK,MACsCjF,iDAAAA,EAAeC,IAIjE,IADA,IAAI4B,EAAS5B,EACN4B,EAAQA,EAASA,KACvB,GAAIA,EAAAiD,KAAqBjD,EAAzBiD,IAAAA,IAA6D,CAC5DF,EAAQI,EACR,KACA,CAKF,GAAIJ,aAAiBK,MACpB,MAAML,CAEP,CAED,KACCC,EAAYA,GAAa,CAAA,GACfK,eAAiBtE,EAAcX,GACzCkE,EAAcS,EAAO3E,EAAO0D,EAAUkB,GAKb,mBAAdD,EAAMG,MAChBI,WAAW,WACV,MAAMP,CACN,EAIF,CAFC,MAAOQ,GACR,MAAMA,CACN,CACD,EAED9B,EAAOA,WAAS,SAACrD,EAAOoF,GACvB,IAAKA,EACJ,MAAUJ,IAAAA,MACT,uIAKF,IAAIK,EACJ,OAAQD,EAAWE,UAClB,KCjGyB,EDkGzB,KChGmC,GDiGnC,KClG0B,EDmGzBD,GAAU,EACV,MACD,QACCA,GAAU,EAGZ,IAAKA,EAAS,CACb,IAAIE,EAAgBxF,EAAeC,GACnC,UAAUgF,MAC8DI,wEAAAA,EAA+BG,qBAAAA,UAAqBH,EADtH,KAGN,CAEG3B,GAASA,EAAQzD,EAAOoF,EAC5B,EAED/B,EAAAA,QAAOC,IAAS,SAAAtD,GACf,IAAMC,EAA0BD,EAA1BC,KACFuF,EAAc7D,EADc3B,EAChC6B,IAIA,GAFAkC,GAAe,OAEF0B,IAATxF,EACH,MAAM,IAAI+E,MACT,+IAECxC,EAAexC,GACRW,OAAAA,EAAcX,IAEbC,GAAQ,MAARA,GAA+B,iBAARA,EAAkB,CACnD,QAAuBwF,IAAnBxF,EAAAyF,UAA8CD,IAAdxF,MACnC,MAAU+E,IAAAA,MACT,2CAA2C/E,EAA3C,wEAEYF,EAAeC,GAF3B,MAEuCwC,EAAevC,GAFtD,uBAGqBF,EAAeC,GAHpC,wFAKQW,EAAcX,IAIxB,MAAM,IAAIgF,MACT,4CACEW,MAAMC,QAAQ3F,GAAQ,QAAUA,GAEnC,CAqCD,GAlCW,UAATA,GAA6B,UAATA,GAA6B,UAATA,GACpB,UAArBuF,EAAYvF,KAQH,OAATA,GACqB,UAArBuF,EAAYvF,MACS,UAArBuF,EAAYvF,MACS,UAArBuF,EAAYvF,MACS,UAArBuF,EAAYvF,KAEZsB,QAAQoD,MACP,uFACCnC,EAAexC,GACRW,OAAAA,EAAcX,IAEJ,OAATC,GAAsC,OAArBuF,EAAYvF,KACvCsB,QAAQoD,MACP,kEACCnC,EAAexC,UACRW,EAAcX,IAEJ,OAATC,GAAsC,OAArBuF,EAAYvF,MACvCsB,QAAQoD,MACP,2DACCnC,EAAexC,GADhB,OAEQW,EAAcX,IA3BvBuB,QAAQoD,MACP,oFACCnC,EAAexC,GACRW,OAAAA,EAAcX,SA6BTyF,IAAdzF,EAAM6F,KACc,mBAAb7F,EAAM6F,KACO,iBAAb7F,EAAM6F,OACX,aAAc7F,GAEhB,MAAM,IAAIgF,MACT,0GACoChF,EAAM6F,kBACzCrD,EAAexC,GACRW,OAAAA,EAAcX,IAIxB,GAAyB,iBAAdA,EAAMC,KAChB,IAAK,IAAM6F,KAAO9F,EAAMyC,MACvB,GACY,MAAXqD,EAAI,IACO,MAAXA,EAAI,IACuB,mBAApB9F,EAAMyC,MAAMqD,IACC,MAApB9F,EAAMyC,MAAMqD,GAEZ,MAAUd,IAAAA,MACT,iBAAgBc,EAAhB,oDACoB9F,EAAMyC,MAAMqD,GADhC,cAECtD,EAAexC,UACRW,EAAcX,IAO1B,GAAyB,mBAAdA,EAAMC,MAAsBD,EAAMC,KAAK8F,UAAW,CAC5D,GAC4B,SAA3B/F,EAAMC,KAAKE,aACXmE,IACCA,EAAiBG,cAAcuB,IAAIhG,EAAMC,MACzC,CACD,IAAMgG,EACL,yFACD,IACC,IAAMC,EAAYlG,EAAMC,OACxBqE,EAAiBG,cAAc0B,IAAInG,EAAMC,MAAM,GAC/CsB,QAAQC,KACPyE,EAAsClG,kCAAAA,EAAemG,GAMtD,CAJC,MAAOnB,GACRxD,QAAQC,KACPyE,EAAI,8DAEL,CACD,CAED,IAAIG,EAASpG,EAAMyC,MACfzC,EAAMC,KAAiBoG,YAC1BD,EEvOYE,SAAOC,EAAK9D,GAC3B,IAAK,IAAI+D,KAAK/D,EAAO8D,EAAIC,GAAK/D,EAAM+D,GACpC,OAA6BD,CAC7B,CFoOYD,CAAO,CAAA,EAAIF,IACNP,IFxNFY,SACfC,EACAN,EACAO,EACApB,EACAqB,GAEA9D,OAAO+D,KAAKH,GAAWI,QAAQ,SAAAC,GAC9B,IAAIpC,EACJ,IACCA,EAAQ+B,EAAUK,GACjBX,EACAW,EACAxB,EEiNA,OF/MA,KAtCyB,+CA2C1B,CAFC,MAAOJ,GACRR,EAAQQ,CACR,CACGR,KAAWA,EAAMqC,WAAWlH,KAC/BA,EAAmB6E,EAAMqC,UAAW,EACpCzF,QAAQoD,MACGgC,qBAAkBhC,EAAMqC,SAAWJ,GACvCA,KAAAA,KACL,KAGH,EACD,CE6LEH,CACCzG,EAAMC,KAAK8F,UACXK,EACA,EACArG,EAAeC,GACf,kBAAMW,EAAcX,EAApB,EAED,CAEGgE,GAAeA,EAAchE,EACjC,EAEDqD,EAAOA,QAAAgB,IAAS,SAAC4C,EAAMC,EAAOjH,GAC7B,IAAKgH,IAASlD,EACb,MAAM,IAAIiB,MAAM,iDAGbZ,GAASA,EAAQ6C,EAAMC,EAAOjH,EAClC,EAMD,IAAMuB,EAAO,SAAC2F,EAAUH,GAAa,MAAA,CACpCI,IADoC,WAEnC,IAAMtB,EAAM,MAAQqB,EAAWH,EAC3BtC,GAAgBA,EAAa2C,QAAQvB,GAAO,IAC/CpB,EAAa3D,KAAK+E,GAClBvE,QAAQC,KAAsB2F,iBAAAA,EAA2BH,mBAAAA,GAE1D,EACDb,IAAM,WACL,IAAML,EAAM,MAAQqB,EAAWH,EAC3BtC,GAAgBA,EAAa2C,QAAQvB,GAAO,IAC/CpB,EAAa3D,KAAK+E,GAClBvE,QAAQC,KAAsB2F,iBAAAA,EAA4BH,oBAAAA,GAE3D,EAdW,EAiBPM,EAAuB,CAC5BC,SAAU/F,EAAK,WAAY,kBAC3BgG,WAAYhG,EAAK,aAAc,mBAC/B0B,SAAU1B,EAAK,WAAY,6BAGtBiG,EAAkB3E,OAAO4E,OAAO,CAAA,EAAIJ,GAE1CjE,EAAAA,QAAQrD,MAAQ,SAAAA,GACf,IAAMyC,EAAQzC,EAAMyC,MACpB,GACgB,OAAfzC,EAAMC,MACG,MAATwC,IACC,aAAcA,GAAS,WAAYA,GACnC,CACD,IAAMkF,EAAY3H,EAAMyC,MAAQ,CAAA,EAChC,IAAK,IAAI+D,KAAK/D,EAAO,CACpB,IAAMmF,EAAInF,EAAM+D,GACN,aAANA,EAAkBxG,EAAMoB,SAAWwG,EACxB,WAANpB,EAAgBxG,EAAM6H,OAASD,EACnCD,EAASnB,GAAKoB,CACnB,CACD,CAGD5H,EAAM8H,UAAYL,EACdxD,GAAUA,EAASjE,EACvB,EAEDqD,EAAAA,QAAQG,OAAS,SAAAxD,GA2BhB,GAnBIA,OACHA,EAAA0F,IAAgBoB,QAAQ,SAAAiB,GACvB,GAAIA,QAAwBtC,IAAfsC,EAAM9H,KAAoB,QAE/B8H,EAAPlG,UACOkG,EACPzE,IAAA,IAAMuD,EAAO/D,OAAO+D,KAAKkB,GAAOC,KAAK,KACrC,MAAM,IAAIhD,MACT,0EAA0E6B,EAA1E,SACQlG,EAAcX,GAEvB,CACD,GAGF+D,GAAe,EAEXR,GAAWA,EAAUvD,GAEF,MAAnBA,EAAA0F,IAEH,IADA,IAAMmB,EAAO,GACJL,EAAI,EAAGA,EAAIxG,EAAA0F,IAAgBlF,OAAQgG,IAAK,CAChD,IAAMuB,EAAQ/H,MAAgBwG,GAC9B,GAAKuB,GAAsB,MAAbA,EAAMjC,IAApB,CAEA,IAAMA,EAAMiC,EAAMjC,IAClB,IAA2B,IAAvBe,EAAKQ,QAAQvB,GAAa,CAC7BvE,QAAQoD,MACP,8EACyBmB,EADzB,mFAGCtD,EAAexC,UACRW,EAAcX,IAIvB,KACA,CAED6G,EAAK9F,KAAK+E,EAdV,CAeA,CAEF,CACD,CGxWDmC,uBLIgBC,WACfpI,EAAqB,CAAA,CACrB"}