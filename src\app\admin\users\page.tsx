import { getCurrentUser } from '@/utils/auth';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { UserRole } from '@/types';
import UserList from '@/components/admin/UserList';

export default async function UsersPage() {
  const user = await getCurrentUser();

  if (!user || (user.role !== UserRole.ADMIN && user.role !== UserRole.REGIONAL_DIRECTOR)) {
    redirect('/dashboard');
  }

  return (
    <div className="space-y-6">
      {/* Header with welcome message and notification */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">User Management</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage system users, roles, and permissions
          </p>
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Welcome, <span className="font-medium">{user.name}</span>
        </div>
      </div>

      {/* User management card */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Card header with title and create button */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">System Users</h2>
          <div className="mt-3 sm:mt-0">
            <Link
              href="/admin/users/create"
              className="btn btn-primary"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create New User
            </Link>
          </div>
        </div>

        {/* User list component */}
        <UserList />
      </div>
    </div>
  );
}
