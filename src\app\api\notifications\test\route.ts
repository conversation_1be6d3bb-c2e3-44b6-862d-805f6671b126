import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/options';
import dbConnect from '@/lib/db/mongodb';
import Notification from '@/models/Notification';
import SmartNotification from '@/models/SmartNotification';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    await dbConnect();

    // Try to convert to ObjectId if needed
    let userId;
    try {
      const mongoose = require('mongoose');
      userId = mongoose.Types.ObjectId.isValid(session.user.id)
        ? new mongoose.Types.ObjectId(session.user.id)
        : session.user.id;
    } catch (error) {
      console.error('Error converting user ID to ObjectId:', error);
      userId = session.user.id;
    }

    const body = await request.json();
    const { type = 'both' } = body; // 'regular', 'smart', or 'both'

    const results = [];

    // Create a regular notification
    if (type === 'regular' || type === 'both') {
      const regularNotification = new Notification({
        userId,
        documentId: null, // Test notification without document
        message: `Test notification created at ${new Date().toLocaleString()}`,
        type: 'TEST',
        isRead: false
      });

      await regularNotification.save();
      results.push({ type: 'regular', id: regularNotification._id });
    }

    // Create a smart notification
    if (type === 'smart' || type === 'both') {
      const smartNotification = new SmartNotification({
        userId,
        documentId: null, // Test notification without document
        message: `Smart AI notification created at ${new Date().toLocaleString()}`,
        type: 'AI_TEST',
        priority: 'high',
        suggestedActions: [
          'Review the notification system',
          'Test real-time updates',
          'Check notification preferences'
        ],
        optimalTiming: 'immediate',
        aiGenerated: true,
        aiContext: {
          workload: 5,
          urgency: 'medium',
          userBehavior: {
            preferredTime: 'morning',
            responseRate: 0.85
          }
        },
        isRead: false,
        isScheduled: false
      });

      await smartNotification.save();
      results.push({ type: 'smart', id: smartNotification._id });
    }

    console.log(`Created ${results.length} test notifications for user ${session.user.id}`);

    return NextResponse.json({
      success: true,
      message: `Created ${results.length} test notification(s)`,
      notifications: results
    });

  } catch (error: any) {
    console.error('Error creating test notifications:', error);
    return NextResponse.json(
      { message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    await dbConnect();

    // Try to convert to ObjectId if needed
    let userId;
    try {
      const mongoose = require('mongoose');
      userId = mongoose.Types.ObjectId.isValid(session.user.id)
        ? new mongoose.Types.ObjectId(session.user.id)
        : session.user.id;
    } catch (error) {
      console.error('Error converting user ID to ObjectId:', error);
      userId = session.user.id;
    }

    // Delete all test notifications
    const regularResult = await Notification.deleteMany({
      userId,
      type: { $in: ['TEST', 'AI_TEST'] }
    });

    const smartResult = await SmartNotification.deleteMany({
      userId,
      type: { $in: ['TEST', 'AI_TEST'] }
    });

    const totalDeleted = regularResult.deletedCount + smartResult.deletedCount;

    console.log(`Deleted ${totalDeleted} test notifications for user ${session.user.id}`);

    return NextResponse.json({
      success: true,
      message: `Deleted ${totalDeleted} test notification(s)`,
      stats: {
        total: totalDeleted,
        regular: regularResult.deletedCount,
        smart: smartResult.deletedCount
      }
    });

  } catch (error: any) {
    console.error('Error deleting test notifications:', error);
    return NextResponse.json(
      { message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}
