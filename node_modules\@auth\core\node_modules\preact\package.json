{"name": "preact", "amdName": "preact", "version": "10.11.3", "private": false, "description": "Fast 3kb React-compatible Virtual DOM library.", "main": "dist/preact.js", "module": "dist/preact.module.js", "umd:main": "dist/preact.umd.js", "unpkg": "dist/preact.min.js", "source": "src/index.js", "exports": {".": {"types": "./src/index.d.ts", "browser": "./dist/preact.module.js", "umd": "./dist/preact.umd.js", "import": "./dist/preact.mjs", "require": "./dist/preact.js"}, "./compat": {"types": "./compat/src/index.d.ts", "browser": "./compat/dist/compat.module.js", "umd": "./compat/dist/compat.umd.js", "import": "./compat/dist/compat.mjs", "require": "./compat/dist/compat.js"}, "./debug": {"types": "./debug/src/index.d.ts", "browser": "./debug/dist/debug.module.js", "umd": "./debug/dist/debug.umd.js", "import": "./debug/dist/debug.mjs", "require": "./debug/dist/debug.js"}, "./devtools": {"types": "./devtools/src/index.d.ts", "browser": "./devtools/dist/devtools.module.js", "umd": "./devtools/dist/devtools.umd.js", "import": "./devtools/dist/devtools.mjs", "require": "./devtools/dist/devtools.js"}, "./hooks": {"types": "./hooks/src/index.d.ts", "browser": "./hooks/dist/hooks.module.js", "umd": "./hooks/dist/hooks.umd.js", "import": "./hooks/dist/hooks.mjs", "require": "./hooks/dist/hooks.js"}, "./test-utils": {"types": "./test-utils/src/index.d.ts", "browser": "./test-utils/dist/testUtils.module.js", "umd": "./test-utils/dist/testUtils.umd.js", "import": "./test-utils/dist/testUtils.mjs", "require": "./test-utils/dist/testUtils.js"}, "./jsx-runtime": {"types": "./jsx-runtime/src/index.d.ts", "browser": "./jsx-runtime/dist/jsxRuntime.module.js", "umd": "./jsx-runtime/dist/jsxRuntime.umd.js", "import": "./jsx-runtime/dist/jsxRuntime.mjs", "require": "./jsx-runtime/dist/jsxRuntime.js"}, "./jsx-dev-runtime": {"browser": "./jsx-runtime/dist/jsxRuntime.module.js", "umd": "./jsx-runtime/dist/jsxRuntime.umd.js", "import": "./jsx-runtime/dist/jsxRuntime.mjs", "require": "./jsx-runtime/dist/jsxRuntime.js"}, "./compat/client": {"import": "./compat/client.mjs", "require": "./compat/client.js"}, "./compat/server": {"browser": "./compat/server.browser.js", "import": "./compat/server.mjs", "require": "./compat/server.js"}, "./compat/jsx-runtime": {"import": "./compat/jsx-runtime.mjs", "require": "./compat/jsx-runtime.js"}, "./compat/jsx-dev-runtime": {"import": "./compat/jsx-dev-runtime.mjs", "require": "./compat/jsx-dev-runtime.js"}, "./compat/scheduler": {"import": "./compat/scheduler.mjs", "require": "./compat/scheduler.js"}, "./package.json": "./package.json", "./compat/package.json": "./compat/package.json", "./debug/package.json": "./debug/package.json", "./devtools/package.json": "./devtools/package.json", "./hooks/package.json": "./hooks/package.json", "./test-utils/package.json": "./test-utils/package.json", "./jsx-runtime/package.json": "./jsx-runtime/package.json"}, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/preact"}, "types": "src/index.d.ts", "scripts": {"prepare": "run-s build && check-export-map", "build": "npm-run-all --parallel build:*", "build:core": "microbundle build --raw --no-generateTypes -f cjs,esm,umd", "build:core-min": "microbundle build --raw --no-generateTypes -f cjs,esm,umd,iife src/cjs.js -o dist/preact.min.js", "build:debug": "microbundle build --raw --no-generateTypes -f cjs,esm,umd --cwd debug", "build:devtools": "microbundle build --raw --no-generateTypes -f cjs,esm,umd --cwd devtools", "build:hooks": "microbundle build --raw --no-generateTypes -f cjs,esm,umd --cwd hooks", "build:test-utils": "microbundle build --raw --no-generateTypes -f cjs,esm,umd --cwd test-utils", "build:compat": "microbundle build src/index.js src/scheduler.js --raw --no-generateTypes -f cjs,esm,umd --cwd compat --globals 'preact/hooks=preactHooks'", "build:jsx": "microbundle build --raw --no-generateTypes -f cjs,esm,umd --cwd jsx-runtime", "postbuild": "node ./config/node-13-exports.js && node ./config/compat-entries.js", "dev": "microbundle watch --raw --no-generateTypes --format cjs", "dev:hooks": "microbundle watch --raw --no-generateTypes --format cjs --cwd hooks", "dev:compat": "microbundle watch --raw --no-generateTypes --format cjs --cwd compat --globals 'preact/hooks=preactHooks'", "test": "npm-run-all build lint test:unit", "test:unit": "run-p test:mocha test:karma:minify test:ts", "test:ts": "run-p test:ts:*", "test:ts:core": "tsc -p test/ts/ && mocha --require \"@babel/register\" test/ts/**/*-test.js", "test:ts:compat": "tsc -p compat/test/ts/", "test:mocha": "mocha --recursive --require \"@babel/register\" test/shared test/node", "test:mocha:watch": "npm run test:mocha -- --watch", "test:karma": "cross-env COVERAGE=true BABEL_NO_MODULES=true karma start karma.conf.js --single-run", "test:karma:minify": "cross-env COVERAGE=true MINIFY=true BABEL_NO_MODULES=true karma start karma.conf.js --single-run", "test:karma:watch": "cross-env BABEL_NO_MODULES=true karma start karma.conf.js --no-single-run", "test:karma:hooks": "cross-env COVERAGE=false BABEL_NO_MODULES=true karma start karma.conf.js --grep=hooks/test/browser/**.js --no-single-run", "test:karma:test-utils": "cross-env PERFORMANCE=false COVERAGE=false BABEL_NO_MODULES=true karma start karma.conf.js --grep=test-utils/test/shared/**.js --no-single-run", "test:karma:bench": "cross-env PERFORMANCE=true COVERAGE=false BABEL_NO_MODULES=true karma start karma.conf.js --grep=test/benchmarks/**.js --single-run", "benchmark": "npm run test:karma:bench -- no-single-run", "lint": "eslint src test debug compat hooks test-utils"}, "eslintConfig": {"extends": ["developit", "prettier"], "settings": {"react": {"pragma": "createElement"}}, "rules": {"camelcase": [1, {"allow": ["__test__*", "unstable_*", "UNSAFE_*"]}], "no-unused-vars": [2, {"args": "none", "varsIgnorePattern": "^h|React$"}], "prefer-rest-params": 0, "prefer-spread": 0, "no-cond-assign": 0, "react/jsx-no-bind": 0, "react/no-danger": "off", "react/prefer-stateless-function": 0, "react/sort-comp": 0, "jest/valid-expect": 0, "jest/no-disabled-tests": 0, "jest/no-test-callback": 0, "jest/expect-expect": 0, "jest/no-standalone-expect": 0, "jest/no-export": 0, "react/no-find-dom-node": 0}}, "eslintIgnore": ["test/fixtures", "test/ts/", "*.ts", "dist"], "prettier": {"singleQuote": true, "trailingComma": "none", "useTabs": true, "tabWidth": 2}, "lint-staged": {"**/*.{js,jsx,ts,tsx,yml}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "files": ["src", "dist", "compat/dist", "compat/src", "compat/client.js", "compat/client.mjs", "compat/server.browser.js", "compat/server.js", "compat/server.mjs", "compat/scheduler.js", "compat/scheduler.mjs", "compat/test-utils.js", "compat/jsx-runtime.js", "compat/jsx-runtime.mjs", "compat/jsx-dev-runtime.js", "compat/jsx-dev-runtime.mjs", "compat/package.json", "debug/dist", "debug/src", "debug/package.json", "devtools/dist", "devtools/src", "devtools/package.json", "hooks/dist", "hooks/src", "hooks/package.json", "jsx-runtime/dist", "jsx-runtime/src", "jsx-runtime/package.json", "test-utils/src", "test-utils/package.json", "test-utils/dist"], "keywords": ["preact", "react", "ui", "user interface", "virtual dom", "vdom", "components", "dom diff", "front-end", "framework"], "authors": ["The Preact Authors (https://github.com/preactjs/preact/contributors)"], "repository": "preactjs/preact", "bugs": "https://github.com/preactjs/preact/issues", "homepage": "https://preactjs.com", "devDependencies": {"@actions/github": "^5.0.0", "@actions/glob": "^0.2.0", "@babel/core": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-react-jsx-source": "^7.7.4", "@babel/preset-env": "^7.7.1", "@babel/register": "^7.7.0", "@types/chai": "^4.1.2", "@types/mocha": "^5.0.0", "@types/node": "^14.14.10", "babel-plugin-istanbul": "^6.0.0", "babel-plugin-transform-async-to-promises": "^0.8.15", "babel-plugin-transform-rename-properties": "0.1.0", "benchmark": "^2.1.4", "chai": "^4.1.2", "check-export-map": "^1.3.0", "coveralls": "^3.0.0", "cross-env": "^7.0.2", "diff": "^5.0.0", "errorstacks": "^2.4.0", "esbuild": "^0.14.50", "eslint": "5.15.1", "eslint-config-developit": "^1.1.1", "eslint-config-prettier": "^6.5.0", "eslint-plugin-react": "7.12.4", "husky": "^4.3.0", "karma": "^6.3.16", "karma-chai-sinon": "^0.1.5", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.1.0", "karma-esbuild": "^2.2.4", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^4.3.4", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.7", "kolorist": "^1.2.10", "lint-staged": "^10.5.2", "lodash": "^4.17.20", "microbundle": "^0.15.1", "mocha": "^8.2.1", "npm-merge-driver-install": "^1.1.1", "npm-run-all": "^4.0.0", "preact-render-to-string": "^5.2.5", "prettier": "^1.18.2", "prop-types": "^15.7.2", "sade": "^1.7.4", "sinon": "^9.2.3", "sinon-chai": "^3.5.0", "typescript": "4.4.2", "undici": "^4.12.0"}, "volta": {"node": "16.18.0"}}