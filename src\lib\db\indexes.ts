import mongoose from 'mongoose';
import dbConnect from './mongodb';

/**
 * Database indexes for performance optimization
 * This file creates essential indexes to speed up common queries
 */

export async function createPerformanceIndexes() {
  try {
    await dbConnect();
    
    console.log('🚀 Creating performance indexes...');
    
    // Get database connection
    const db = mongoose.connection.db;
    
    // 1. Notification indexes for faster queries
    const notificationsCollection = db.collection('notifications');
    
    // Index for user notifications (most common query)
    await notificationsCollection.createIndex(
      { userId: 1, createdAt: -1 },
      { name: 'user_notifications_idx', background: true }
    );
    
    // Index for unread notifications
    await notificationsCollection.createIndex(
      { userId: 1, isRead: 1, createdAt: -1 },
      { name: 'user_unread_notifications_idx', background: true }
    );
    
    // Index for notification type filtering
    await notificationsCollection.createIndex(
      { userId: 1, type: 1, createdAt: -1 },
      { name: 'user_type_notifications_idx', background: true }
    );
    
    // 2. Smart notification indexes
    const smartNotificationsCollection = db.collection('smartnotifications');
    
    // Index for user smart notifications
    await smartNotificationsCollection.createIndex(
      { userId: 1, createdAt: -1 },
      { name: 'user_smart_notifications_idx', background: true }
    );
    
    // Index for unread smart notifications
    await smartNotificationsCollection.createIndex(
      { userId: 1, isRead: 1, createdAt: -1 },
      { name: 'user_unread_smart_notifications_idx', background: true }
    );
    
    // Index for priority filtering
    await smartNotificationsCollection.createIndex(
      { userId: 1, priority: 1, createdAt: -1 },
      { name: 'user_priority_smart_notifications_idx', background: true }
    );
    
    // 3. Document indexes for faster queries
    const documentsCollection = db.collection('documents');
    
    // Index for user documents
    await documentsCollection.createIndex(
      { createdBy: 1, createdAt: -1 },
      { name: 'user_documents_idx', background: true }
    );
    
    // Index for document status
    await documentsCollection.createIndex(
      { status: 1, createdAt: -1 },
      { name: 'status_documents_idx', background: true }
    );
    
    // Index for tracking number (unique identifier)
    await documentsCollection.createIndex(
      { trackingNumber: 1 },
      { name: 'tracking_number_idx', unique: true, background: true }
    );
    
    // Index for recipient queries
    await documentsCollection.createIndex(
      { recipientId: 1, status: 1, createdAt: -1 },
      { name: 'recipient_documents_idx', background: true }
    );
    
    // Index for division-based queries
    await documentsCollection.createIndex(
      { 'createdBy.division': 1, status: 1, createdAt: -1 },
      { name: 'division_documents_idx', background: true }
    );
    
    // 4. User indexes
    const usersCollection = db.collection('users');
    
    // Index for email login
    await usersCollection.createIndex(
      { email: 1 },
      { name: 'email_idx', unique: true, background: true }
    );
    
    // Index for role and division queries
    await usersCollection.createIndex(
      { role: 1, division: 1 },
      { name: 'role_division_idx', background: true }
    );
    
    // 5. Session indexes for faster authentication
    const sessionsCollection = db.collection('sessions');
    
    // Index for session token lookup
    await sessionsCollection.createIndex(
      { sessionToken: 1 },
      { name: 'session_token_idx', unique: true, background: true }
    );
    
    // Index for user sessions
    await sessionsCollection.createIndex(
      { userId: 1, expires: 1 },
      { name: 'user_sessions_idx', background: true }
    );
    
    // 6. Audit log indexes
    const auditLogsCollection = db.collection('auditlogs');
    
    // Index for user audit logs
    await auditLogsCollection.createIndex(
      { userId: 1, timestamp: -1 },
      { name: 'user_audit_logs_idx', background: true }
    );
    
    // Index for action type filtering
    await auditLogsCollection.createIndex(
      { action: 1, timestamp: -1 },
      { name: 'action_audit_logs_idx', background: true }
    );
    
    // Index for document-related audit logs
    await auditLogsCollection.createIndex(
      { documentId: 1, timestamp: -1 },
      { name: 'document_audit_logs_idx', background: true }
    );
    
    console.log('✅ Performance indexes created successfully!');
    
    // Log index statistics
    const collections = [
      'notifications',
      'smartnotifications', 
      'documents',
      'users',
      'sessions',
      'auditlogs'
    ];
    
    for (const collectionName of collections) {
      const collection = db.collection(collectionName);
      const indexes = await collection.listIndexes().toArray();
      console.log(`📊 ${collectionName}: ${indexes.length} indexes`);
    }
    
  } catch (error) {
    console.error('❌ Error creating performance indexes:', error);
    throw error;
  }
}

/**
 * Drop all custom indexes (for maintenance)
 */
export async function dropPerformanceIndexes() {
  try {
    await dbConnect();
    
    console.log('🗑️ Dropping performance indexes...');
    
    const db = mongoose.connection.db;
    const collections = [
      'notifications',
      'smartnotifications',
      'documents', 
      'users',
      'sessions',
      'auditlogs'
    ];
    
    for (const collectionName of collections) {
      const collection = db.collection(collectionName);
      const indexes = await collection.listIndexes().toArray();
      
      // Drop custom indexes (keep _id index)
      for (const index of indexes) {
        if (index.name !== '_id_') {
          await collection.dropIndex(index.name);
          console.log(`Dropped index: ${index.name} from ${collectionName}`);
        }
      }
    }
    
    console.log('✅ Performance indexes dropped successfully!');
    
  } catch (error) {
    console.error('❌ Error dropping performance indexes:', error);
    throw error;
  }
}

/**
 * Get index statistics for monitoring
 */
export async function getIndexStats() {
  try {
    await dbConnect();
    
    const db = mongoose.connection.db;
    const collections = [
      'notifications',
      'smartnotifications',
      'documents',
      'users', 
      'sessions',
      'auditlogs'
    ];
    
    const stats: Record<string, any> = {};
    
    for (const collectionName of collections) {
      const collection = db.collection(collectionName);
      const indexes = await collection.listIndexes().toArray();
      const collStats = await db.command({ collStats: collectionName });
      
      stats[collectionName] = {
        indexCount: indexes.length,
        indexes: indexes.map(idx => ({
          name: idx.name,
          keys: idx.key,
          unique: idx.unique || false
        })),
        totalSize: collStats.size,
        totalIndexSize: collStats.totalIndexSize,
        avgObjSize: collStats.avgObjSize
      };
    }
    
    return stats;
    
  } catch (error) {
    console.error('❌ Error getting index stats:', error);
    throw error;
  }
}
