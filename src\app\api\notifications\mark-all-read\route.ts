import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/options';
import dbConnect from '@/lib/db/mongodb';
import Notification from '@/models/Notification';
import SmartNotification from '@/models/SmartNotification';

// POST /api/notifications/mark-all-read - Mark all notifications as read
export async function POST(request: NextRequest) {
  return markAllAsRead(request);
}

// PATCH /api/notifications/mark-all-read - Mark all notifications as read (alternative method)
export async function PATCH(request: NextRequest) {
  return markAllAsRead(request);
}

async function markAllAsRead(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    await dbConnect();

    // Try to convert to ObjectId if needed
    let userId;
    try {
      const mongoose = require('mongoose');
      userId = mongoose.Types.ObjectId.isValid(session.user.id)
        ? new mongoose.Types.ObjectId(session.user.id)
        : session.user.id;
    } catch (error) {
      console.error('Error converting user ID to ObjectId:', error);
      userId = session.user.id;
    }

    // Update all unread regular notifications for the current user
    const regularResult = await Notification.updateMany(
      { userId, isRead: false },
      { isRead: true }
    );

    // Update all unread smart notifications for the current user
    const smartResult = await SmartNotification.updateMany(
      { userId, isRead: false },
      { isRead: true }
    );

    const totalUpdated = regularResult.modifiedCount + smartResult.modifiedCount;

    console.log(`Marked ${totalUpdated} notifications as read for user ${session.user.id}`);
    console.log(`Regular: ${regularResult.modifiedCount}, Smart: ${smartResult.modifiedCount}`);

    return NextResponse.json({
      success: true,
      message: `All ${totalUpdated} notifications marked as read`,
      stats: {
        total: totalUpdated,
        regular: regularResult.modifiedCount,
        smart: smartResult.modifiedCount
      }
    });
  } catch (error: any) {
    console.error('Error marking all notifications as read:', error);
    return NextResponse.json(
      { message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}
