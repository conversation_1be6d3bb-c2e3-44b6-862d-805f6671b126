/**
 * Core types and interfaces for Gemini AI service
 */

/**
 * Interface for Gemini API request
 */
export interface GeminiRequest {
  contents: {
    parts: {
      text: string;
    }[];
  }[];
  generationConfig: {
    temperature: number;
    topK: number;
    topP: number;
    maxOutputTokens: number;
  };
}

/**
 * Interface for Gemini API response
 */
export interface GeminiResponse {
  candidates: {
    content: {
      parts: {
        text: string;
      }[];
    };
    finishReason: string;
  }[];
}

/**
 * Smart notification generation context
 */
export interface NotificationContext {
  workload?: number;
  urgency?: 'low' | 'medium' | 'high' | 'critical';
  deadline?: Date;
  userBehavior?: any;
  relatedDocuments?: number;
}

/**
 * Smart notification result
 */
export interface SmartNotificationResult {
  message: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  suggestedActions: string[];
  optimalTiming: string;
}

/**
 * User behavior analysis data
 */
export interface UserActivityData {
  loginTimes: Date[];
  responsePatterns: any[];
  documentTypes: string[];
  averageResponseTime: number;
}

/**
 * User behavior analysis result
 */
export interface BehaviorAnalysisResult {
  optimalHours: number[];
  preferredDays: string[];
  responsePatterns: any;
  recommendations: string[];
}

/**
 * Reminder schedule entry
 */
export interface ReminderEntry {
  scheduledFor: Date;
  message: string;
  type: 'gentle' | 'urgent' | 'critical';
  escalate?: boolean;
}

/**
 * Reminder schedule result
 */
export interface ReminderScheduleResult {
  reminders: ReminderEntry[];
  escalationPlan: any;
}

/**
 * Chat message interface
 */
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

/**
 * Chat context interface
 */
export interface ChatContext {
  currentPage?: string;
  userRole?: string;
  userDivision?: string;
  pendingDocuments?: number;
  recentActivity?: any[];
}

/**
 * Intent analysis result
 */
export interface IntentAnalysisResult {
  intent: string;
  confidence: number;
  entities: Array<{
    type: string;
    value: string;
  }>;
  suggestions: string[];
}

/**
 * Contextual help result
 */
export interface ContextualHelpResult {
  helpMessage: string;
  quickActions: Array<{
    label: string;
    action: string;
    description: string;
  }>;
  tutorials: Array<{
    title: string;
    steps: string[];
  }>;
}

/**
 * Document interface for AI processing
 */
export interface DocumentData {
  id?: string;
  title: string;
  description?: string;
  category: string;
  status: string;
  createdAt: Date;
  action?: string;
  trackingNumber?: string;
}

/**
 * User interface for AI processing
 */
export interface UserData {
  id: string;
  name: string;
  division: string;
  role: string;
  email?: string;
}

/**
 * Feedback data interface
 */
export interface FeedbackData {
  title: string;
  description: string;
  category: string;
}

/**
 * AI generation configuration
 */
export interface AIConfig {
  temperature: number;
  topK: number;
  topP: number;
  maxOutputTokens: number;
}

/**
 * Default AI configuration
 */
export const DEFAULT_AI_CONFIG: AIConfig = {
  temperature: 0.7,
  topK: 40,
  topP: 0.95,
  maxOutputTokens: 2048
};

/**
 * API endpoints configuration
 */
export const GEMINI_CONFIG = {
  API_URL: 'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent',
  API_KEY: process.env.GEMINI_API_KEY
};
