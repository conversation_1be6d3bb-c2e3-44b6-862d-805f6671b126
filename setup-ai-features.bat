@echo off
color 0E
echo ================================================================
echo           Document Tracker - AI Features Setup
echo ================================================================
echo.

echo This wizard will help you set up AI features for Document Tracker.
echo.

echo The AI features include:
echo [✓] Smart Chatbot - Document assistance and help
echo [✓] Intelligent Notifications - AI-optimized alerts
echo [✓] Behavior Analysis - User pattern recognition
echo [✓] Smart Reminders - Intelligent scheduling
echo [✓] Feedback Analysis - AI-powered suggestions
echo.

echo Choose an option:
echo [1] Test current AI setup
echo [2] Get new Gemini API key (opens browser)
echo [3] Update API key in .env.local
echo [4] Run full diagnostic
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto test_ai
if "%choice%"=="2" goto get_api_key
if "%choice%"=="3" goto update_api_key
if "%choice%"=="4" goto full_diagnostic
goto invalid_choice

:test_ai
echo.
echo ================================================================
echo                    Testing AI Features
echo ================================================================
echo.
node scripts/test-ai-features.js
goto end

:get_api_key
echo.
echo ================================================================
echo                    Getting Gemini API Key
echo ================================================================
echo.
echo Opening Google AI Studio in your browser...
echo.
echo Steps to get your API key:
echo 1. Sign in with your Google account
echo 2. Click "Get API key"
echo 3. Create a new API key
echo 4. Copy the API key
echo 5. Come back here and choose option 3 to update it
echo.
start https://makersuite.google.com/app/apikey
echo.
echo After getting your API key, run this script again and choose option 3.
goto end

:update_api_key
echo.
echo ================================================================
echo                    Update API Key
echo ================================================================
echo.
echo Current .env.local file location: %CD%\.env.local
echo.
echo Please manually edit the .env.local file and update this line:
echo GEMINI_API_KEY=your_new_api_key_here
echo.
echo Replace "your_new_api_key_here" with your actual API key.
echo.
echo After updating, run this script again and choose option 1 to test.
echo.
set /p open_file="Do you want to open .env.local file now? (y/n): "
if /i "%open_file%"=="y" notepad .env.local
goto end

:full_diagnostic
echo.
echo ================================================================
echo                    Full AI Diagnostic
echo ================================================================
echo.
echo Running comprehensive AI features test...
echo.
node scripts/test-ai-features.js
echo.
echo If issues were found, try:
echo 1. Get a new API key (option 2)
echo 2. Update .env.local (option 3)
echo 3. Restart the application
echo 4. Test again (option 1)
goto end

:invalid_choice
echo.
echo Invalid choice. Please enter 1, 2, 3, or 4.
echo.
pause
goto end

:end
echo.
echo Press any key to exit...
pause > nul
