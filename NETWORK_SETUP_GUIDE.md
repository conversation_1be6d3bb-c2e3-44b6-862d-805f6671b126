# 🌐 Document Tracker - Network Access Setup Guide

This guide will help you set up the Document Tracker system for network access so multiple users can connect from different computers on the same network.

## 🚀 Quick Start

### **Method 1: Using the Batch File (Recommended)**
```bash
# Double-click or run:
start-network.bat
```

### **Method 2: Using NPM Commands**
```bash
# Development mode with network access
npm run network

# Or use the built-in Next.js command
npm run dev:network
```

### **Method 3: Manual Command**
```bash
# Start with network access on default port (3000)
npm run dev -- -H 0.0.0.0

# Start with custom port
npm run dev -- -H 0.0.0.0 -p 3001
```

## 📋 Step-by-Step Setup

### **Step 1: Prepare the Server PC**

1. **Make sure the system is running:**
   - Database is connected and migrated
   - All dependencies are installed (`npm install`)
   - Admin user is created

2. **Check Windows Firewall:**
   - Open Windows Defender Firewall
   - Allow Node.js through the firewall
   - Or temporarily disable firewall for testing

### **Step 2: Start Network Access**

1. **Run the network startup:**
   ```bash
   start-network.bat
   ```

2. **Choose your mode:**
   - **Development Mode**: Hot reload, debugging (recommended for setup)
   - **Production Mode**: Optimized performance (requires `npm run build` first)
   - **Custom Port**: Use a different port if 3000 is busy

3. **Note the network addresses:**
   The script will show you something like:
   ```
   🌍 Your application will be accessible at:
   • Local: http://localhost:3000
   • Network: http://*************:3000
   ```

### **Step 3: Connect Other Users**

1. **Share the network URL:**
   - Give other users the network URL (e.g., `http://*************:3000`)
   - Make sure they're on the same WiFi/network

2. **First-time setup:**
   - First user should create an admin account
   - Admin can then create accounts for other users

## 🔧 Network Configuration Options

### **Available NPM Scripts:**
```json
{
  "dev:network": "next dev -H 0.0.0.0",
  "start:network": "next start -H 0.0.0.0",
  "network": "node scripts/start-network.js",
  "network:dev": "node scripts/start-network.js --dev"
}
```

### **Command Line Options:**
```bash
# Basic network access
npm run dev:network

# Custom port
npm run dev:network -- -p 3001

# Production mode (build first)
npm run build
npm run start:network
```

## 🌍 Finding Your Network IP Address

### **Method 1: Using the Script**
The `start-network.js` script automatically detects and displays your network IP.

### **Method 2: Manual Check**
```bash
# Windows Command Prompt
ipconfig

# Look for "IPv4 Address" under your network adapter
# Usually something like: ************* or **********
```

### **Method 3: Network Settings**
1. Open Windows Settings
2. Go to Network & Internet
3. Click on your connection (WiFi/Ethernet)
4. Look for IPv4 address

## 👥 Multi-User Access

### **User Roles and Access:**
- **Admin**: Full system access, user management
- **Regional Director**: Department oversight
- **Division Chief**: Division management
- **Employee**: Document processing

### **Concurrent Users:**
- ✅ Multiple users can access simultaneously
- ✅ Real-time notifications work across network
- ✅ Document updates are synchronized
- ✅ Session management handles multiple logins

## 🛡️ Security Considerations

### **Network Security:**
- System is accessible to anyone on the network
- Use strong passwords for all accounts
- Consider VPN for remote access
- Monitor user activity through audit logs

### **Firewall Configuration:**
```bash
# Windows Firewall - Allow specific port
netsh advfirewall firewall add rule name="Document Tracker" dir=in action=allow protocol=TCP localport=3000
```

## 🔍 Troubleshooting

### **Common Issues:**

#### **1. Users Can't Connect**
```bash
# Check if server is running with network access
netstat -an | findstr :3000

# Should show: 0.0.0.0:3000 (not 127.0.0.1:3000)
```

**Solutions:**
- Make sure you used `-H 0.0.0.0` flag
- Check Windows Firewall settings
- Verify all devices are on same network
- Try different port if 3000 is blocked

#### **2. Slow Performance**
**Solutions:**
- Use production mode (`npm run build` then `npm run start:network`)
- Check network bandwidth
- Reduce concurrent users if needed
- Optimize database queries

#### **3. Connection Drops**
**Solutions:**
- Check network stability
- Restart the application
- Verify server PC doesn't go to sleep
- Check for antivirus interference

### **Testing Network Access:**

1. **From Server PC:**
   ```bash
   # Test local access
   curl http://localhost:3000
   
   # Test network access
   curl http://YOUR_IP:3000
   ```

2. **From Client PC:**
   ```bash
   # Test connection
   ping YOUR_SERVER_IP
   
   # Test port access
   telnet YOUR_SERVER_IP 3000
   ```

## 📊 Performance Optimization

### **For Better Network Performance:**

1. **Use Production Mode:**
   ```bash
   npm run build
   npm run start:network
   ```

2. **Optimize Database:**
   - Ensure MongoDB is running locally on server
   - Use database indexes (already configured)
   - Regular database maintenance

3. **Network Optimization:**
   - Use wired connection for server PC
   - Ensure good WiFi signal for client devices
   - Consider dedicated network for the system

## 🔄 Maintenance

### **Regular Tasks:**
- Monitor server performance
- Check disk space for uploads
- Review audit logs
- Update user accounts as needed
- Backup database regularly

### **Server Management:**
```bash
# Check running processes
tasklist | findstr node

# Monitor network connections
netstat -an | findstr :3000

# Check system resources
wmic cpu get loadpercentage /value
```

## 📱 Mobile Access

The system is responsive and works on:
- ✅ Desktop computers
- ✅ Laptops
- ✅ Tablets
- ✅ Mobile phones (with limitations)

Access the same network URL from any device browser.

---

## 🎯 Quick Reference

### **Start Network Access:**
```bash
start-network.bat
```

### **Share This URL:**
```
http://YOUR_IP_ADDRESS:3000
```

### **Default Credentials:**
- Username: `admin`
- Password: `Admin123!`

### **Support:**
- Check audit logs for user activity
- Monitor system through admin dashboard
- Use database reset tools if needed

**Ready to go? Run `start-network.bat` and share the network URL with your team!**
