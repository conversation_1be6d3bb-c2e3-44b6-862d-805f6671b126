import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { db } = await connectToDatabase();
    
    // Create a test user
    const usersCollection = db.collection('users');
    const testUser = {
      name: 'Test User',
      email: '<EMAIL>',
      role: 'EMPLOYEE',
      division: 'ORD',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const userResult = await usersCollection.insertOne(testUser);
    console.log('Test user created:', userResult.insertedId);

    // Create a test document
    const documentsCollection = db.collection('documents');
    const testDocument = {
      title: 'Test Document',
      description: 'This is a test document to initialize the database',
      category: 'MEMO',
      actionType: 'FOR_INFORMATION',
      senderId: userResult.insertedId,
      recipientId: userResult.insertedId,
      status: 'PENDING',
      trackingNumber: 'DTN-TEST-001',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const docResult = await documentsCollection.insertOne(testDocument);
    console.log('Test document created:', docResult.insertedId);

    // Create a test notification
    const notificationsCollection = db.collection('notifications');
    const testNotification = {
      userId: userResult.insertedId,
      title: 'Welcome to Document Tracker',
      message: 'Your account has been set up successfully',
      type: 'INFO',
      isRead: false,
      createdAt: new Date()
    };
    
    const notifResult = await notificationsCollection.insertOne(testNotification);
    console.log('Test notification created:', notifResult.insertedId);

    res.status(200).json({
      success: true,
      message: 'Database initialized successfully!',
      data: {
        userId: userResult.insertedId,
        documentId: docResult.insertedId,
        notificationId: notifResult.insertedId
      }
    });
    
  } catch (error) {
    console.error('Error initializing database:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize database',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
