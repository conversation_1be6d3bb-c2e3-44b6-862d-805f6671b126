"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/calendar/holidays/route";
exports.ids = ["app/api/calendar/holidays/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fholidays%2Froute&page=%2Fapi%2Fcalendar%2Fholidays%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fholidays%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fholidays%2Froute&page=%2Fapi%2Fcalendar%2Fholidays%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fholidays%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Administrator_Desktop_DocumentTracker_src_app_api_calendar_holidays_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/calendar/holidays/route.ts */ \"(rsc)/./src/app/api/calendar/holidays/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/calendar/holidays/route\",\n        pathname: \"/api/calendar/holidays\",\n        filename: \"route\",\n        bundlePath: \"app/api/calendar/holidays/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\api\\\\calendar\\\\holidays\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Administrator_Desktop_DocumentTracker_src_app_api_calendar_holidays_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/calendar/holidays/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fholidays%2Froute&page=%2Fapi%2Fcalendar%2Fholidays%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fholidays%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/calendar/holidays/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/calendar/holidays/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Mark this route as dynamic to prevent static generation\nconst dynamic = \"force-dynamic\";\n// Philippine holidays for 2024 - Based on Proclamation No. 368\nconst philippineHolidays2024 = [\n    {\n        date: \"2024-01-01\",\n        name: \"New Year's Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-02-10\",\n        name: \"Chinese New Year\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2024-03-28\",\n        name: \"Maundy Thursday\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-03-29\",\n        name: \"Good Friday\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-03-30\",\n        name: \"Black Saturday\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2024-04-09\",\n        name: \"Araw ng Kagitingan\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-05-01\",\n        name: \"Labor Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-06-12\",\n        name: \"Independence Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-06-19\",\n        name: \"Eid al-Adha\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-08-21\",\n        name: \"Ninoy Aquino Day\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2024-08-26\",\n        name: \"National Heroes Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-11-01\",\n        name: \"All Saints' Day\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2024-11-02\",\n        name: \"All Souls' Day\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2024-11-30\",\n        name: \"Bonifacio Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-12-08\",\n        name: \"Feast of the Immaculate Conception\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2024-12-24\",\n        name: \"Christmas Eve\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2024-12-25\",\n        name: \"Christmas Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-12-30\",\n        name: \"Rizal Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2024-12-31\",\n        name: \"New Year's Eve\",\n        type: \"Special Non-working Holiday\"\n    }\n];\n// Philippine holidays for 2025 - Based on Proclamation No. 727\nconst philippineHolidays2025 = [\n    {\n        date: \"2025-01-01\",\n        name: \"New Year's Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-01-29\",\n        name: \"Chinese New Year\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2025-02-25\",\n        name: \"EDSA People Power Revolution Anniversary\",\n        type: \"Special Working Day\"\n    },\n    {\n        date: \"2025-04-09\",\n        name: \"Araw ng Kagitingan\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-04-17\",\n        name: \"Maundy Thursday\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-04-18\",\n        name: \"Good Friday\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-04-19\",\n        name: \"Black Saturday\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2025-05-01\",\n        name: \"Labor Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-06-12\",\n        name: \"Independence Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-07-27\",\n        name: \"Iglesia ni Cristo Anniversary\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2025-08-21\",\n        name: \"Ninoy Aquino Day\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2025-08-25\",\n        name: \"National Heroes Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-10-31\",\n        name: \"All Saints' Day Eve\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2025-11-01\",\n        name: \"All Saints' Day\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2025-11-30\",\n        name: \"Bonifacio Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-12-08\",\n        name: \"Feast of the Immaculate Conception\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2025-12-24\",\n        name: \"Christmas Eve\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2025-12-25\",\n        name: \"Christmas Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-12-30\",\n        name: \"Rizal Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2025-12-31\",\n        name: \"New Year's Eve\",\n        type: \"Special Non-working Holiday\"\n    }\n];\n// Philippine holidays for 2026 (Projected based on recurring holidays)\nconst philippineHolidays2026 = [\n    {\n        date: \"2026-01-01\",\n        name: \"New Year's Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-02-17\",\n        name: \"Chinese New Year\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2026-02-25\",\n        name: \"EDSA People Power Revolution Anniversary\",\n        type: \"Special Working Day\"\n    },\n    {\n        date: \"2026-04-02\",\n        name: \"Maundy Thursday\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-04-03\",\n        name: \"Good Friday\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-04-04\",\n        name: \"Black Saturday\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2026-04-09\",\n        name: \"Araw ng Kagitingan\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-05-01\",\n        name: \"Labor Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-06-12\",\n        name: \"Independence Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-07-27\",\n        name: \"Iglesia ni Cristo Anniversary\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2026-08-21\",\n        name: \"Ninoy Aquino Day\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2026-08-31\",\n        name: \"National Heroes Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-11-01\",\n        name: \"All Saints' Day\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2026-11-30\",\n        name: \"Bonifacio Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-12-08\",\n        name: \"Feast of the Immaculate Conception\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2026-12-24\",\n        name: \"Christmas Eve\",\n        type: \"Special Non-working Holiday\"\n    },\n    {\n        date: \"2026-12-25\",\n        name: \"Christmas Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-12-30\",\n        name: \"Rizal Day\",\n        type: \"Regular Holiday\"\n    },\n    {\n        date: \"2026-12-31\",\n        name: \"New Year's Eve\",\n        type: \"Special Non-working Holiday\"\n    }\n];\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const year = searchParams.get(\"year\");\n        let holidays;\n        if (year === \"2025\") {\n            holidays = philippineHolidays2025;\n        } else if (year === \"2026\") {\n            holidays = philippineHolidays2026;\n        } else {\n            // Default to 2024 if no year specified or any other year\n            holidays = philippineHolidays2024;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            holidays\n        });\n    } catch (error) {\n        console.error(\"Error fetching holidays:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: error.message || \"Something went wrong\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jYWxlbmRhci9ob2xpZGF5cy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMkM7QUFFM0MsMERBQTBEO0FBQ25ELE1BQU1DLFVBQVUsZ0JBQWdCO0FBRXZDLCtEQUErRDtBQUMvRCxNQUFNQyx5QkFBeUI7SUFDN0I7UUFBRUMsTUFBTTtRQUFjQyxNQUFNO1FBQW1CQyxNQUFNO0lBQWtCO0lBQ3ZFO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFvQkMsTUFBTTtJQUE4QjtJQUNwRjtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBbUJDLE1BQU07SUFBa0I7SUFDdkU7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWVDLE1BQU07SUFBa0I7SUFDbkU7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWtCQyxNQUFNO0lBQThCO0lBQ2xGO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFzQkMsTUFBTTtJQUFrQjtJQUMxRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBYUMsTUFBTTtJQUFrQjtJQUNqRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBb0JDLE1BQU07SUFBa0I7SUFDeEU7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWVDLE1BQU07SUFBa0I7SUFDbkU7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQW9CQyxNQUFNO0lBQThCO0lBQ3BGO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUF1QkMsTUFBTTtJQUFrQjtJQUMzRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBb0JDLE1BQU07SUFBOEI7SUFDcEY7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQW1CQyxNQUFNO0lBQThCO0lBQ25GO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFpQkMsTUFBTTtJQUFrQjtJQUNyRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBc0NDLE1BQU07SUFBOEI7SUFDdEc7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWlCQyxNQUFNO0lBQThCO0lBQ2pGO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFpQkMsTUFBTTtJQUFrQjtJQUNyRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBYUMsTUFBTTtJQUFrQjtJQUNqRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBbUJDLE1BQU07SUFBOEI7Q0FDcEY7QUFFRCwrREFBK0Q7QUFDL0QsTUFBTUMseUJBQXlCO0lBQzdCO1FBQUVILE1BQU07UUFBY0MsTUFBTTtRQUFtQkMsTUFBTTtJQUFrQjtJQUN2RTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBb0JDLE1BQU07SUFBOEI7SUFDcEY7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQTRDQyxNQUFNO0lBQXNCO0lBQ3BHO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFzQkMsTUFBTTtJQUFrQjtJQUMxRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBbUJDLE1BQU07SUFBa0I7SUFDdkU7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWVDLE1BQU07SUFBa0I7SUFDbkU7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWtCQyxNQUFNO0lBQThCO0lBQ2xGO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFhQyxNQUFNO0lBQWtCO0lBQ2pFO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFvQkMsTUFBTTtJQUFrQjtJQUN4RTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBaUNDLE1BQU07SUFBOEI7SUFDakc7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQW9CQyxNQUFNO0lBQThCO0lBQ3BGO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUF1QkMsTUFBTTtJQUFrQjtJQUMzRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBd0JDLE1BQU07SUFBOEI7SUFDeEY7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQW9CQyxNQUFNO0lBQThCO0lBQ3BGO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFpQkMsTUFBTTtJQUFrQjtJQUNyRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBc0NDLE1BQU07SUFBOEI7SUFDdEc7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWlCQyxNQUFNO0lBQThCO0lBQ2pGO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFpQkMsTUFBTTtJQUFrQjtJQUNyRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBYUMsTUFBTTtJQUFrQjtJQUNqRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBbUJDLE1BQU07SUFBOEI7Q0FDcEY7QUFFRCx1RUFBdUU7QUFDdkUsTUFBTUUseUJBQXlCO0lBQzdCO1FBQUVKLE1BQU07UUFBY0MsTUFBTTtRQUFtQkMsTUFBTTtJQUFrQjtJQUN2RTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBb0JDLE1BQU07SUFBOEI7SUFDcEY7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQTRDQyxNQUFNO0lBQXNCO0lBQ3BHO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFtQkMsTUFBTTtJQUFrQjtJQUN2RTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBZUMsTUFBTTtJQUFrQjtJQUNuRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBa0JDLE1BQU07SUFBOEI7SUFDbEY7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQXNCQyxNQUFNO0lBQWtCO0lBQzFFO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFhQyxNQUFNO0lBQWtCO0lBQ2pFO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFvQkMsTUFBTTtJQUFrQjtJQUN4RTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBaUNDLE1BQU07SUFBOEI7SUFDakc7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQW9CQyxNQUFNO0lBQThCO0lBQ3BGO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUF1QkMsTUFBTTtJQUFrQjtJQUMzRTtRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBb0JDLE1BQU07SUFBOEI7SUFDcEY7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWlCQyxNQUFNO0lBQWtCO0lBQ3JFO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFzQ0MsTUFBTTtJQUE4QjtJQUN0RztRQUFFRixNQUFNO1FBQWNDLE1BQU07UUFBaUJDLE1BQU07SUFBOEI7SUFDakY7UUFBRUYsTUFBTTtRQUFjQyxNQUFNO1FBQWlCQyxNQUFNO0lBQWtCO0lBQ3JFO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFhQyxNQUFNO0lBQWtCO0lBQ2pFO1FBQUVGLE1BQU07UUFBY0MsTUFBTTtRQUFtQkMsTUFBTTtJQUE4QjtDQUNwRjtBQUVNLGVBQWVHLElBQUlDLE9BQWdCO0lBQ3hDLElBQUk7UUFDRixNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlGLFFBQVFHLEdBQUc7UUFDNUMsTUFBTUMsT0FBT0gsYUFBYUksR0FBRyxDQUFDO1FBRTlCLElBQUlDO1FBRUosSUFBSUYsU0FBUyxRQUFRO1lBQ25CRSxXQUFXVDtRQUNiLE9BQU8sSUFBSU8sU0FBUyxRQUFRO1lBQzFCRSxXQUFXUjtRQUNiLE9BQU87WUFDTCx5REFBeUQ7WUFDekRRLFdBQVdiO1FBQ2I7UUFFQSxPQUFPRixxREFBWUEsQ0FBQ2dCLElBQUksQ0FBQztZQUFFRDtRQUFTO0lBQ3RDLEVBQUUsT0FBT0UsT0FBWTtRQUNuQkMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsT0FBT2pCLHFEQUFZQSxDQUFDZ0IsSUFBSSxDQUN0QjtZQUFFRyxTQUFTRixNQUFNRSxPQUFPLElBQUk7UUFBdUIsR0FDbkQ7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kb2N1bWVudC10cmFja2VyLy4vc3JjL2FwcC9hcGkvY2FsZW5kYXIvaG9saWRheXMvcm91dGUudHM/OWYxNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5cbi8vIE1hcmsgdGhpcyByb3V0ZSBhcyBkeW5hbWljIHRvIHByZXZlbnQgc3RhdGljIGdlbmVyYXRpb25cbmV4cG9ydCBjb25zdCBkeW5hbWljID0gJ2ZvcmNlLWR5bmFtaWMnO1xuXG4vLyBQaGlsaXBwaW5lIGhvbGlkYXlzIGZvciAyMDI0IC0gQmFzZWQgb24gUHJvY2xhbWF0aW9uIE5vLiAzNjhcbmNvbnN0IHBoaWxpcHBpbmVIb2xpZGF5czIwMjQgPSBbXG4gIHsgZGF0ZTogJzIwMjQtMDEtMDEnLCBuYW1lOiAnTmV3IFllYXJcXCdzIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjQtMDItMTAnLCBuYW1lOiAnQ2hpbmVzZSBOZXcgWWVhcicsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjQtMDMtMjgnLCBuYW1lOiAnTWF1bmR5IFRodXJzZGF5JywgdHlwZTogJ1JlZ3VsYXIgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNC0wMy0yOScsIG5hbWU6ICdHb29kIEZyaWRheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjQtMDMtMzAnLCBuYW1lOiAnQmxhY2sgU2F0dXJkYXknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI0LTA0LTA5JywgbmFtZTogJ0FyYXcgbmcgS2FnaXRpbmdhbicsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjQtMDUtMDEnLCBuYW1lOiAnTGFib3IgRGF5JywgdHlwZTogJ1JlZ3VsYXIgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNC0wNi0xMicsIG5hbWU6ICdJbmRlcGVuZGVuY2UgRGF5JywgdHlwZTogJ1JlZ3VsYXIgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNC0wNi0xOScsIG5hbWU6ICdFaWQgYWwtQWRoYScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjQtMDgtMjEnLCBuYW1lOiAnTmlub3kgQXF1aW5vIERheScsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjQtMDgtMjYnLCBuYW1lOiAnTmF0aW9uYWwgSGVyb2VzIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjQtMTEtMDEnLCBuYW1lOiAnQWxsIFNhaW50c1xcJyBEYXknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI0LTExLTAyJywgbmFtZTogJ0FsbCBTb3Vsc1xcJyBEYXknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI0LTExLTMwJywgbmFtZTogJ0JvbmlmYWNpbyBEYXknLCB0eXBlOiAnUmVndWxhciBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI0LTEyLTA4JywgbmFtZTogJ0ZlYXN0IG9mIHRoZSBJbW1hY3VsYXRlIENvbmNlcHRpb24nLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI0LTEyLTI0JywgbmFtZTogJ0NocmlzdG1hcyBFdmUnLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI0LTEyLTI1JywgbmFtZTogJ0NocmlzdG1hcyBEYXknLCB0eXBlOiAnUmVndWxhciBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI0LTEyLTMwJywgbmFtZTogJ1JpemFsIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjQtMTItMzEnLCBuYW1lOiAnTmV3IFllYXJcXCdzIEV2ZScsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG5dO1xuXG4vLyBQaGlsaXBwaW5lIGhvbGlkYXlzIGZvciAyMDI1IC0gQmFzZWQgb24gUHJvY2xhbWF0aW9uIE5vLiA3MjdcbmNvbnN0IHBoaWxpcHBpbmVIb2xpZGF5czIwMjUgPSBbXG4gIHsgZGF0ZTogJzIwMjUtMDEtMDEnLCBuYW1lOiAnTmV3IFllYXJcXCdzIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjUtMDEtMjknLCBuYW1lOiAnQ2hpbmVzZSBOZXcgWWVhcicsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjUtMDItMjUnLCBuYW1lOiAnRURTQSBQZW9wbGUgUG93ZXIgUmV2b2x1dGlvbiBBbm5pdmVyc2FyeScsIHR5cGU6ICdTcGVjaWFsIFdvcmtpbmcgRGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTA0LTA5JywgbmFtZTogJ0FyYXcgbmcgS2FnaXRpbmdhbicsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjUtMDQtMTcnLCBuYW1lOiAnTWF1bmR5IFRodXJzZGF5JywgdHlwZTogJ1JlZ3VsYXIgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNS0wNC0xOCcsIG5hbWU6ICdHb29kIEZyaWRheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjUtMDQtMTknLCBuYW1lOiAnQmxhY2sgU2F0dXJkYXknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTA1LTAxJywgbmFtZTogJ0xhYm9yIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjUtMDYtMTInLCBuYW1lOiAnSW5kZXBlbmRlbmNlIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjUtMDctMjcnLCBuYW1lOiAnSWdsZXNpYSBuaSBDcmlzdG8gQW5uaXZlcnNhcnknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTA4LTIxJywgbmFtZTogJ05pbm95IEFxdWlubyBEYXknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTA4LTI1JywgbmFtZTogJ05hdGlvbmFsIEhlcm9lcyBEYXknLCB0eXBlOiAnUmVndWxhciBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTEwLTMxJywgbmFtZTogJ0FsbCBTYWludHNcXCcgRGF5IEV2ZScsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjUtMTEtMDEnLCBuYW1lOiAnQWxsIFNhaW50c1xcJyBEYXknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTExLTMwJywgbmFtZTogJ0JvbmlmYWNpbyBEYXknLCB0eXBlOiAnUmVndWxhciBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTEyLTA4JywgbmFtZTogJ0ZlYXN0IG9mIHRoZSBJbW1hY3VsYXRlIENvbmNlcHRpb24nLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTEyLTI0JywgbmFtZTogJ0NocmlzdG1hcyBFdmUnLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTEyLTI1JywgbmFtZTogJ0NocmlzdG1hcyBEYXknLCB0eXBlOiAnUmVndWxhciBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI1LTEyLTMwJywgbmFtZTogJ1JpemFsIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjUtMTItMzEnLCBuYW1lOiAnTmV3IFllYXJcXCdzIEV2ZScsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG5dO1xuXG4vLyBQaGlsaXBwaW5lIGhvbGlkYXlzIGZvciAyMDI2IChQcm9qZWN0ZWQgYmFzZWQgb24gcmVjdXJyaW5nIGhvbGlkYXlzKVxuY29uc3QgcGhpbGlwcGluZUhvbGlkYXlzMjAyNiA9IFtcbiAgeyBkYXRlOiAnMjAyNi0wMS0wMScsIG5hbWU6ICdOZXcgWWVhclxcJ3MgRGF5JywgdHlwZTogJ1JlZ3VsYXIgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNi0wMi0xNycsIG5hbWU6ICdDaGluZXNlIE5ldyBZZWFyJywgdHlwZTogJ1NwZWNpYWwgTm9uLXdvcmtpbmcgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNi0wMi0yNScsIG5hbWU6ICdFRFNBIFBlb3BsZSBQb3dlciBSZXZvbHV0aW9uIEFubml2ZXJzYXJ5JywgdHlwZTogJ1NwZWNpYWwgV29ya2luZyBEYXknIH0sXG4gIHsgZGF0ZTogJzIwMjYtMDQtMDInLCBuYW1lOiAnTWF1bmR5IFRodXJzZGF5JywgdHlwZTogJ1JlZ3VsYXIgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNi0wNC0wMycsIG5hbWU6ICdHb29kIEZyaWRheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjYtMDQtMDQnLCBuYW1lOiAnQmxhY2sgU2F0dXJkYXknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI2LTA0LTA5JywgbmFtZTogJ0FyYXcgbmcgS2FnaXRpbmdhbicsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjYtMDUtMDEnLCBuYW1lOiAnTGFib3IgRGF5JywgdHlwZTogJ1JlZ3VsYXIgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNi0wNi0xMicsIG5hbWU6ICdJbmRlcGVuZGVuY2UgRGF5JywgdHlwZTogJ1JlZ3VsYXIgSG9saWRheScgfSxcbiAgeyBkYXRlOiAnMjAyNi0wNy0yNycsIG5hbWU6ICdJZ2xlc2lhIG5pIENyaXN0byBBbm5pdmVyc2FyeScsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjYtMDgtMjEnLCBuYW1lOiAnTmlub3kgQXF1aW5vIERheScsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjYtMDgtMzEnLCBuYW1lOiAnTmF0aW9uYWwgSGVyb2VzIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjYtMTEtMDEnLCBuYW1lOiAnQWxsIFNhaW50c1xcJyBEYXknLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI2LTExLTMwJywgbmFtZTogJ0JvbmlmYWNpbyBEYXknLCB0eXBlOiAnUmVndWxhciBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI2LTEyLTA4JywgbmFtZTogJ0ZlYXN0IG9mIHRoZSBJbW1hY3VsYXRlIENvbmNlcHRpb24nLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI2LTEyLTI0JywgbmFtZTogJ0NocmlzdG1hcyBFdmUnLCB0eXBlOiAnU3BlY2lhbCBOb24td29ya2luZyBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI2LTEyLTI1JywgbmFtZTogJ0NocmlzdG1hcyBEYXknLCB0eXBlOiAnUmVndWxhciBIb2xpZGF5JyB9LFxuICB7IGRhdGU6ICcyMDI2LTEyLTMwJywgbmFtZTogJ1JpemFsIERheScsIHR5cGU6ICdSZWd1bGFyIEhvbGlkYXknIH0sXG4gIHsgZGF0ZTogJzIwMjYtMTItMzEnLCBuYW1lOiAnTmV3IFllYXJcXCdzIEV2ZScsIHR5cGU6ICdTcGVjaWFsIE5vbi13b3JraW5nIEhvbGlkYXknIH0sXG5dO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgeWVhciA9IHNlYXJjaFBhcmFtcy5nZXQoJ3llYXInKTtcblxuICAgIGxldCBob2xpZGF5cztcblxuICAgIGlmICh5ZWFyID09PSAnMjAyNScpIHtcbiAgICAgIGhvbGlkYXlzID0gcGhpbGlwcGluZUhvbGlkYXlzMjAyNTtcbiAgICB9IGVsc2UgaWYgKHllYXIgPT09ICcyMDI2Jykge1xuICAgICAgaG9saWRheXMgPSBwaGlsaXBwaW5lSG9saWRheXMyMDI2O1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBEZWZhdWx0IHRvIDIwMjQgaWYgbm8geWVhciBzcGVjaWZpZWQgb3IgYW55IG90aGVyIHllYXJcbiAgICAgIGhvbGlkYXlzID0gcGhpbGlwcGluZUhvbGlkYXlzMjAyNDtcbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBob2xpZGF5cyB9KTtcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGhvbGlkYXlzOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UgfHwgJ1NvbWV0aGluZyB3ZW50IHdyb25nJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImR5bmFtaWMiLCJwaGlsaXBwaW5lSG9saWRheXMyMDI0IiwiZGF0ZSIsIm5hbWUiLCJ0eXBlIiwicGhpbGlwcGluZUhvbGlkYXlzMjAyNSIsInBoaWxpcHBpbmVIb2xpZGF5czIwMjYiLCJHRVQiLCJyZXF1ZXN0Iiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwieWVhciIsImdldCIsImhvbGlkYXlzIiwianNvbiIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiLCJzdGF0dXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/calendar/holidays/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fholidays%2Froute&page=%2Fapi%2Fcalendar%2Fholidays%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fholidays%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();