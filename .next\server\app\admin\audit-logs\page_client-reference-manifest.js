globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/admin/audit-logs/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/components/admin/AuditLogList.tsx":{"*":{"id":"(ssr)/./src/components/admin/AuditLogList.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/NotificationDropdown.tsx":{"*":{"id":"(ssr)/./src/components/NotificationDropdown.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/layout.tsx":{"*":{"id":"(ssr)/./src/app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AuthProvider.tsx":{"*":{"id":"(ssr)/./src/components/AuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/PerformanceMonitor.tsx":{"*":{"id":"(ssr)/./src/components/PerformanceMonitor.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/DarkModeContext.tsx":{"*":{"id":"(ssr)/./src/contexts/DarkModeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/SettingsContext.tsx":{"*":{"id":"(ssr)/./src/contexts/SettingsContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/admin/DashboardTabs.tsx":{"*":{"id":"(ssr)/./src/components/admin/DashboardTabs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/feedback/FeedbackList.tsx":{"*":{"id":"(ssr)/./src/components/feedback/FeedbackList.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\components\\admin\\AuditLogList.tsx":{"id":"(app-pages-browser)/./src/components/admin/AuditLogList.tsx","name":"*","chunks":["app/admin/audit-logs/page","static/chunks/app/admin/audit-logs/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\components\\NotificationDropdown.tsx":{"id":"(app-pages-browser)/./src/components/NotificationDropdown.tsx","name":"*","chunks":["app/admin/audit-logs/page","static/chunks/app/admin/audit-logs/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\app\\admin\\layout.tsx":{"id":"(app-pages-browser)/./src/app/admin/layout.tsx","name":"*","chunks":["app/admin/layout","static/chunks/app/admin/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\app\\fonts.css":{"id":"(app-pages-browser)/./src/app/fonts.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\components\\AuthProvider.tsx":{"id":"(app-pages-browser)/./src/components/AuthProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\components\\PerformanceMonitor.tsx":{"id":"(app-pages-browser)/./src/components/PerformanceMonitor.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\contexts\\DarkModeContext.tsx":{"id":"(app-pages-browser)/./src/contexts/DarkModeContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\contexts\\SettingsContext.tsx":{"id":"(app-pages-browser)/./src/contexts/SettingsContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\components\\admin\\DashboardTabs.tsx":{"id":"(app-pages-browser)/./src/components/admin/DashboardTabs.tsx","name":"*","chunks":["app/admin/page","static/chunks/app/admin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\components\\feedback\\FeedbackList.tsx":{"id":"(app-pages-browser)/./src/components/feedback/FeedbackList.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\":[],"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\app\\admin\\layout":[],"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\app\\admin\\page":[],"C:\\Users\\<USER>\\Desktop\\DocumentTracker\\src\\app\\admin\\audit-logs\\page":[]}}