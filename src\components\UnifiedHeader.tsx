'use client';

import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import { useState, useRef, useEffect, memo } from 'react';
import { signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { UserRole } from '@/types';
import dynamic from 'next/dynamic';
import DarkModeToggle from './DarkModeToggle';

// Dynamically import non-critical components
const RealTimeNotifications = dynamic(() => import('./notifications/RealTimeNotifications'), {
  ssr: false,
  loading: () => (
    <div className="w-8 h-8 flex items-center justify-center">
      <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
    </div>
  )
});

const ColorSchemeToggle = dynamic(() => import('./ColorSchemeToggle'), {
  ssr: false,
  loading: () => (
    <div className="w-8 h-8 flex items-center justify-center">
      <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
    </div>
  )
});

interface UnifiedHeaderProps {
  isAdminSection?: boolean;
}

function UnifiedHeader({ isAdminSection = false }: UnifiedHeaderProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsProfileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="sticky top-0 z-40 transition-colors duration-200 shadow-sm border-b" style={{
      backgroundColor: 'rgba(var(--theme-bg-secondary), 1)',
      borderColor: 'rgba(var(--theme-border-primary), 1)'
    }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and App Name */}
          <div className="flex items-center">
            {/* Add left padding on mobile to make room for the menu button */}
            <Link href={isAdminSection ? "/admin" : "/dashboard"} className="flex-shrink-0 flex items-center group pl-10 md:pl-0">
              <div className="relative h-10 w-10 overflow-hidden rounded-xl shadow-sm transition-all duration-200 group-hover:shadow-md">
                <Image
                  src="/MGB Logo_Proxy.png"
                  alt="MGB Logo"
                  fill
                  sizes="40px"
                  priority
                  fetchPriority="high"
                  className="object-cover transition-transform duration-200 group-hover:scale-105"
                />
              </div>
              <div className="ml-3">
                <div className="text-secondary-500 dark:text-secondary-400 font-bold text-lg transition-colors">
                  {isAdminSection ? 'MGB Admin' : 'MGB'}
                </div>
                <div className="text-xs text-accent-green-600 dark:text-accent-green-400 transition-colors">
                  {isAdminSection ? 'Administration Panel' : 'Document Management System'}
                </div>
              </div>
            </Link>
          </div>

          {/* Right side controls */}
          <div className="flex items-center space-x-4">

            {/* Real-time Notifications */}
            <RealTimeNotifications />

            {/* Color Scheme Toggle */}
            <ColorSchemeToggle />

            {/* Dark Mode Toggle */}
            <DarkModeToggle />

            {/* User Profile Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="flex items-center space-x-3 focus:outline-none"
                aria-expanded={isProfileOpen}
                aria-haspopup="true"
              >
                <div className="relative h-9 w-9 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center text-white shadow-sm overflow-hidden transition-transform hover:scale-105">
                  {session?.user?.name?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors">
                    {session?.user?.name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 transition-colors">
                    {session?.user?.role === UserRole.ADMIN ? 'Administrator' : session?.user?.division}
                  </div>
                </div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${isProfileOpen ? 'rotate-180' : ''}`}
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>

              {/* Dropdown menu */}
              {isProfileOpen && (
                <div
                  className="origin-top-right absolute right-0 mt-2 w-56 rounded-xl shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50 transition-all duration-200 ease-out animate-dropdown"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="user-menu"
                >
                  <div className="py-2" role="none">
                    <div className="block px-4 py-3 text-sm border-b border-gray-100 dark:border-gray-700">
                      <p className="font-medium text-gray-900 dark:text-white">{session?.user?.name}</p>
                      <p className="text-gray-500 dark:text-gray-400 text-xs mt-0.5">{session?.user?.email}</p>
                    </div>

                    <Link
                      href="/profile"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      role="menuitem"
                      onClick={() => setIsProfileOpen(false)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                      My Profile
                    </Link>

                    <Link
                      href="/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      role="menuitem"
                      onClick={() => setIsProfileOpen(false)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      Settings
                    </Link>

                    {/* Navigation links based on context */}
                    {!isAdminSection && (session?.user?.role === UserRole.ADMIN || session?.user?.role === UserRole.REGIONAL_DIRECTOR) && (
                      <Link
                        href="/admin"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        role="menuitem"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Admin Panel
                      </Link>
                    )}

                    <div className="border-t border-gray-100 dark:border-gray-700 my-1"></div>

                    <button
                      onClick={() => {
                        const callbackUrl = window.location.origin;
                        console.log('Signing out with callback URL:', callbackUrl);
                        signOut({ callbackUrl });
                      }}
                      className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                      role="menuitem"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm6.293 11.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L11.586 9H5a1 1 0 100 2h6.586l-2.293 2.293z" clipRule="evenodd" />
                      </svg>
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(UnifiedHeader);
