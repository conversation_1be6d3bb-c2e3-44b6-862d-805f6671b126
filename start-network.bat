@echo off
color 0A
echo ================================================================
echo           Document Tracker - Network Access
echo ================================================================
echo.

echo This will start the Document Tracker system with network access
echo so other users on the same network can connect to it.
echo.

echo Choose your startup mode:
echo [1] Development Mode (with hot reload)
echo [2] Production Mode (optimized)
echo [3] Custom Port
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto dev_mode
if "%choice%"=="2" goto prod_mode
if "%choice%"=="3" goto custom_port
goto invalid_choice

:dev_mode
echo.
echo Starting in Development Mode...
node scripts/start-network.js --dev
goto end

:prod_mode
echo.
echo Starting in Production Mode...
echo Note: Make sure you have built the application first (npm run build)
node scripts/start-network.js
goto end

:custom_port
echo.
set /p port="Enter port number (default 3000): "
if "%port%"=="" set port=3000
echo.
echo Starting with custom port %port%...
node scripts/start-network.js --dev --port=%port%
goto end

:invalid_choice
echo.
echo Invalid choice. Starting in Development Mode...
node scripts/start-network.js --dev
goto end

:end
