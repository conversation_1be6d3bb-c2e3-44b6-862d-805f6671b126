{"name": "document-tracker", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:network": "next dev -H 0.0.0.0", "dev:custom": "node server.js", "dev:domain": "node server.js --host=doctracker.local", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "start:network": "next start -H 0.0.0.0", "start:custom": "NODE_ENV=production node server.js", "start:domain": "NODE_ENV=production node server.js --host=doctracker.local", "network": "node scripts/start-network.js", "network:dev": "node scripts/start-network.js --dev", "network:fixed": "node scripts/start-network-fixed.js", "network:fixed:dev": "node scripts/start-network-fixed.js --dev", "setup:domain": "node scripts/setup-local-domain.js", "lint": "next lint", "clean": "rimraf .next", "clean:cache": "rimraf .next/cache", "dev:clean": "npm run clean:cache && next dev", "analyze": "node scripts/analyze-bundle.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:gemini": "jest src/lib/gemini/__tests__/", "performance:setup": "node scripts/setup-performance.js", "performance:monitor": "node scripts/monitor-performance.js", "db:indexes": "node scripts/setup-performance.js"}, "dependencies": {"@auth/core": "^0.34.2", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/archiver": "^6.0.3", "@types/bcrypt": "^5.0.2", "@types/node": "^20.10.5", "@types/os-utils": "^0.0.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "archiver": "^7.0.1", "autoprefixer": "^10.4.16", "base64-img": "^1.0.4", "bcrypt": "^5.1.1", "chart.js": "^4.4.9", "color": "^5.0.0", "critters": "^0.0.23", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "eslint": "^8.56.0", "eslint-config-next": "^14.2.28", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.510.0", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "next": "^14.2.28", "next-auth": "^4.24.5", "os-utils": "^0.0.14", "pdfmake": "^0.2.20", "postcss": "^8.4.32", "qrcode-generator": "^1.4.4", "react": "^18.2.0", "react-calendar": "^5.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-qrcode-logo": "^3.0.0", "tailwindcss": "^3.4.0", "tailwindcss-palette": "^0.0.0", "typescript": "^5.3.3", "web-vitals": "^5.0.1"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/color": "^4.2.0", "@types/jest": "^29.5.8", "@types/pdfmake": "^0.2.11", "@types/react-calendar": "^4.1.0", "@types/uuid": "^10.0.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "rimraf": "^6.0.1"}}