/**
 * Network Startup Script for Document Tracker
 * Starts the application with network access for multiple users
 */

const { spawn } = require('child_process');
const os = require('os');

// Get command line arguments
const args = process.argv.slice(2);
const isDev = args.includes('--dev');
const port = args.find(arg => arg.startsWith('--port='))?.split('=')[1] || '3000';

console.log('🌐 Document Tracker - Network Access Setup');
console.log('==========================================');
console.log('');

// Get network interfaces
function getNetworkInterfaces() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip internal and non-IPv4 addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        addresses.push({
          name: name,
          address: interface.address,
          netmask: interface.netmask
        });
      }
    }
  }
  
  return addresses;
}

// Display network information
function displayNetworkInfo() {
  const interfaces = getNetworkInterfaces();
  
  console.log('📡 Network Information:');
  console.log('');
  
  if (interfaces.length === 0) {
    console.log('❌ No network interfaces found');
    console.log('   Make sure you are connected to a network');
    return false;
  }
  
  interfaces.forEach((iface, index) => {
    console.log(`   ${index + 1}. ${iface.name}: ${iface.address}`);
  });
  
  console.log('');
  console.log('🌍 Your application will be accessible at:');
  console.log(`   • Local: http://localhost:${port}`);
  
  interfaces.forEach(iface => {
    console.log(`   • Network: http://${iface.address}:${port}`);
  });
  
  console.log('');
  console.log('👥 Other users on the same network can access using:');
  interfaces.forEach(iface => {
    console.log(`   http://${iface.address}:${port}`);
  });
  
  console.log('');
  return true;
}

// Start the application
function startApplication() {
  console.log('🚀 Starting Document Tracker...');
  console.log('');
  
  const command = isDev ? 'npm' : 'npm';
  const scriptArgs = isDev ? ['run', 'dev', '--', '-H', '0.0.0.0', '-p', port] : ['run', 'start', '--', '-H', '0.0.0.0', '-p', port];
  
  console.log(`📝 Running: ${command} ${scriptArgs.join(' ')}`);
  console.log('');
  
  const child = spawn(command, scriptArgs, {
    stdio: 'inherit',
    shell: true
  });
  
  child.on('error', (error) => {
    console.error('❌ Failed to start application:', error.message);
    process.exit(1);
  });
  
  child.on('close', (code) => {
    console.log('');
    console.log(`📊 Application exited with code ${code}`);
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('');
    console.log('🛑 Shutting down Document Tracker...');
    child.kill('SIGINT');
  });
  
  process.on('SIGTERM', () => {
    console.log('');
    console.log('🛑 Shutting down Document Tracker...');
    child.kill('SIGTERM');
  });
}

// Display usage instructions
function displayUsage() {
  console.log('📖 Network Access Instructions:');
  console.log('');
  console.log('1. Share the network URL with other users');
  console.log('2. Make sure Windows Firewall allows the connection');
  console.log('3. Users need to be on the same network (WiFi/LAN)');
  console.log('4. First user should create an admin account');
  console.log('');
  console.log('🔧 Troubleshooting:');
  console.log('• If users cannot connect, check Windows Firewall');
  console.log('• Make sure all devices are on the same network');
  console.log('• Try disabling antivirus temporarily');
  console.log('• Check if port 3000 is blocked');
  console.log('');
  console.log('🛑 To stop the server: Press Ctrl+C');
  console.log('');
}

// Main execution
function main() {
  const hasNetwork = displayNetworkInfo();
  
  if (!hasNetwork) {
    console.log('❌ Cannot start network access without network connection');
    process.exit(1);
  }
  
  displayUsage();
  
  console.log('⏳ Starting in 3 seconds...');
  console.log('');
  
  setTimeout(() => {
    startApplication();
  }, 3000);
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  getNetworkInterfaces,
  displayNetworkInfo,
  startApplication
};
