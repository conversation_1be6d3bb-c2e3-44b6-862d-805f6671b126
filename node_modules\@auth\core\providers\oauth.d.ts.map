{"version": 3, "file": "oauth.d.ts", "sourceRoot": "", "sources": ["../src/providers/oauth.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAc,MAAM,cAAc,CAAA;AACtD,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAA;AAClE,OAAO,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,aAAa,CAAA;AACrE,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,aAAa,CAAA;AAG7C,KAAK,uBAAuB,GAAG,GAAG,CAAA;AAClC,KAAK,kBAAkB,GAAG,GAAG,CAAA;AAC7B,KAAK,cAAc,GAAG,GAAG,CAAA;AACzB,KAAK,mBAAmB,GAAG,GAAG,CAAA;AAC9B,KAAK,oBAAoB,GAAG,GAAG,CAAA;AAE/B,YAAY,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAA;AAEzD,MAAM,MAAM,WAAW,GAAG,oBAAoB,GAAG,mBAAmB,CAAA;AAEpE,KAAK,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,GAAG,QAAQ,CAAC,CAAC,CAAA;AAE9E,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;AA0CxC,MAAM,MAAM,4BAA4B,GACtC,eAAe,CAAC,uBAAuB,CAAC,CAAA;AAE1C,MAAM,MAAM,oBAAoB,GAAG,eAAe,CAChD,SAAS,EACT;IACE;;;OAGG;IACH,MAAM,EAAE,kBAAkB,CAAA;IAC1B;;;OAGG;IACH,MAAM,EAAE,WAAW,CAAA;CACpB,EACD;IACE,MAAM,EAAE,QAAQ,CAAA;CACjB,CACF,CAAA;AAED,MAAM,MAAM,uBAAuB,GAAG,eAAe,CACnD,SAAS,EACT;IAAE,MAAM,EAAE,QAAQ,CAAA;CAAE,EACpB,OAAO,CACR,CAAA;AAED,MAAM,MAAM,eAAe,CAAC,OAAO,IAAI,CACrC,OAAO,EAAE,OAAO,EAChB,MAAM,EAAE,QAAQ,KACb,SAAS,CAAC,IAAI,CAAC,CAAA;AAEpB,MAAM,MAAM,eAAe,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAA;AAE/E,MAAM,WAAW,yBAAyB;IACxC,IAAI,CAAC,EAAE,MAAM,CAAA;IACb;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAA;IACb;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAA;IACX,UAAU,CAAC,EAAE,MAAM,CAAA;CACpB;AAED,qBAAqB;AACrB,MAAM,WAAW,YAAY,CAAC,OAAO,CACnC,SAAQ,qBAAqB,EAC3B,aAAa;IACf;;;;;;;;OAQG;IACH,EAAE,EAAE,MAAM,CAAA;IACV,mEAAmE;IACnE,IAAI,EAAE,MAAM,CAAA;IACZ;;;;;;;;OAQG;IACH,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,MAAM,CAAC,EAAE,MAAM,CAAA;IACf;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,4BAA4B,CAAA;IACrD,KAAK,CAAC,EAAE,MAAM,GAAG,oBAAoB,CAAA;IACrC,QAAQ,CAAC,EAAE,MAAM,GAAG,uBAAuB,CAAA;IAC3C,IAAI,EAAE,OAAO,CAAA;IACb;;;;;;;OAOG;IACH,OAAO,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,CAAA;IAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,OAAO,CAAC,EAAE,eAAe,CAAA;IACzB;;;;;;;;;;OAUG;IACH,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,GAAG,MAAM,CAAC,CAAA;IACzC,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,YAAY,CAAC,EAAE,MAAM,CAAA;IACrB;;;OAGG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;IACxB,KAAK,CAAC,EAAE,yBAAyB,CAAA;IACjC;;;;;;;;;;;;OAYG;IACH,iCAAiC,CAAC,EAAE,OAAO,CAAA;IAC3C,gBAAgB,CAAC,EAAE,UAAU,CAAC,kBAAkB,CAAC,CAAA;CASlD;AAED;;;;GAIG;AACH,MAAM,WAAW,UAAU,CAAC,OAAO,CACjC,SAAQ,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC;IACtD,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAA;IAC9E;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAA;CAClB;AAED,MAAM,MAAM,WAAW,CAAC,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAA;AAE9E,MAAM,MAAM,iBAAiB,GAAG,eAAe,GAAG,OAAO,GAAG,UAAU,CAAA;AAqCtE,MAAM,MAAM,kBAAkB,CAAC,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,GAAG;IACvE,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAA;IACrC,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAA;CACxC,CAAA;AAED,MAAM,MAAM,eAAe,CAAC,OAAO,IAAI,IAAI,CACzC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAC7B,SAAS,GAAG,MAAM,CACnB,CAAA;AAED,MAAM,MAAM,cAAc,CAAC,OAAO,IAAI,IAAI,CACxC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAC5B,SAAS,GAAG,MAAM,CACnB,CAAA"}