"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx":
/*!**************************************************!*\
  !*** ./src/components/chatbot/ChatInterface.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AlertProvider */ \"(app-pages-browser)/./src/components/AlertProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nvar _s = $RefreshSig$();\n// Fixed version - no localStorage session restoration\n\n\n\n\nfunction ChatInterface(param) {\n    var _this = this;\n    var isOpen = param.isOpen, onClose = param.onClose, documentContext = param.documentContext;\n    _s();\n    var _useSession = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)(), session = _useSession.data;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var showAlert = (0,_components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__.useAlert)().showAlert;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), messages = _useState[0], setMessages = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), inputMessage = _useState1[0], setInputMessage = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isLoading = _useState2[0], setIsLoading = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), sessionId = _useState3[0], setSessionId = _useState3[1];\n    var _useState4 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isTyping = _useState4[0], setIsTyping = _useState4[1];\n    var messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Clean up any old chat session data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        // Clear any existing chatbot localStorage data to prevent conflicts\n        localStorage.removeItem(\"chatbot_session_id\");\n        localStorage.removeItem(\"chatbot_session_time\");\n    }, []);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        messages\n    ]);\n    // Focus input when chat opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isOpen && inputRef.current) {\n            inputRef.current.focus();\n        }\n    }, [\n        isOpen\n    ]);\n    // Start chat session when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isOpen && !sessionId && (session === null || session === void 0 ? void 0 : session.user)) {\n            // Always start a fresh session instead of trying to restore\n            // This ensures compatibility with browser restarts and auth session changes\n            console.log(\"Starting fresh chat session for user:\", session.user.name);\n            // Clear any old session data to prevent conflicts\n            localStorage.removeItem(\"chatbot_session_id\");\n            localStorage.removeItem(\"chatbot_session_time\");\n            startChatSession();\n        }\n    }, [\n        isOpen,\n        session\n    ]);\n    // Update document context when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (sessionId && documentContext) {\n            updateDocumentContext();\n        }\n    }, [\n        sessionId,\n        documentContext\n    ]);\n    var startChatSession = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var response, errorData, data, _session_user, welcomeMessage, error, fallbackMessage;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            5,\n                            6,\n                            7\n                        ]);\n                        setIsLoading(true);\n                        console.log(\"Starting chat session...\");\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"start_session\",\n                                    context: {\n                                        currentPage: pathname,\n                                        systemState: {\n                                            timestamp: new Date().toISOString()\n                                        }\n                                    }\n                                })\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!!response.ok) return [\n                            3,\n                            3\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        errorData = _state.sent();\n                        console.error(\"Failed to start chat session:\", errorData);\n                        throw new Error(errorData.message || \"Failed to start chat session\");\n                    case 3:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 4:\n                        data = _state.sent();\n                        console.log(\"Chat session started:\", data);\n                        setSessionId(data.data.sessionId);\n                        // If no messages returned, add a welcome message\n                        if (!data.data.messages || data.data.messages.length === 0) {\n                            ;\n                            welcomeMessage = {\n                                id: \"welcome_\".concat(Date.now()),\n                                role: \"assistant\",\n                                content: \"Hello \".concat((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || \"there\", \"! \\uD83D\\uDC4B\\n\\nI'm MGB Bot, your AI assistant for the Document Tracker system. I can help you with:\\n\\n• **Document workflows** - How to send, receive, and process documents\\n• **System navigation** - Finding features and pages\\n• **Status explanations** - Understanding document statuses\\n• **Troubleshooting** - Solving common issues\\n\\nWhat would you like to know?\"),\n                                timestamp: new Date(),\n                                quickReplies: [\n                                    \"How to send documents?\",\n                                    \"Check my inbox\",\n                                    \"Document workflow\",\n                                    \"System help\"\n                                ]\n                            };\n                            setMessages([\n                                welcomeMessage\n                            ]);\n                        } else {\n                            setMessages(data.data.messages);\n                        }\n                        return [\n                            3,\n                            7\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        console.error(\"Error starting chat session:\", error);\n                        // Show fallback welcome message even if session creation fails\n                        fallbackMessage = {\n                            id: \"fallback_\".concat(Date.now()),\n                            role: \"assistant\",\n                            content: \"Hello! \\uD83D\\uDC4B\\n\\nI'm MGB Bot, your AI assistant. I'm having trouble connecting to the server right now, but I can still help you with basic information about the Document Tracker system.\\n\\nTry typing a message and I'll do my best to assist you!\",\n                            timestamp: new Date(),\n                            quickReplies: [\n                                \"How to send documents?\",\n                                \"Check my inbox\",\n                                \"Document workflow\"\n                            ]\n                        };\n                        setMessages([\n                            fallbackMessage\n                        ]);\n                        showAlert({\n                            message: \"Chat session started in offline mode\",\n                            type: \"warning\"\n                        });\n                        return [\n                            3,\n                            7\n                        ];\n                    case 6:\n                        setIsLoading(false);\n                        return [\n                            7\n                        ];\n                    case 7:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function startChatSession() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var updateDocumentContext = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!sessionId || !documentContext) return [\n                            2\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"update_document_context\",\n                                    sessionId: sessionId,\n                                    documentData: documentContext\n                                })\n                            })\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error updating document context:\", error);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function updateDocumentContext() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var sendMessage = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function(message) {\n            var userMessage, response, errorData, data, error, _error_message;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!message.trim() || isLoading) return [\n                            2\n                        ];\n                        if (!!sessionId) return [\n                            3,\n                            2\n                        ];\n                        showAlert({\n                            message: \"Starting chat session...\",\n                            type: \"info\"\n                        });\n                        return [\n                            4,\n                            startChatSession()\n                        ];\n                    case 1:\n                        _state.sent();\n                        // Wait a bit for session to be created, then try again\n                        setTimeout(function() {\n                            if (sessionId) {\n                                sendMessage(message);\n                            }\n                        }, 1000);\n                        return [\n                            2\n                        ];\n                    case 2:\n                        userMessage = {\n                            id: \"temp_\".concat(Date.now()),\n                            role: \"user\",\n                            content: message,\n                            timestamp: new Date()\n                        };\n                        setMessages(function(prev) {\n                            return (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(prev).concat([\n                                userMessage\n                            ]);\n                        });\n                        setInputMessage(\"\");\n                        setIsLoading(true);\n                        setIsTyping(true);\n                        _state.label = 3;\n                    case 3:\n                        _state.trys.push([\n                            3,\n                            8,\n                            9,\n                            10\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"send_message\",\n                                    sessionId: sessionId,\n                                    message: message,\n                                    context: {\n                                        currentPage: pathname\n                                    }\n                                })\n                            })\n                        ];\n                    case 4:\n                        response = _state.sent();\n                        if (!!response.ok) return [\n                            3,\n                            6\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 5:\n                        errorData = _state.sent();\n                        // Handle session expiration\n                        if (response.status === 410 && errorData.error === \"SESSION_EXPIRED\") {\n                            showAlert({\n                                message: \"Chat session expired. Starting a new conversation...\",\n                                type: \"warning\"\n                            });\n                            // Clear current session and start a new one\n                            setSessionId(null);\n                            setMessages([]);\n                            // Restart the session\n                            setTimeout(function() {\n                                if (session === null || session === void 0 ? void 0 : session.user) {\n                                    startChatSession();\n                                }\n                            }, 1000);\n                            return [\n                                2\n                            ];\n                        }\n                        throw new Error(errorData.message || \"Failed to send message\");\n                    case 6:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 7:\n                        data = _state.sent();\n                        setMessages(data.data.messages);\n                        return [\n                            3,\n                            10\n                        ];\n                    case 8:\n                        error = _state.sent();\n                        console.error(\"Error sending message:\", error);\n                        // Don't show error for session expiration as we handle it above\n                        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"session expired\"))) {\n                            showAlert({\n                                message: error.message || \"Failed to send message\",\n                                type: \"error\"\n                            });\n                        }\n                        return [\n                            3,\n                            10\n                        ];\n                    case 9:\n                        setIsLoading(false);\n                        setIsTyping(false);\n                        return [\n                            7\n                        ];\n                    case 10:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function sendMessage(message) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var handleQuickReply = function(reply) {\n        sendMessage(reply);\n    };\n    var handleAction = function(action) {\n        switch(action.type){\n            case \"navigate\":\n                var _action_data;\n                if ((_action_data = action.data) === null || _action_data === void 0 ? void 0 : _action_data.url) {\n                    window.location.href = action.data.url;\n                }\n                break;\n            case \"search\":\n                var _action_data1;\n                // Implement search functionality\n                sendMessage(\"Help me search for: \".concat(((_action_data1 = action.data) === null || _action_data1 === void 0 ? void 0 : _action_data1.query) || \"documents\"));\n                break;\n            case \"help\":\n                var _action_data2, _action_data3;\n                // Handle different help topics\n                if (((_action_data2 = action.data) === null || _action_data2 === void 0 ? void 0 : _action_data2.topic) === \"getting_started\") {\n                    sendMessage(\"Show me a quick tour of the system\");\n                } else if (((_action_data3 = action.data) === null || _action_data3 === void 0 ? void 0 : _action_data3.topic) === \"features\") {\n                    sendMessage(\"What are the main features of the document tracking system?\");\n                } else {\n                    sendMessage(\"I need help with the system\");\n                }\n                break;\n            case \"info\":\n                var _action_data4;\n                // Handle info requests\n                if (((_action_data4 = action.data) === null || _action_data4 === void 0 ? void 0 : _action_data4.topic) === \"features\") {\n                    sendMessage(\"Tell me about the system features\");\n                } else {\n                    sendMessage(\"Give me more information about the system\");\n                }\n                break;\n            case \"create\":\n                var _action_data5;\n                // Handle document creation\n                if ((_action_data5 = action.data) === null || _action_data5 === void 0 ? void 0 : _action_data5.url) {\n                    window.location.href = action.data.url;\n                } else {\n                    sendMessage(\"How do I create a new document?\");\n                }\n                break;\n            default:\n                console.log(\"Unknown action:\", action);\n                sendMessage(\"I need help with this feature\");\n        }\n    };\n    // Format message content with basic markdown support\n    var formatMessageContent = function(content) {\n        if (!content || typeof content !== \"string\") {\n            return \"No content available\";\n        }\n        // Simple text formatting without HTML to avoid nesting issues\n        return content.replace(/\\*\\*(.*?)\\*\\*/g, \"$1\") // Remove bold markers for now\n        .replace(/\\*(.*?)\\*/g, \"$1\") // Remove italic markers for now\n        .replace(/•/g, \"•\"); // Keep bullet points\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-end justify-end p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black bg-opacity-25\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-96 h-[600px] flex flex-col border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-lg\",\n                                                    children: \"\\uD83E\\uDD16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-white\",\n                                                children: \"MGB Bot\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"Typing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-1 flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\",\n                                                                    style: {\n                                                                        animationDelay: \"0.1s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\",\n                                                                    style: {\n                                                                        animationDelay: \"0.2s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this) : \"AI Assistant • Online\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-white hover:text-blue-200 transition-all duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-20 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50\",\n                                title: \"Close MGB Bot\",\n                                \"aria-label\": \"Close chatbot\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2.5,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                        children: [\n                            messages.map(function(message) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                    children: [\n                                        message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"\\uD83E\\uDD16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm \".concat(message.role === \"user\" ? \"bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md\" : \"bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 rounded-bl-md\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                                                    children: formatMessageContent(message.content)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                message.actions && message.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 space-y-2\",\n                                                    children: message.actions.map(function(action, index) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: function() {\n                                                                return handleAction(action);\n                                                            },\n                                                            className: \"flex items-center justify-center w-full px-3 py-2 text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1\",\n                                                                    children: \"\\uD83D\\uDD17\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                action.label\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                message.quickReplies && message.quickReplies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2\",\n                                                    children: message.quickReplies.map(function(reply, index) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: function() {\n                                                                return handleQuickReply(reply);\n                                                            },\n                                                            className: \"px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-sm\",\n                                                            children: [\n                                                                \"\\uD83D\\uDCAC \",\n                                                                reply\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, _this);\n                            }),\n                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"\\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                    children: \"MGB Bot is thinking\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 ml-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.1s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.2s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: inputRef,\n                                                type: \"text\",\n                                                value: inputMessage,\n                                                onChange: function(e) {\n                                                    return setInputMessage(e.target.value);\n                                                },\n                                                onKeyDown: function(e) {\n                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                        e.preventDefault();\n                                                        sendMessage(inputMessage);\n                                                    }\n                                                },\n                                                placeholder: \"Type your message...\",\n                                                disabled: isLoading,\n                                                className: \"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return sendMessage(inputMessage);\n                                        },\n                                        disabled: isLoading || !inputMessage.trim(),\n                                        className: \"px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-sm\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 flex flex-wrap gap-2\",\n                                children: [\n                                    \"How to send documents?\",\n                                    \"Check my inbox\",\n                                    \"Document workflow\"\n                                ].map(function(suggestion, index) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return sendMessage(suggestion);\n                                        },\n                                        disabled: isLoading,\n                                        className: \"px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-full transition-colors duration-200 disabled:opacity-50\",\n                                        children: suggestion\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"Ls2Hyy8WhbzyLp4DHDSclREqoyQ=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__.useAlert\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx\n"));

/***/ })

});