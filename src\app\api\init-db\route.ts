import { NextRequest, NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/document-tracker';

export async function POST(request: NextRequest) {
  const client = new MongoClient(MONGODB_URI);

  try {
    await client.connect();
    console.log('Connected to MongoDB for initialization');

    const db = client.db('document-tracker');

    // Create a test user
    const usersCollection = db.collection('users');
    const testUser = {
      name: 'Test User',
      email: '<EMAIL>',
      role: 'EMPLOYEE',
      division: 'ORD',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const userResult = await usersCollection.insertOne(testUser);
    console.log('Test user created:', userResult.insertedId);

    // Create a test document
    const documentsCollection = db.collection('documents');
    const testDocument = {
      title: 'Test Document',
      description: 'This is a test document to initialize the database',
      category: 'MEMO',
      actionType: 'FOR_INFORMATION',
      senderId: userResult.insertedId,
      recipientId: userResult.insertedId,
      status: 'PENDING',
      trackingNumber: 'DTN-TEST-001',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const docResult = await documentsCollection.insertOne(testDocument);
    console.log('Test document created:', docResult.insertedId);

    // Create a test notification
    const notificationsCollection = db.collection('notifications');
    const testNotification = {
      userId: userResult.insertedId,
      title: 'Welcome to Document Tracker',
      message: 'Your account has been set up successfully',
      type: 'INFO',
      isRead: false,
      createdAt: new Date()
    };

    const notifResult = await notificationsCollection.insertOne(testNotification);
    console.log('Test notification created:', notifResult.insertedId);

    return NextResponse.json({
      success: true,
      message: 'Database initialized successfully!',
      data: {
        userId: userResult.insertedId,
        documentId: docResult.insertedId,
        notificationId: notifResult.insertedId
      }
    });

  } catch (error) {
    console.error('Error initializing database:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to initialize database',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  } finally {
    await client.close();
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Use POST method to initialize the database',
    endpoint: '/api/init-db',
    method: 'POST'
  }, { status: 405 });
}
