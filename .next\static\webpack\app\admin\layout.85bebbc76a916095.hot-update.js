/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/components/chatbot/ChatButton.tsx":
/*!***********************************************!*\
  !*** ./src/components/chatbot/ChatButton.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatButton; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ChatInterface__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _s = $RefreshSig$();\n\n\nfunction ChatButton(param) {\n    var documentContext = param.documentContext;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_3__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isChatOpen = _useState[0], setIsChatOpen = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_3__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isAnimating = _useState1[0], setIsAnimating = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_3__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), 2), showWelcomeMessage = _useState2[0], setShowWelcomeMessage = _useState2[1];\n    // Check localStorage for welcome message preference on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var hasSeenWelcome = localStorage.getItem(\"mgbbot_welcome_dismissed\");\n        if (hasSeenWelcome === \"true\") {\n            setShowWelcomeMessage(false);\n        }\n    }, []);\n    // Add pulsing animation on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var timer = setTimeout(function() {\n            setIsAnimating(true);\n        }, 2000); // Start pulsing after 2 seconds\n        return function() {\n            return clearTimeout(timer);\n        };\n    }, []);\n    // Hide welcome message when chat is opened\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isChatOpen) {\n            setShowWelcomeMessage(false);\n        }\n    }, [\n        isChatOpen\n    ]);\n    // Handle closing welcome message\n    var handleCloseWelcome = function() {\n        setShowWelcomeMessage(false);\n        setIsAnimating(false);\n        // Remember user preference\n        localStorage.setItem(\"mgbbot_welcome_dismissed\", \"true\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 z-40 \".concat(isChatOpen ? \"scale-0\" : \"scale-100\", \" transition-all duration-300\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 rounded-full bg-blue-400 \".concat(isAnimating ? \"animate-ping\" : \"\", \" opacity-75\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 rounded-full bg-blue-400 \".concat(isAnimating ? \"animate-pulse\" : \"\", \" opacity-50\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return setIsChatOpen(true);\n                        },\n                        className: \"relative w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full shadow-2xl transition-all duration-300 flex items-center justify-center group transform hover:scale-110 active:scale-95\",\n                        \"aria-label\": \"Chat with MGB Bot\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 transition-transform group-hover:rotate-12\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H9V3H7C5.9 3 5 3.9 5 5V7C5 8.1 5.9 9 7 9H8V11C8 12.1 8.9 13 10 13H11V15H9C7.9 15 7 15.9 7 17V19C7 20.1 7.9 21 9 21H15C16.1 21 17 20.1 17 19V17C17 15.9 16.1 15 15 15H13V13H14C15.1 13 16 12.1 16 11V9H17C18.1 9 19 8.1 19 7V5C19 3.9 18.1 3 17 3H15V1L21 7V9ZM9 7V5H15V7H9ZM11 9H13V11H11V9ZM9 17H15V19H9V17Z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white animate-bounce\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-red-400 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-full right-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap shadow-lg\",\n                            children: [\n                                \"\\uD83D\\uDCAC Chat with MGB Bot\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-full right-4 border-4 border-transparent border-t-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_ChatInterface__WEBPACK_IMPORTED_MODULE_2___default()), {\n                isOpen: isChatOpen,\n                onClose: function() {\n                    return setIsChatOpen(false);\n                },\n                documentContext: documentContext\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            !isChatOpen && showWelcomeMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-24 right-6 z-30 max-w-xs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl p-4 transform transition-all duration-500 hover:scale-105\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white text-sm\",\n                                            children: \"\\uD83E\\uDD16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                            children: \"Hi! I'm MGB Bot\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-300 mt-1\",\n                                            children: \"Need help with documents? Click to chat with me! \\uD83D\\uDCAC\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCloseWelcome,\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                    title: \"Close welcome message\",\n                                    \"aria-label\": \"Close welcome message\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: 2,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 right-8 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white dark:bg-gray-800 border-r border-b border-gray-200 dark:border-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatButton.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ChatButton, \"0Z8AuvzZEFV9euqNjsi6NssVt7k=\");\n_c = ChatButton;\nvar _c;\n$RefreshReg$(_c, \"ChatButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXRib3QvQ2hhdEJ1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNBO0FBVzdCLFNBQVNHLFdBQVcsS0FBb0M7UUFBcEMsd0JBQUVDOztJQUNuQyxJQUFvQ0osWUFBQUEsK0RBQUFBLENBQUFBLCtDQUFRQSxDQUFDLFlBQXRDSyxhQUE2QkwsY0FBakJNLGdCQUFpQk47SUFDcEMsSUFBc0NBLGFBQUFBLCtEQUFBQSxDQUFBQSwrQ0FBUUEsQ0FBQyxZQUF4Q08sY0FBK0JQLGVBQWxCUSxpQkFBa0JSO0lBQ3RDLElBQW9EQSxhQUFBQSwrREFBQUEsQ0FBQUEsK0NBQVFBLENBQUMsV0FBdERTLHFCQUE2Q1QsZUFBekJVLHdCQUF5QlY7SUFFcEQsNkRBQTZEO0lBQzdEQyxnREFBU0EsQ0FBQztRQUNSLElBQU1VLGlCQUFpQkMsYUFBYUMsT0FBTyxDQUFDO1FBQzVDLElBQUlGLG1CQUFtQixRQUFRO1lBQzdCRCxzQkFBc0I7UUFDeEI7SUFDRixHQUFHLEVBQUU7SUFFTCxpQ0FBaUM7SUFDakNULGdEQUFTQSxDQUFDO1FBQ1IsSUFBTWEsUUFBUUMsV0FBVztZQUN2QlAsZUFBZTtRQUNqQixHQUFHLE9BQU8sZ0NBQWdDO1FBRTFDLE9BQU87bUJBQU1RLGFBQWFGOztJQUM1QixHQUFHLEVBQUU7SUFFTCwyQ0FBMkM7SUFDM0NiLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSUksWUFBWTtZQUNkSyxzQkFBc0I7UUFDeEI7SUFDRixHQUFHO1FBQUNMO0tBQVc7SUFFZixpQ0FBaUM7SUFDakMsSUFBTVkscUJBQXFCO1FBQ3pCUCxzQkFBc0I7UUFDdEJGLGVBQWU7UUFDZiwyQkFBMkI7UUFDM0JJLGFBQWFNLE9BQU8sQ0FBQyw0QkFBNEI7SUFDbkQ7SUFFQSxxQkFDRTs7MEJBRUUsOERBQUNDO2dCQUFJQyxXQUFXLCtCQUFvRSxPQUFyQ2YsYUFBYSxZQUFZLGFBQVk7O2tDQUVsRiw4REFBQ2M7d0JBQUlDLFdBQVcsNkNBQStFLE9BQWxDYixjQUFjLGlCQUFpQixJQUFHOzs7Ozs7a0NBQy9GLDhEQUFDWTt3QkFBSUMsV0FBVyw2Q0FBZ0YsT0FBbkNiLGNBQWMsa0JBQWtCLElBQUc7Ozs7OztrQ0FHaEcsOERBQUNjO3dCQUNDQyxTQUFTO21DQUFNaEIsY0FBYzs7d0JBQzdCYyxXQUFVO3dCQUNWRyxjQUFXO2tDQUdYLDRFQUFDSjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNJO29DQUNDSixXQUFVO29DQUNWSyxNQUFLO29DQUNMQyxTQUFROzhDQUVSLDRFQUFDQzt3Q0FBS0MsR0FBRTs7Ozs7Ozs7Ozs7OENBSVYsOERBQUNUO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNckIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQWtGOzhDQUUvRiw4REFBQ0Q7b0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1yQiw4REFBQ2xCLHVEQUFhQTtnQkFDWjJCLFFBQVF4QjtnQkFDUnlCLFNBQVM7MkJBQU14QixjQUFjOztnQkFDN0JGLGlCQUFpQkE7Ozs7OztZQUlsQixDQUFDQyxjQUFjSSxvQ0FDZCw4REFBQ1U7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNXOzRDQUFLWCxXQUFVO3NEQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHekMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1k7NENBQUdaLFdBQVU7c0RBQXNEOzs7Ozs7c0RBR3BFLDhEQUFDYTs0Q0FBRWIsV0FBVTtzREFBZ0Q7Ozs7Ozs7Ozs7Ozs4Q0FJL0QsOERBQUNDO29DQUNDQyxTQUFTTDtvQ0FDVEcsV0FBVTtvQ0FDVmMsT0FBTTtvQ0FDTlgsY0FBVzs4Q0FFWCw0RUFBQ0M7d0NBQUlKLFdBQVU7d0NBQVVLLE1BQUs7d0NBQU9VLFFBQU87d0NBQWVULFNBQVE7d0NBQVlVLGFBQWE7a0RBQzFGLDRFQUFDVDs0Q0FBS1UsZUFBYzs0Q0FBUUMsZ0JBQWU7NENBQVFWLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSTNELDhEQUFDVDs0QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU0zQjtHQXZId0JqQjtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9jaGF0Ym90L0NoYXRCdXR0b24udHN4PzMxMmIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IENoYXRJbnRlcmZhY2UgZnJvbSAnLi9DaGF0SW50ZXJmYWNlJztcblxuaW50ZXJmYWNlIENoYXRCdXR0b25Qcm9wcyB7XG4gIGRvY3VtZW50Q29udGV4dD86IHtcbiAgICBjdXJyZW50RG9jdW1lbnQ/OiBhbnk7XG4gICAgZG9jdW1lbnRKb3VybmV5PzogYW55W107XG4gICAgcm91dGluZ1NsaXA/OiBhbnlbXTtcbiAgICByZWxhdGVkRG9jdW1lbnRzPzogYW55W107XG4gIH07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENoYXRCdXR0b24oeyBkb2N1bWVudENvbnRleHQgfTogQ2hhdEJ1dHRvblByb3BzKSB7XG4gIGNvbnN0IFtpc0NoYXRPcGVuLCBzZXRJc0NoYXRPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQW5pbWF0aW5nLCBzZXRJc0FuaW1hdGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93V2VsY29tZU1lc3NhZ2UsIHNldFNob3dXZWxjb21lTWVzc2FnZV0gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICAvLyBDaGVjayBsb2NhbFN0b3JhZ2UgZm9yIHdlbGNvbWUgbWVzc2FnZSBwcmVmZXJlbmNlIG9uIG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFzU2VlbldlbGNvbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnbWdiYm90X3dlbGNvbWVfZGlzbWlzc2VkJyk7XG4gICAgaWYgKGhhc1NlZW5XZWxjb21lID09PSAndHJ1ZScpIHtcbiAgICAgIHNldFNob3dXZWxjb21lTWVzc2FnZShmYWxzZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gQWRkIHB1bHNpbmcgYW5pbWF0aW9uIG9uIG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHNldElzQW5pbWF0aW5nKHRydWUpO1xuICAgIH0sIDIwMDApOyAvLyBTdGFydCBwdWxzaW5nIGFmdGVyIDIgc2Vjb25kc1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gIH0sIFtdKTtcblxuICAvLyBIaWRlIHdlbGNvbWUgbWVzc2FnZSB3aGVuIGNoYXQgaXMgb3BlbmVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzQ2hhdE9wZW4pIHtcbiAgICAgIHNldFNob3dXZWxjb21lTWVzc2FnZShmYWxzZSk7XG4gICAgfVxuICB9LCBbaXNDaGF0T3Blbl0pO1xuXG4gIC8vIEhhbmRsZSBjbG9zaW5nIHdlbGNvbWUgbWVzc2FnZVxuICBjb25zdCBoYW5kbGVDbG9zZVdlbGNvbWUgPSAoKSA9PiB7XG4gICAgc2V0U2hvd1dlbGNvbWVNZXNzYWdlKGZhbHNlKTtcbiAgICBzZXRJc0FuaW1hdGluZyhmYWxzZSk7XG4gICAgLy8gUmVtZW1iZXIgdXNlciBwcmVmZXJlbmNlXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ21nYmJvdF93ZWxjb21lX2Rpc21pc3NlZCcsICd0cnVlJyk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qIEZsb2F0aW5nIENoYXQgQnV0dG9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BmaXhlZCBib3R0b20tNiByaWdodC02IHotNDAgJHtpc0NoYXRPcGVuID8gJ3NjYWxlLTAnIDogJ3NjYWxlLTEwMCd9IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMGB9PlxuICAgICAgICB7LyogUHVsc2luZyBSaW5nIEFuaW1hdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQtZnVsbCBiZy1ibHVlLTQwMCAke2lzQW5pbWF0aW5nID8gJ2FuaW1hdGUtcGluZycgOiAnJ30gb3BhY2l0eS03NWB9PjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgcm91bmRlZC1mdWxsIGJnLWJsdWUtNDAwICR7aXNBbmltYXRpbmcgPyAnYW5pbWF0ZS1wdWxzZScgOiAnJ30gb3BhY2l0eS01MGB9PjwvZGl2PlxuXG4gICAgICAgIHsvKiBNYWluIEJ1dHRvbiAqL31cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQ2hhdE9wZW4odHJ1ZSl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy0xNiBoLTE2IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1ibHVlLTYwMCBob3Zlcjpmcm9tLWJsdWUtNjAwIGhvdmVyOnRvLWJsdWUtNzAwIHRleHQtd2hpdGUgcm91bmRlZC1mdWxsIHNoYWRvdy0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdyb3VwIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMTAgYWN0aXZlOnNjYWxlLTk1XCJcbiAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2hhdCB3aXRoIE1HQiBCb3RcIlxuICAgICAgICA+XG4gICAgICAgICAgey8qIFJvYm90IEljb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTggaC04IHRyYW5zaXRpb24tdHJhbnNmb3JtIGdyb3VwLWhvdmVyOnJvdGF0ZS0xMlwiXG4gICAgICAgICAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHBhdGggZD1cIk0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVY3TDE1IDFIOVYzSDdDNS45IDMgNSAzLjkgNSA1VjdDNSA4LjEgNS45IDkgNyA5SDhWMTFDOCAxMi4xIDguOSAxMyAxMCAxM0gxMVYxNUg5QzcuOSAxNSA3IDE1LjkgNyAxN1YxOUM3IDIwLjEgNy45IDIxIDkgMjFIMTVDMTYuMSAyMSAxNyAyMC4xIDE3IDE5VjE3QzE3IDE1LjkgMTYuMSAxNSAxNSAxNUgxM1YxM0gxNEMxNS4xIDEzIDE2IDEyLjEgMTYgMTFWOUgxN0MxOC4xIDkgMTkgOC4xIDE5IDdWNUMxOSAzLjkgMTguMSAzIDE3IDNIMTVWMUwyMSA3VjlaTTkgN1Y1SDE1VjdIOVpNMTEgOUgxM1YxMUgxMVY5Wk05IDE3SDE1VjE5SDlWMTdaXCIvPlxuICAgICAgICAgICAgPC9zdmc+XG5cbiAgICAgICAgICAgIHsvKiBOb3RpZmljYXRpb24gRG90ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgdy0zIGgtMyBiZy1yZWQtNTAwIHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItd2hpdGUgYW5pbWF0ZS1ib3VuY2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIGJnLXJlZC00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcGluZ1wiPjwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgIHsvKiBIZWxwZXIgVGV4dCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tZnVsbCByaWdodC0wIG1iLTIgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDAgcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAgdGV4dC13aGl0ZSBweC0zIHB5LTIgcm91bmRlZC1sZyB0ZXh0LXNtIHdoaXRlc3BhY2Utbm93cmFwIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAg8J+SrCBDaGF0IHdpdGggTUdCIEJvdFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtZnVsbCByaWdodC00IGJvcmRlci00IGJvcmRlci10cmFuc3BhcmVudCBib3JkZXItdC1ncmF5LTkwMFwiPjwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ2hhdCBJbnRlcmZhY2UgKi99XG4gICAgICA8Q2hhdEludGVyZmFjZVxuICAgICAgICBpc09wZW49e2lzQ2hhdE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzQ2hhdE9wZW4oZmFsc2UpfVxuICAgICAgICBkb2N1bWVudENvbnRleHQ9e2RvY3VtZW50Q29udGV4dH1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBXZWxjb21lIE1lc3NhZ2UgZm9yIEZpcnN0LXRpbWUgVXNlcnMgKi99XG4gICAgICB7IWlzQ2hhdE9wZW4gJiYgc2hvd1dlbGNvbWVNZXNzYWdlICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBib3R0b20tMjQgcmlnaHQtNiB6LTMwIG1heC13LXhzXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBzaGFkb3cteGwgcC00IHRyYW5zZm9ybSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgaG92ZXI6c2NhbGUtMTA1XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbVwiPvCfpJY8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgIEhpISBJJ20gTUdCIEJvdFxuICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICBOZWVkIGhlbHAgd2l0aCBkb2N1bWVudHM/IENsaWNrIHRvIGNoYXQgd2l0aCBtZSEg8J+SrFxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbG9zZVdlbGNvbWV9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIGRhcms6aG92ZXI6dGV4dC1ncmF5LTMwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgcC0xIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy1ncmF5LTEwMCBkYXJrOmhvdmVyOmJnLWdyYXktNzAwXCJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIkNsb3NlIHdlbGNvbWUgbWVzc2FnZVwiXG4gICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkNsb3NlIHdlbGNvbWUgbWVzc2FnZVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2VXaWR0aD17Mn0+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgcmlnaHQtOCB0cmFuc2Zvcm0gdHJhbnNsYXRlLXktMS8yIHJvdGF0ZS00NSB3LTIgaC0yIGJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgYm9yZGVyLXIgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwXCI+PC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNoYXRJbnRlcmZhY2UiLCJDaGF0QnV0dG9uIiwiZG9jdW1lbnRDb250ZXh0IiwiaXNDaGF0T3BlbiIsInNldElzQ2hhdE9wZW4iLCJpc0FuaW1hdGluZyIsInNldElzQW5pbWF0aW5nIiwic2hvd1dlbGNvbWVNZXNzYWdlIiwic2V0U2hvd1dlbGNvbWVNZXNzYWdlIiwiaGFzU2VlbldlbGNvbWUiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiaGFuZGxlQ2xvc2VXZWxjb21lIiwic2V0SXRlbSIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJhcmlhLWxhYmVsIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJzcGFuIiwiaDQiLCJwIiwidGl0bGUiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chatbot/ChatButton.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx":
/*!**************************************************!*\
  !*** ./src/components/chatbot/ChatInterface.tsx ***!
  \**************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});