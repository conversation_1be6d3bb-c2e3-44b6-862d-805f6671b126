/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Capp%5C%5Cfonts.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccomponents%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccontexts%5C%5CDarkModeContext.tsx%22%2C%22ids%22%3A%5B%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Capp%5C%5Cfonts.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccomponents%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccontexts%5C%5CDarkModeContext.tsx%22%2C%22ids%22%3A%5B%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AuthProvider.tsx */ \"(ssr)/./src/components/AuthProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PerformanceMonitor.tsx */ \"(ssr)/./src/components/PerformanceMonitor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/DarkModeContext.tsx */ \"(ssr)/./src/contexts/DarkModeContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SettingsContext.tsx */ \"(ssr)/./src/contexts/SettingsContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Capp%5C%5Cfonts.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccomponents%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceMonitor.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccontexts%5C%5CDarkModeContext.tsx%22%2C%22ids%22%3A%5B%22DarkModeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CDocumentTracker%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthProvider.tsx":
/*!*****************************************!*\
  !*** ./src/components/AuthProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthProvider({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\AuthProvider.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdXRoUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrRDtBQVFuQyxTQUFTQyxhQUFhLEVBQUVDLFFBQVEsRUFBRUMsT0FBTyxFQUFTO0lBQy9ELHFCQUNFLDhEQUFDSCw0REFBZUE7UUFBQ0csU0FBU0E7a0JBQ3ZCRDs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kb2N1bWVudC10cmFja2VyLy4vc3JjL2NvbXBvbmVudHMvQXV0aFByb3ZpZGVyLnRzeD8xMmZjIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IFNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgnO1xuXG50eXBlIFByb3BzID0ge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBzZXNzaW9uPzogU2Vzc2lvbiB8IG51bGw7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiwgc2Vzc2lvbiB9OiBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxTZXNzaW9uUHJvdmlkZXIgc2Vzc2lvbj17c2Vzc2lvbn0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJzZXNzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PerformanceMonitor.tsx":
/*!***********************************************!*\
  !*** ./src/components/PerformanceMonitor.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PerformanceMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * Performance monitoring component that tracks and reports web vitals\n * This component doesn't render anything visible but collects performance metrics\n */ function PerformanceMonitor() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Only run in production or when explicitly enabled\n        if ( true && !process.env.NEXT_PUBLIC_ENABLE_METRICS) {\n            return;\n        }\n        // Check if the browser supports the necessary APIs\n        if (false) {}\n    }, []);\n    // Function to send metrics to analytics\n    const sendToAnalytics = (metricName, metric)=>{\n        // Here you would typically send this data to your analytics service\n        // For now, we'll just log it to the console in a structured format\n        const body = {\n            name: metricName,\n            value: metric.value,\n            id: metric.id,\n            page: window.location.pathname,\n            timestamp: Date.now()\n        };\n        // In a real implementation, you would send this data to your analytics endpoint\n        // fetch('/api/metrics', {\n        //   method: 'POST',\n        //   body: JSON.stringify(body),\n        //   headers: { 'Content-Type': 'application/json' },\n        // });\n        // For development, store in localStorage to track improvements\n        try {\n            const metricsKey = `performance_metrics_${metricName}`;\n            const existingMetrics = JSON.parse(localStorage.getItem(metricsKey) || \"[]\");\n            existingMetrics.push(body);\n            // Keep only the last 10 measurements\n            if (existingMetrics.length > 10) {\n                existingMetrics.shift();\n            }\n            localStorage.setItem(metricsKey, JSON.stringify(existingMetrics));\n        } catch (e) {\n            console.error(\"Error storing metrics in localStorage:\", e);\n        }\n    };\n    // This component doesn't render anything visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PerformanceMonitor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/DarkModeContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/DarkModeContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DarkModeProvider: () => (/* binding */ DarkModeProvider),\n/* harmony export */   useDarkMode: () => (/* binding */ useDarkMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useDarkMode,DarkModeProvider auto */ \n\nconst DarkModeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useDarkMode() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DarkModeContext);\n    if (context === undefined) {\n        throw new Error(\"useDarkMode must be used within a DarkModeProvider\");\n    }\n    return context;\n}\nfunction DarkModeProvider({ children }) {\n    // Always initialize to false (light mode) by default\n    const [darkMode, setDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize dark mode based on user preference only - default to light mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run this on the client side\n        setMounted(true);\n        // Check if user has a preference stored\n        const storedPreference = localStorage.getItem(\"darkMode\");\n        if (storedPreference !== null) {\n            // Only set to dark mode if explicitly stored as 'true'\n            setDarkMode(storedPreference === \"true\");\n        } else {\n            // Default to light mode regardless of system preference\n            setDarkMode(false);\n            // Store the default preference\n            localStorage.setItem(\"darkMode\", \"false\");\n        }\n    }, []);\n    // Update the document class when dark mode changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        if (darkMode) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n        // Store user preference\n        localStorage.setItem(\"darkMode\", darkMode.toString());\n    }, [\n        darkMode,\n        mounted\n    ]);\n    const toggleDarkMode = ()=>{\n        setDarkMode(!darkMode);\n    };\n    // Provide a value object that won't change unless darkMode changes\n    const contextValue = {\n        darkMode,\n        toggleDarkMode,\n        setDarkMode\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DarkModeContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\contexts\\\\DarkModeContext.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/DarkModeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useSettings,SettingsProvider auto */ \n\n\nconst defaultSettings = {\n    colorScheme: \"default\",\n    darkMode: false,\n    fontSize: \"medium\"\n};\nconst SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    settings: defaultSettings,\n    updateSettings: async ()=>{},\n    isLoading: false,\n    error: null\n});\nconst useSettings = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\nconst SettingsProvider = ({ children })=>{\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch settings when session is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSettings = async ()=>{\n            try {\n                setIsLoading(true);\n                setError(null);\n                // Wait for session to be determined\n                if (status === \"loading\") {\n                    return; // Still loading session, wait\n                }\n                // If no session, use default settings\n                if (status === \"unauthenticated\" || !session) {\n                    console.log(\"No session found, using default settings\");\n                    setSettings(defaultSettings);\n                    setIsLoading(false);\n                    return;\n                }\n                console.log(\"Session found, fetching user settings for:\", session.user?.name);\n                const response = await fetch(\"/api/settings\");\n                if (!response.ok) {\n                    // If unauthorized (401), just use default settings instead of throwing error\n                    if (response.status === 401) {\n                        console.warn(\"User not authenticated, using default settings\");\n                        setSettings(defaultSettings);\n                        return;\n                    }\n                    throw new Error(\"Failed to fetch settings\");\n                }\n                const data = await response.json();\n                if (data.success && data.settings) {\n                    setSettings({\n                        colorScheme: data.settings.colorScheme,\n                        darkMode: data.settings.darkMode,\n                        fontSize: data.settings.fontSize\n                    });\n                } else {\n                    // If no settings found, use defaults\n                    setSettings(defaultSettings);\n                }\n            } catch (err) {\n                console.error(\"Error fetching settings:\", err);\n                // Use default settings instead of showing error\n                setSettings(defaultSettings);\n                setError(null); // Don't show error to user, just use defaults\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchSettings();\n    }, [\n        session,\n        status\n    ]); // Depend on session and status\n    // Update settings\n    const updateSettings = async (newSettings)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Update local state immediately for better UX\n            const updatedSettings = {\n                ...settings,\n                ...newSettings\n            };\n            setSettings(updatedSettings);\n            // If no session, just keep local settings\n            if (status === \"unauthenticated\" || !session) {\n                console.log(\"No session, settings saved locally only\");\n                setIsLoading(false);\n                return;\n            }\n            // Save to server\n            const response = await fetch(\"/api/settings\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(updatedSettings)\n            });\n            if (!response.ok) {\n                // If unauthorized, just keep the local settings without throwing error\n                if (response.status === 401) {\n                    console.warn(\"User not authenticated, settings saved locally only\");\n                    return;\n                }\n                throw new Error(\"Failed to update settings\");\n            }\n            // Apply settings in the correct order to ensure proper theme application\n            // First, remove all theme classes regardless of what's changing\n            const themeClasses = [\n                \"theme-default\",\n                \"theme-lavender\",\n                \"theme-mint\",\n                \"theme-peach\",\n                \"theme-skyBlue\",\n                \"theme-coral\",\n                \"theme-lemon\",\n                \"theme-periwinkle\",\n                \"theme-rose\"\n            ];\n            document.documentElement.classList.remove(...themeClasses);\n            // Special case: if selecting default color scheme, ensure dark mode is off\n            if (newSettings.colorScheme === \"default\") {\n                document.documentElement.classList.remove(\"dark\");\n                // Update the darkMode setting to false if it's not already\n                if (newSettings.darkMode === true) {\n                    newSettings.darkMode = false;\n                    // Also update localStorage to keep it in sync\n                    localStorage.setItem(\"darkMode\", \"false\");\n                }\n            } else if (newSettings.darkMode !== undefined) {\n                if (newSettings.darkMode) {\n                    document.documentElement.classList.add(\"dark\");\n                } else {\n                    document.documentElement.classList.remove(\"dark\");\n                }\n            }\n            // Finally apply color scheme (after dark mode)\n            // This ensures the color scheme's variables are applied properly\n            if (newSettings.colorScheme && newSettings.colorScheme !== \"default\") {\n                document.documentElement.classList.add(`theme-${newSettings.colorScheme}`);\n            }\n            // Apply font size\n            if (newSettings.fontSize) {\n                document.documentElement.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n                switch(newSettings.fontSize){\n                    case \"small\":\n                        document.documentElement.classList.add(\"text-sm\");\n                        break;\n                    case \"medium\":\n                        document.documentElement.classList.add(\"text-base\");\n                        break;\n                    case \"large\":\n                        document.documentElement.classList.add(\"text-lg\");\n                        break;\n                }\n            }\n            // Log changes\n            if (newSettings.colorScheme || newSettings.darkMode !== undefined) {\n                console.log(`Applied settings - Color scheme: ${newSettings.colorScheme || settings.colorScheme}, Dark mode: ${newSettings.darkMode !== undefined ? newSettings.darkMode : settings.darkMode}`);\n            }\n        } catch (err) {\n            console.error(\"Error updating settings:\", err);\n            setError(err.message || \"An error occurred while updating settings\");\n            // Revert to previous settings on error\n            setSettings(settings);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Apply settings on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Apply settings in the correct order to ensure proper theme application\n        // First, remove all theme classes\n        const themeClasses = [\n            \"theme-default\",\n            \"theme-lavender\",\n            \"theme-mint\",\n            \"theme-peach\",\n            \"theme-skyBlue\",\n            \"theme-coral\",\n            \"theme-lemon\",\n            \"theme-periwinkle\",\n            \"theme-rose\"\n        ];\n        document.documentElement.classList.remove(...themeClasses);\n        // Special case: if using default color scheme, ensure dark mode is off\n        if (settings.colorScheme === \"default\") {\n            document.documentElement.classList.remove(\"dark\");\n            // If settings has dark mode on but color scheme is default, update settings\n            if (settings.darkMode) {\n                setSettings((prev)=>({\n                        ...prev,\n                        darkMode: false\n                    }));\n                // Also update localStorage to keep it in sync\n                localStorage.setItem(\"darkMode\", \"false\");\n            }\n        } else if (settings.darkMode) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n        // Finally apply color scheme (after dark mode)\n        // This ensures the color scheme's variables are applied properly\n        if (settings.colorScheme !== \"default\") {\n            document.documentElement.classList.add(`theme-${settings.colorScheme}`);\n        }\n        // Apply font size\n        document.documentElement.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n        switch(settings.fontSize){\n            case \"small\":\n                document.documentElement.classList.add(\"text-sm\");\n                break;\n            case \"medium\":\n                document.documentElement.classList.add(\"text-base\");\n                break;\n            case \"large\":\n                document.documentElement.classList.add(\"text-lg\");\n                break;\n        }\n        console.log(`Applied color scheme: ${settings.colorScheme}, Dark mode: ${settings.darkMode}`);\n    }, [\n        settings\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: {\n            settings,\n            updateSettings,\n            isLoading,\n            error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\contexts\\\\SettingsContext.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/SettingsContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/fonts.css":
/*!***************************!*\
  !*** ./src/app/fonts.css ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2374c5c6fde5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2ZvbnRzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2RvY3VtZW50LXRyYWNrZXIvLi9zcmMvYXBwL2ZvbnRzLmNzcz9hNDIxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMjM3NGM1YzZmZGU1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/fonts.css\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"28028d63ffd8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZG9jdW1lbnQtdHJhY2tlci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MjI5NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI4MDI4ZDYzZmZkOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _fonts_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./fonts.css */ \"(rsc)/./src/app/fonts.css\");\n/* harmony import */ var _polyfills__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/polyfills */ \"(rsc)/./src/polyfills.js\");\n/* harmony import */ var _polyfills__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_polyfills__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/auth/options */ \"(rsc)/./src/lib/auth/options.ts\");\n/* harmony import */ var _components_AuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AuthProvider */ \"(rsc)/./src/components/AuthProvider.tsx\");\n/* harmony import */ var _contexts_DarkModeContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/DarkModeContext */ \"(rsc)/./src/contexts/DarkModeContext.tsx\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(rsc)/./src/contexts/SettingsContext.tsx\");\n/* harmony import */ var _lib_serverInit__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/serverInit */ \"(rsc)/./src/lib/serverInit.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dynamic */ \"(rsc)/./node_modules/next/dist/api/app-dynamic.js\");\n\n\n\n// Import custom polyfills\n\n\n\n\n\n\n\n\n// Dynamically import the performance monitor to avoid affecting initial load\nconst PerformanceMonitor = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_rsc_src_components_PerformanceMonitor_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/PerformanceMonitor */ \"(rsc)/./src/components/PerformanceMonitor.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\layout.tsx -> \" + \"@/components/PerformanceMonitor\"\n        ]\n    },\n    ssr: false\n});\nconst metadata = {\n    title: \"MGB Document Management System\",\n    description: \"Document Management System for Mines and Geosciences Bureau\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3002\")\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 5,\n    themeColor: [\n        {\n            media: \"(prefers-color-scheme: light)\",\n            color: \"#ffffff\"\n        },\n        {\n            media: \"(prefers-color-scheme: dark)\",\n            color: \"#111827\"\n        }\n    ]\n};\nasync function RootLayout({ children }) {\n    // Initialize server components\n    await (0,_lib_serverInit__WEBPACK_IMPORTED_MODULE_9__.initializeServer)();\n    // Get the user session\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_4__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_5__.authOptions);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"/fonts/inter-var.woff2\",\n                        as: \"font\",\n                        type: \"font/woff2\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"/api\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthProvider__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    session: session,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_DarkModeContext__WEBPACK_IMPORTED_MODULE_7__.DarkModeProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_8__.SettingsProvider, {\n                            children: [\n                                children,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PerformanceMonitor, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AuthProvider.tsx":
/*!*****************************************!*\
  !*** ./src/components/AuthProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\DocumentTracker\src\components\AuthProvider.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/contexts/DarkModeContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/DarkModeContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DarkModeProvider: () => (/* binding */ e1),
/* harmony export */   useDarkMode: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\DocumentTracker\src\contexts\DarkModeContext.tsx#useDarkMode`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\DocumentTracker\src\contexts\DarkModeContext.tsx#DarkModeProvider`);


/***/ }),

/***/ "(rsc)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SettingsProvider: () => (/* binding */ e1),
/* harmony export */   useSettings: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\DocumentTracker\src\contexts\SettingsContext.tsx#useSettings`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\DocumentTracker\src\contexts\SettingsContext.tsx#SettingsProvider`);


/***/ }),

/***/ "(rsc)/./src/lib/auth/options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth/options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _utils_audit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/audit */ \"(rsc)/./src/utils/audit.ts\");\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n/* harmony import */ var _utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/serverTimestamp */ \"(rsc)/./src/utils/serverTimestamp.ts\");\n/* harmony import */ var _utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/sessionToken */ \"(rsc)/./src/utils/sessionToken.ts\");\n\n\n\n\n\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"credentials\",\n            name: \"Credentials\",\n            // Define the credentials that will be submitted from the login form\n            credentials: {\n                name: {\n                    label: \"Name\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials, req) {\n                // Add more detailed logging of received credentials\n                console.log(\"Received credentials:\", credentials);\n                if (!credentials?.name || !credentials?.password) {\n                    console.error(\"Missing credentials:\", {\n                        name: !!credentials?.name,\n                        password: !!credentials?.password\n                    });\n                    throw new Error(\"Name and password required\");\n                }\n                try {\n                    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                    console.log(\"Looking up user with name:\", credentials.name);\n                    // Make the search case-insensitive and escape special characters in the regex\n                    const escapedName = credentials.name.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n                        name: {\n                            $regex: new RegExp(`^${escapedName}$`, \"i\")\n                        }\n                    }).select(\"+password\");\n                    if (!user) {\n                        console.log(\"User not found with name:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"User found, checking password\");\n                    if (!user.password) {\n                        console.log(\"User has no password set\");\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    const isPasswordCorrect = await (0,bcrypt__WEBPACK_IMPORTED_MODULE_1__.compare)(credentials.password, user.password);\n                    if (!isPasswordCorrect) {\n                        console.log(\"Password incorrect for user:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"Authentication successful for user:\", credentials.name);\n                    const userId = user._id?.toString() || \"\";\n                    // Create a new session token\n                    const userAgent = req?.headers?.[\"user-agent\"];\n                    const ipAddress = req?.headers?.[\"x-forwarded-for\"] || req?.socket?.remoteAddress || \"unknown\";\n                    const sessionToken = await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__.createSession)(userId, userAgent, ipAddress);\n                    // Log successful login\n                    await (0,_utils_audit__WEBPACK_IMPORTED_MODULE_4__.logAuditEvent)({\n                        action: _types_audit__WEBPACK_IMPORTED_MODULE_5__.AuditLogAction.USER_LOGIN,\n                        performedBy: userId,\n                        targetId: userId,\n                        targetType: \"User\",\n                        details: {\n                            name: user.name,\n                            email: user.email,\n                            role: user.role,\n                            division: user.division,\n                            sessionToken: sessionToken\n                        }\n                    });\n                    return {\n                        id: userId,\n                        name: user.name,\n                        email: user.email,\n                        role: user.role,\n                        division: user.division,\n                        image: user.image,\n                        sessionToken: sessionToken\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw new Error(\"Authentication failed. Please try again.\");\n                }\n                // This code is unreachable due to the try/catch block above\n                // but we'll keep it as a fallback\n                console.error(\"Warning: Reached unreachable code in NextAuth authorize callback\");\n                return null;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async jwt ({ token, user, trigger }) {\n            console.log(\"JWT callback called with user:\", user);\n            console.log(\"Initial token:\", token);\n            if (user) {\n                // Add user data to token\n                token.id = user.id;\n                // Cast user to any to access custom properties\n                const customUser = user;\n                token.name = customUser.name; // Ensure name is included\n                token.role = customUser.role;\n                token.division = customUser.division;\n                // Add session token to JWT\n                if (customUser.sessionToken) {\n                    token.sessionToken = customUser.sessionToken;\n                }\n                // Add server timestamp to token to invalidate sessions on server restart\n                token.serverTimestamp = (0,_utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_6__.getServerTimestamp)();\n                console.log(\"Updated token with user data:\", token);\n            }\n            // Handle sign out\n            if (trigger === \"signOut\") {\n                // Remove the session from the database when the user signs out\n                if (token.id && token.sessionToken) {\n                    await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__.removeSession)(token.id, token.sessionToken);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"Session callback called with token:\", token);\n            console.log(\"Initial session:\", session);\n            if (token) {\n                // Add user data to session\n                session.user.id = token.id;\n                session.user.name = token.name; // Ensure name is included\n                session.user.role = token.role;\n                session.user.division = token.division;\n                // Add server timestamp to session\n                session.serverTimestamp = token.serverTimestamp;\n                // Add session token to session\n                if (token.sessionToken) {\n                    session.sessionToken = token.sessionToken;\n                }\n            }\n            console.log(\"Returning session:\", session);\n            return session;\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgvb3B0aW9ucy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQ2tFO0FBQ2pDO0FBQ1E7QUFDUjtBQUNhO0FBQ0M7QUFDYztBQUVPO0FBRTdELE1BQU1TLGNBQStCO0lBQzFDQyxXQUFXO1FBQ1RWLDJFQUFtQkEsQ0FBQztZQUNsQlcsSUFBSTtZQUNKQyxNQUFNO1lBQ04sb0VBQW9FO1lBQ3BFQyxhQUFhO2dCQUNYRCxNQUFNO29CQUFFRSxPQUFPO29CQUFRQyxNQUFNO2dCQUFPO2dCQUNwQ0MsVUFBVTtvQkFBRUYsT0FBTztvQkFBWUMsTUFBTTtnQkFBVztZQUNsRDtZQUNBLE1BQU1FLFdBQVVKLFdBQVcsRUFBRUssR0FBRztnQkFDOUIsb0RBQW9EO2dCQUNwREMsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QlA7Z0JBRXJDLElBQUksQ0FBQ0EsYUFBYUQsUUFBUSxDQUFDQyxhQUFhRyxVQUFVO29CQUNoREcsUUFBUUUsS0FBSyxDQUFDLHdCQUF3Qjt3QkFBRVQsTUFBTSxDQUFDLENBQUNDLGFBQWFEO3dCQUFNSSxVQUFVLENBQUMsQ0FBQ0gsYUFBYUc7b0JBQVM7b0JBQ3JHLE1BQU0sSUFBSU0sTUFBTTtnQkFDbEI7Z0JBRUEsSUFBSTtvQkFDRixNQUFNcEIsMkRBQVNBO29CQUVmaUIsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QlAsWUFBWUQsSUFBSTtvQkFDMUQsOEVBQThFO29CQUM5RSxNQUFNVyxjQUFjVixZQUFZRCxJQUFJLENBQUNZLE9BQU8sQ0FBQyx1QkFBdUI7b0JBQ3BFLE1BQU1DLE9BQU8sTUFBTXRCLG9EQUFJQSxDQUFDdUIsT0FBTyxDQUFDO3dCQUM5QmQsTUFBTTs0QkFBRWUsUUFBUSxJQUFJQyxPQUFPLENBQUMsQ0FBQyxFQUFFTCxZQUFZLENBQUMsQ0FBQyxFQUFFO3dCQUFLO29CQUN0RCxHQUFHTSxNQUFNLENBQUM7b0JBRVYsSUFBSSxDQUFDSixNQUFNO3dCQUNUTixRQUFRQyxHQUFHLENBQUMsNkJBQTZCUCxZQUFZRCxJQUFJO3dCQUN6RCxNQUFNLElBQUlVLE1BQU07b0JBQ2xCO29CQUVBSCxRQUFRQyxHQUFHLENBQUM7b0JBQ1osSUFBSSxDQUFDSyxLQUFLVCxRQUFRLEVBQUU7d0JBQ2xCRyxRQUFRQyxHQUFHLENBQUM7d0JBQ1osTUFBTSxJQUFJRSxNQUFNO29CQUNsQjtvQkFDQSxNQUFNUSxvQkFBb0IsTUFBTTdCLCtDQUFPQSxDQUFDWSxZQUFZRyxRQUFRLEVBQUVTLEtBQUtULFFBQVE7b0JBRTNFLElBQUksQ0FBQ2MsbUJBQW1CO3dCQUN0QlgsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ1AsWUFBWUQsSUFBSTt3QkFDNUQsTUFBTSxJQUFJVSxNQUFNO29CQUNsQjtvQkFFQUgsUUFBUUMsR0FBRyxDQUFDLHVDQUF1Q1AsWUFBWUQsSUFBSTtvQkFFbkUsTUFBTW1CLFNBQVNOLEtBQUtPLEdBQUcsRUFBRUMsY0FBYztvQkFFdkMsNkJBQTZCO29CQUM3QixNQUFNQyxZQUFZaEIsS0FBS2lCLFNBQVMsQ0FBQyxhQUFhO29CQUM5QyxNQUFNQyxZQUFZbEIsS0FBS2lCLFNBQVMsQ0FBQyxrQkFBa0IsSUFDbENqQixLQUFLbUIsUUFBUUMsaUJBQ2I7b0JBRWpCLE1BQU1DLGVBQWUsTUFBTWhDLGtFQUFhQSxDQUN0Q3dCLFFBQ0FHLFdBQ0FFO29CQUdGLHVCQUF1QjtvQkFDdkIsTUFBTWhDLDJEQUFhQSxDQUFDO3dCQUNsQm9DLFFBQVFuQyx3REFBY0EsQ0FBQ29DLFVBQVU7d0JBQ2pDQyxhQUFhWDt3QkFDYlksVUFBVVo7d0JBQ1ZhLFlBQVk7d0JBQ1pDLFNBQVM7NEJBQ1BqQyxNQUFNYSxLQUFLYixJQUFJOzRCQUNma0MsT0FBT3JCLEtBQUtxQixLQUFLOzRCQUNqQkMsTUFBTXRCLEtBQUtzQixJQUFJOzRCQUNmQyxVQUFVdkIsS0FBS3VCLFFBQVE7NEJBQ3ZCVCxjQUFjQTt3QkFDaEI7b0JBQ0Y7b0JBRUEsT0FBTzt3QkFDTDVCLElBQUlvQjt3QkFDSm5CLE1BQU1hLEtBQUtiLElBQUk7d0JBQ2ZrQyxPQUFPckIsS0FBS3FCLEtBQUs7d0JBQ2pCQyxNQUFNdEIsS0FBS3NCLElBQUk7d0JBQ2ZDLFVBQVV2QixLQUFLdUIsUUFBUTt3QkFDdkJDLE9BQU94QixLQUFLd0IsS0FBSzt3QkFDakJWLGNBQWNBO29CQUNoQjtnQkFDRixFQUFFLE9BQU9sQixPQUFPO29CQUNkRixRQUFRRSxLQUFLLENBQUMseUJBQXlCQTtvQkFDdkMsTUFBTSxJQUFJQyxNQUFNO2dCQUNsQjtnQkFFQSw0REFBNEQ7Z0JBQzVELGtDQUFrQztnQkFDbENILFFBQVFFLEtBQUssQ0FBQztnQkFDZCxPQUFPO1lBQ1Q7UUFDRjtLQUNEO0lBQ0Q2QixPQUFPO1FBQ0xDLFFBQVE7UUFDUjlCLE9BQU87SUFDVDtJQUNBK0IsV0FBVztRQUNULE1BQU1DLEtBQUksRUFBRUMsS0FBSyxFQUFFN0IsSUFBSSxFQUFFOEIsT0FBTyxFQUFFO1lBQ2hDcEMsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ0s7WUFDOUNOLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JrQztZQUU5QixJQUFJN0IsTUFBTTtnQkFDUix5QkFBeUI7Z0JBQ3pCNkIsTUFBTTNDLEVBQUUsR0FBR2MsS0FBS2QsRUFBRTtnQkFDbEIsK0NBQStDO2dCQUMvQyxNQUFNNkMsYUFBYS9CO2dCQUNuQjZCLE1BQU0xQyxJQUFJLEdBQUc0QyxXQUFXNUMsSUFBSSxFQUFFLDBCQUEwQjtnQkFDeEQwQyxNQUFNUCxJQUFJLEdBQUdTLFdBQVdULElBQUk7Z0JBQzVCTyxNQUFNTixRQUFRLEdBQUdRLFdBQVdSLFFBQVE7Z0JBRXBDLDJCQUEyQjtnQkFDM0IsSUFBSVEsV0FBV2pCLFlBQVksRUFBRTtvQkFDM0JlLE1BQU1mLFlBQVksR0FBR2lCLFdBQVdqQixZQUFZO2dCQUM5QztnQkFFQSx5RUFBeUU7Z0JBQ3pFZSxNQUFNRyxlQUFlLEdBQUduRCwwRUFBa0JBO2dCQUUxQ2EsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ2tDO1lBQy9DO1lBRUEsa0JBQWtCO1lBQ2xCLElBQUlDLFlBQVksV0FBVztnQkFDekIsK0RBQStEO2dCQUMvRCxJQUFJRCxNQUFNM0MsRUFBRSxJQUFJMkMsTUFBTWYsWUFBWSxFQUFFO29CQUNsQyxNQUFNL0Isa0VBQWFBLENBQUM4QyxNQUFNM0MsRUFBRSxFQUFFMkMsTUFBTWYsWUFBWTtnQkFDbEQ7WUFDRjtZQUVBLE9BQU9lO1FBQ1Q7UUFDQSxNQUFNSSxTQUFRLEVBQUVBLE9BQU8sRUFBRUosS0FBSyxFQUFFO1lBQzlCbkMsUUFBUUMsR0FBRyxDQUFDLHVDQUF1Q2tDO1lBQ25EbkMsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQnNDO1lBRWhDLElBQUlKLE9BQU87Z0JBQ1QsMkJBQTJCO2dCQUMzQkksUUFBUWpDLElBQUksQ0FBQ2QsRUFBRSxHQUFHMkMsTUFBTTNDLEVBQUU7Z0JBQzFCK0MsUUFBUWpDLElBQUksQ0FBQ2IsSUFBSSxHQUFHMEMsTUFBTTFDLElBQUksRUFBRSwwQkFBMEI7Z0JBQzFEOEMsUUFBUWpDLElBQUksQ0FBQ3NCLElBQUksR0FBR08sTUFBTVAsSUFBSTtnQkFDOUJXLFFBQVFqQyxJQUFJLENBQUN1QixRQUFRLEdBQUdNLE1BQU1OLFFBQVE7Z0JBRXRDLGtDQUFrQztnQkFDbENVLFFBQVFELGVBQWUsR0FBR0gsTUFBTUcsZUFBZTtnQkFFL0MsK0JBQStCO2dCQUMvQixJQUFJSCxNQUFNZixZQUFZLEVBQUU7b0JBQ3RCbUIsUUFBUW5CLFlBQVksR0FBR2UsTUFBTWYsWUFBWTtnQkFDM0M7WUFDRjtZQUVBcEIsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQnNDO1lBQ2xDLE9BQU9BO1FBQ1Q7SUFDRjtJQUNBQSxTQUFTO1FBQ1BDLFVBQVU7UUFDVkMsUUFBUSxLQUFLLEtBQUs7SUFDcEI7SUFDQUMsUUFBUUMsUUFBUUMsR0FBRyxDQUFDQyxlQUFlO0lBQ25DQyxPQUFPSCxrQkFBeUI7QUFDbEMsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2RvY3VtZW50LXRyYWNrZXIvLi9zcmMvbGliL2F1dGgvb3B0aW9ucy50cz85ODgyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTmV4dEF1dGhPcHRpb25zIH0gZnJvbSAnbmV4dC1hdXRoJztcbmltcG9ydCBDcmVkZW50aWFsc1Byb3ZpZGVyIGZyb20gJ25leHQtYXV0aC9wcm92aWRlcnMvY3JlZGVudGlhbHMnO1xuaW1wb3J0IHsgY29tcGFyZSB9IGZyb20gJ2JjcnlwdCc7XG5pbXBvcnQgZGJDb25uZWN0IGZyb20gJ0AvbGliL2RiL21vbmdvZGInO1xuaW1wb3J0IFVzZXIgZnJvbSAnQC9tb2RlbHMvVXNlcic7XG5pbXBvcnQgeyBsb2dBdWRpdEV2ZW50IH0gZnJvbSAnQC91dGlscy9hdWRpdCc7XG5pbXBvcnQgeyBBdWRpdExvZ0FjdGlvbiB9IGZyb20gJ0AvdHlwZXMvYXVkaXQnO1xuaW1wb3J0IHsgZ2V0U2VydmVyVGltZXN0YW1wIH0gZnJvbSAnQC91dGlscy9zZXJ2ZXJUaW1lc3RhbXAnO1xuaW1wb3J0IHsgZ2V0QmFzZVVybCB9IGZyb20gJ0AvdXRpbHMvZ2V0QmFzZVVybCc7XG5pbXBvcnQgeyBjcmVhdGVTZXNzaW9uLCByZW1vdmVTZXNzaW9uIH0gZnJvbSAnQC91dGlscy9zZXNzaW9uVG9rZW4nO1xuXG5leHBvcnQgY29uc3QgYXV0aE9wdGlvbnM6IE5leHRBdXRoT3B0aW9ucyA9IHtcbiAgcHJvdmlkZXJzOiBbXG4gICAgQ3JlZGVudGlhbHNQcm92aWRlcih7XG4gICAgICBpZDogJ2NyZWRlbnRpYWxzJyxcbiAgICAgIG5hbWU6ICdDcmVkZW50aWFscycsXG4gICAgICAvLyBEZWZpbmUgdGhlIGNyZWRlbnRpYWxzIHRoYXQgd2lsbCBiZSBzdWJtaXR0ZWQgZnJvbSB0aGUgbG9naW4gZm9ybVxuICAgICAgY3JlZGVudGlhbHM6IHtcbiAgICAgICAgbmFtZTogeyBsYWJlbDogJ05hbWUnLCB0eXBlOiAndGV4dCcgfSxcbiAgICAgICAgcGFzc3dvcmQ6IHsgbGFiZWw6ICdQYXNzd29yZCcsIHR5cGU6ICdwYXNzd29yZCcgfSxcbiAgICAgIH0sXG4gICAgICBhc3luYyBhdXRob3JpemUoY3JlZGVudGlhbHMsIHJlcSkge1xuICAgICAgICAvLyBBZGQgbW9yZSBkZXRhaWxlZCBsb2dnaW5nIG9mIHJlY2VpdmVkIGNyZWRlbnRpYWxzXG4gICAgICAgIGNvbnNvbGUubG9nKCdSZWNlaXZlZCBjcmVkZW50aWFsczonLCBjcmVkZW50aWFscyk7XG5cbiAgICAgICAgaWYgKCFjcmVkZW50aWFscz8ubmFtZSB8fCAhY3JlZGVudGlhbHM/LnBhc3N3b3JkKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignTWlzc2luZyBjcmVkZW50aWFsczonLCB7IG5hbWU6ICEhY3JlZGVudGlhbHM/Lm5hbWUsIHBhc3N3b3JkOiAhIWNyZWRlbnRpYWxzPy5wYXNzd29yZCB9KTtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ05hbWUgYW5kIHBhc3N3b3JkIHJlcXVpcmVkJyk7XG4gICAgICAgIH1cblxuICAgICAgICB0cnkge1xuICAgICAgICAgIGF3YWl0IGRiQ29ubmVjdCgpO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coJ0xvb2tpbmcgdXAgdXNlciB3aXRoIG5hbWU6JywgY3JlZGVudGlhbHMubmFtZSk7XG4gICAgICAgICAgLy8gTWFrZSB0aGUgc2VhcmNoIGNhc2UtaW5zZW5zaXRpdmUgYW5kIGVzY2FwZSBzcGVjaWFsIGNoYXJhY3RlcnMgaW4gdGhlIHJlZ2V4XG4gICAgICAgICAgY29uc3QgZXNjYXBlZE5hbWUgPSBjcmVkZW50aWFscy5uYW1lLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXF1cXFxcXS9nLCAnXFxcXCQmJyk7XG4gICAgICAgICAgY29uc3QgdXNlciA9IGF3YWl0IFVzZXIuZmluZE9uZSh7XG4gICAgICAgICAgICBuYW1lOiB7ICRyZWdleDogbmV3IFJlZ0V4cChgXiR7ZXNjYXBlZE5hbWV9JGAsICdpJykgfVxuICAgICAgICAgIH0pLnNlbGVjdCgnK3Bhc3N3b3JkJyk7XG5cbiAgICAgICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIG5vdCBmb3VuZCB3aXRoIG5hbWU6JywgY3JlZGVudGlhbHMubmFtZSk7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgbmFtZSBvciBwYXNzd29yZCcpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIGZvdW5kLCBjaGVja2luZyBwYXNzd29yZCcpO1xuICAgICAgICAgIGlmICghdXNlci5wYXNzd29yZCkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgaGFzIG5vIHBhc3N3b3JkIHNldCcpO1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIG5hbWUgb3IgcGFzc3dvcmQnKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgY29uc3QgaXNQYXNzd29yZENvcnJlY3QgPSBhd2FpdCBjb21wYXJlKGNyZWRlbnRpYWxzLnBhc3N3b3JkLCB1c2VyLnBhc3N3b3JkKTtcblxuICAgICAgICAgIGlmICghaXNQYXNzd29yZENvcnJlY3QpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdQYXNzd29yZCBpbmNvcnJlY3QgZm9yIHVzZXI6JywgY3JlZGVudGlhbHMubmFtZSk7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgbmFtZSBvciBwYXNzd29yZCcpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoZW50aWNhdGlvbiBzdWNjZXNzZnVsIGZvciB1c2VyOicsIGNyZWRlbnRpYWxzLm5hbWUpO1xuXG4gICAgICAgICAgY29uc3QgdXNlcklkID0gdXNlci5faWQ/LnRvU3RyaW5nKCkgfHwgJyc7XG5cbiAgICAgICAgICAvLyBDcmVhdGUgYSBuZXcgc2Vzc2lvbiB0b2tlblxuICAgICAgICAgIGNvbnN0IHVzZXJBZ2VudCA9IHJlcT8uaGVhZGVycz8uWyd1c2VyLWFnZW50J107XG4gICAgICAgICAgY29uc3QgaXBBZGRyZXNzID0gcmVxPy5oZWFkZXJzPy5bJ3gtZm9yd2FyZGVkLWZvciddIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICByZXE/LnNvY2tldD8ucmVtb3RlQWRkcmVzcyB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3Vua25vd24nO1xuXG4gICAgICAgICAgY29uc3Qgc2Vzc2lvblRva2VuID0gYXdhaXQgY3JlYXRlU2Vzc2lvbihcbiAgICAgICAgICAgIHVzZXJJZCxcbiAgICAgICAgICAgIHVzZXJBZ2VudCBhcyBzdHJpbmcsXG4gICAgICAgICAgICBpcEFkZHJlc3MgYXMgc3RyaW5nXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIC8vIExvZyBzdWNjZXNzZnVsIGxvZ2luXG4gICAgICAgICAgYXdhaXQgbG9nQXVkaXRFdmVudCh7XG4gICAgICAgICAgICBhY3Rpb246IEF1ZGl0TG9nQWN0aW9uLlVTRVJfTE9HSU4sXG4gICAgICAgICAgICBwZXJmb3JtZWRCeTogdXNlcklkLFxuICAgICAgICAgICAgdGFyZ2V0SWQ6IHVzZXJJZCxcbiAgICAgICAgICAgIHRhcmdldFR5cGU6ICdVc2VyJyxcbiAgICAgICAgICAgIGRldGFpbHM6IHtcbiAgICAgICAgICAgICAgbmFtZTogdXNlci5uYW1lLFxuICAgICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgICAgICAgcm9sZTogdXNlci5yb2xlLFxuICAgICAgICAgICAgICBkaXZpc2lvbjogdXNlci5kaXZpc2lvbixcbiAgICAgICAgICAgICAgc2Vzc2lvblRva2VuOiBzZXNzaW9uVG9rZW4sXG4gICAgICAgICAgICB9LFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGlkOiB1c2VySWQsXG4gICAgICAgICAgICBuYW1lOiB1c2VyLm5hbWUsXG4gICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgICAgIHJvbGU6IHVzZXIucm9sZSxcbiAgICAgICAgICAgIGRpdmlzaW9uOiB1c2VyLmRpdmlzaW9uLFxuICAgICAgICAgICAgaW1hZ2U6IHVzZXIuaW1hZ2UsXG4gICAgICAgICAgICBzZXNzaW9uVG9rZW46IHNlc3Npb25Ub2tlbixcbiAgICAgICAgICB9O1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhlbnRpY2F0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIGZhaWxlZC4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFRoaXMgY29kZSBpcyB1bnJlYWNoYWJsZSBkdWUgdG8gdGhlIHRyeS9jYXRjaCBibG9jayBhYm92ZVxuICAgICAgICAvLyBidXQgd2UnbGwga2VlcCBpdCBhcyBhIGZhbGxiYWNrXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1dhcm5pbmc6IFJlYWNoZWQgdW5yZWFjaGFibGUgY29kZSBpbiBOZXh0QXV0aCBhdXRob3JpemUgY2FsbGJhY2snKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9LFxuICAgIH0pLFxuICBdLFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogJy9hdXRoL3NpZ25pbicsXG4gICAgZXJyb3I6ICcvYXV0aC9lcnJvcicsXG4gIH0sXG4gIGNhbGxiYWNrczoge1xuICAgIGFzeW5jIGp3dCh7IHRva2VuLCB1c2VyLCB0cmlnZ2VyIH0pIHtcbiAgICAgIGNvbnNvbGUubG9nKCdKV1QgY2FsbGJhY2sgY2FsbGVkIHdpdGggdXNlcjonLCB1c2VyKTtcbiAgICAgIGNvbnNvbGUubG9nKCdJbml0aWFsIHRva2VuOicsIHRva2VuKTtcblxuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgLy8gQWRkIHVzZXIgZGF0YSB0byB0b2tlblxuICAgICAgICB0b2tlbi5pZCA9IHVzZXIuaWQ7XG4gICAgICAgIC8vIENhc3QgdXNlciB0byBhbnkgdG8gYWNjZXNzIGN1c3RvbSBwcm9wZXJ0aWVzXG4gICAgICAgIGNvbnN0IGN1c3RvbVVzZXIgPSB1c2VyIGFzIGFueTtcbiAgICAgICAgdG9rZW4ubmFtZSA9IGN1c3RvbVVzZXIubmFtZTsgLy8gRW5zdXJlIG5hbWUgaXMgaW5jbHVkZWRcbiAgICAgICAgdG9rZW4ucm9sZSA9IGN1c3RvbVVzZXIucm9sZTtcbiAgICAgICAgdG9rZW4uZGl2aXNpb24gPSBjdXN0b21Vc2VyLmRpdmlzaW9uO1xuXG4gICAgICAgIC8vIEFkZCBzZXNzaW9uIHRva2VuIHRvIEpXVFxuICAgICAgICBpZiAoY3VzdG9tVXNlci5zZXNzaW9uVG9rZW4pIHtcbiAgICAgICAgICB0b2tlbi5zZXNzaW9uVG9rZW4gPSBjdXN0b21Vc2VyLnNlc3Npb25Ub2tlbjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEFkZCBzZXJ2ZXIgdGltZXN0YW1wIHRvIHRva2VuIHRvIGludmFsaWRhdGUgc2Vzc2lvbnMgb24gc2VydmVyIHJlc3RhcnRcbiAgICAgICAgdG9rZW4uc2VydmVyVGltZXN0YW1wID0gZ2V0U2VydmVyVGltZXN0YW1wKCk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ1VwZGF0ZWQgdG9rZW4gd2l0aCB1c2VyIGRhdGE6JywgdG9rZW4pO1xuICAgICAgfVxuXG4gICAgICAvLyBIYW5kbGUgc2lnbiBvdXRcbiAgICAgIGlmICh0cmlnZ2VyID09PSAnc2lnbk91dCcpIHtcbiAgICAgICAgLy8gUmVtb3ZlIHRoZSBzZXNzaW9uIGZyb20gdGhlIGRhdGFiYXNlIHdoZW4gdGhlIHVzZXIgc2lnbnMgb3V0XG4gICAgICAgIGlmICh0b2tlbi5pZCAmJiB0b2tlbi5zZXNzaW9uVG9rZW4pIHtcbiAgICAgICAgICBhd2FpdCByZW1vdmVTZXNzaW9uKHRva2VuLmlkLCB0b2tlbi5zZXNzaW9uVG9rZW4pO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB0b2tlbjtcbiAgICB9LFxuICAgIGFzeW5jIHNlc3Npb24oeyBzZXNzaW9uLCB0b2tlbiB9KSB7XG4gICAgICBjb25zb2xlLmxvZygnU2Vzc2lvbiBjYWxsYmFjayBjYWxsZWQgd2l0aCB0b2tlbjonLCB0b2tlbik7XG4gICAgICBjb25zb2xlLmxvZygnSW5pdGlhbCBzZXNzaW9uOicsIHNlc3Npb24pO1xuXG4gICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgLy8gQWRkIHVzZXIgZGF0YSB0byBzZXNzaW9uXG4gICAgICAgIHNlc3Npb24udXNlci5pZCA9IHRva2VuLmlkO1xuICAgICAgICBzZXNzaW9uLnVzZXIubmFtZSA9IHRva2VuLm5hbWU7IC8vIEVuc3VyZSBuYW1lIGlzIGluY2x1ZGVkXG4gICAgICAgIHNlc3Npb24udXNlci5yb2xlID0gdG9rZW4ucm9sZTtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmRpdmlzaW9uID0gdG9rZW4uZGl2aXNpb247XG5cbiAgICAgICAgLy8gQWRkIHNlcnZlciB0aW1lc3RhbXAgdG8gc2Vzc2lvblxuICAgICAgICBzZXNzaW9uLnNlcnZlclRpbWVzdGFtcCA9IHRva2VuLnNlcnZlclRpbWVzdGFtcDtcblxuICAgICAgICAvLyBBZGQgc2Vzc2lvbiB0b2tlbiB0byBzZXNzaW9uXG4gICAgICAgIGlmICh0b2tlbi5zZXNzaW9uVG9rZW4pIHtcbiAgICAgICAgICBzZXNzaW9uLnNlc3Npb25Ub2tlbiA9IHRva2VuLnNlc3Npb25Ub2tlbjtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnUmV0dXJuaW5nIHNlc3Npb246Jywgc2Vzc2lvbik7XG4gICAgICByZXR1cm4gc2Vzc2lvbjtcbiAgICB9LFxuICB9LFxuICBzZXNzaW9uOiB7XG4gICAgc3RyYXRlZ3k6ICdqd3QnLFxuICAgIG1heEFnZTogMjQgKiA2MCAqIDYwLCAvLyAyNCBob3Vyc1xuICB9LFxuICBzZWNyZXQ6IHByb2Nlc3MuZW52Lk5FWFRBVVRIX1NFQ1JFVCxcbiAgZGVidWc6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnLFxufTtcbiJdLCJuYW1lcyI6WyJDcmVkZW50aWFsc1Byb3ZpZGVyIiwiY29tcGFyZSIsImRiQ29ubmVjdCIsIlVzZXIiLCJsb2dBdWRpdEV2ZW50IiwiQXVkaXRMb2dBY3Rpb24iLCJnZXRTZXJ2ZXJUaW1lc3RhbXAiLCJjcmVhdGVTZXNzaW9uIiwicmVtb3ZlU2Vzc2lvbiIsImF1dGhPcHRpb25zIiwicHJvdmlkZXJzIiwiaWQiLCJuYW1lIiwiY3JlZGVudGlhbHMiLCJsYWJlbCIsInR5cGUiLCJwYXNzd29yZCIsImF1dGhvcml6ZSIsInJlcSIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsIkVycm9yIiwiZXNjYXBlZE5hbWUiLCJyZXBsYWNlIiwidXNlciIsImZpbmRPbmUiLCIkcmVnZXgiLCJSZWdFeHAiLCJzZWxlY3QiLCJpc1Bhc3N3b3JkQ29ycmVjdCIsInVzZXJJZCIsIl9pZCIsInRvU3RyaW5nIiwidXNlckFnZW50IiwiaGVhZGVycyIsImlwQWRkcmVzcyIsInNvY2tldCIsInJlbW90ZUFkZHJlc3MiLCJzZXNzaW9uVG9rZW4iLCJhY3Rpb24iLCJVU0VSX0xPR0lOIiwicGVyZm9ybWVkQnkiLCJ0YXJnZXRJZCIsInRhcmdldFR5cGUiLCJkZXRhaWxzIiwiZW1haWwiLCJyb2xlIiwiZGl2aXNpb24iLCJpbWFnZSIsInBhZ2VzIiwic2lnbkluIiwiY2FsbGJhY2tzIiwiand0IiwidG9rZW4iLCJ0cmlnZ2VyIiwiY3VzdG9tVXNlciIsInNlcnZlclRpbWVzdGFtcCIsInNlc3Npb24iLCJzdHJhdGVneSIsIm1heEFnZSIsInNlY3JldCIsInByb2Nlc3MiLCJlbnYiLCJORVhUQVVUSF9TRUNSRVQiLCJkZWJ1ZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/mongodb.ts":
/*!*******************************!*\
  !*** ./src/lib/db/mongodb.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isConnectionHealthy: () => (/* binding */ isConnectionHealthy)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _register_models__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./register-models */ \"(rsc)/./src/lib/db/register-models.ts\");\n\n\nconst MONGODB_URI = process.env.MONGODB_URI || \"mongodb://localhost:27017/document-tracker\";\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable\");\n}\n// Connection options\nconst connectionOptions = {\n    bufferCommands: false,\n    serverSelectionTimeoutMS: 10000,\n    socketTimeoutMS: 45000,\n    family: 4,\n    // Enable auto reconnect\n    autoIndex: true,\n    autoCreate: true,\n    // Connection health monitoring\n    heartbeatFrequencyMS: 10000\n};\n// Initialize cached connection object\nlet cached = global.mongoose || {\n    conn: null,\n    promise: null,\n    isConnecting: false,\n    lastConnectionAttempt: 0\n};\nif (!global.mongoose) {\n    global.mongoose = cached;\n}\n// Set up connection event listeners\nfunction setupConnectionMonitoring() {\n    // Only set up listeners once\n    if (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.listenerCount(\"connected\") > 0) return;\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"connected\", ()=>{\n        console.log(\"MongoDB connection established successfully\");\n    });\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"error\", (err)=>{\n        console.error(\"MongoDB connection error:\", err);\n    });\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"disconnected\", ()=>{\n        console.warn(\"MongoDB disconnected. Will attempt to reconnect automatically.\");\n    });\n    // Handle process termination\n    process.on(\"SIGINT\", async ()=>{\n        try {\n            await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n            console.log(\"MongoDB connection closed due to application termination\");\n            process.exit(0);\n        } catch (err) {\n            console.error(\"Error closing MongoDB connection:\", err);\n            process.exit(1);\n        }\n    });\n}\n// Check if connection is healthy\nfunction isConnectionHealthy() {\n    return (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1; // 1 = connected\n}\n// Main connection function\nasync function dbConnect() {\n    // Set up connection monitoring\n    setupConnectionMonitoring();\n    // If we already have a connection and it's healthy, return it\n    if (cached.conn && isConnectionHealthy()) {\n        console.log(\"Using existing MongoDB connection\");\n        return cached.conn;\n    }\n    console.log(\"No healthy MongoDB connection found, creating a new one\");\n    // If we're already trying to connect, wait for that promise\n    if (cached.isConnecting && cached.promise) {\n        try {\n            cached.conn = await cached.promise;\n            return cached.conn;\n        } catch (error) {\n            // If the current connection attempt fails, we'll try again below\n            console.error(\"Ongoing connection attempt failed:\", error);\n        }\n    }\n    // Prevent connection attempts in rapid succession (throttle to once per 5 seconds)\n    const now = Date.now();\n    const minTimeBetweenAttempts = 5000; // 5 seconds\n    if (now - cached.lastConnectionAttempt < minTimeBetweenAttempts) {\n        console.warn(\"Connection attempt throttled. Waiting before retrying...\");\n        await new Promise((resolve)=>setTimeout(resolve, minTimeBetweenAttempts));\n    }\n    // Start a new connection attempt\n    cached.isConnecting = true;\n    cached.lastConnectionAttempt = Date.now();\n    cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, connectionOptions).then((mongoose)=>{\n        console.log(\"MongoDB connected successfully\");\n        cached.isConnecting = false;\n        // Register models after successful connection\n        (0,_register_models__WEBPACK_IMPORTED_MODULE_1__.registerModels)();\n        return mongoose;\n    }).catch((error)=>{\n        console.error(\"MongoDB connection error:\", error);\n        cached.isConnecting = false;\n        cached.promise = null; // Reset the promise on error\n        throw error;\n    });\n    try {\n        cached.conn = await cached.promise;\n        return cached.conn;\n    } catch (error) {\n        console.error(\"Failed to connect to MongoDB:\", error);\n        // Implement exponential backoff for retries in production\n        if (false) {}\n        throw error;\n    }\n}\n// Export the connection function and health check\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/register-models.ts":
/*!***************************************!*\
  !*** ./src/lib/db/register-models.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerModels: () => (/* binding */ registerModels)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\n// Define the Document Journey Schema\nconst DocumentJourneySchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    action: {\n        type: String,\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    fromDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    toDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    byUser: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    timestamp: {\n        type: Date,\n        default: Date.now\n    },\n    notes: {\n        type: String\n    }\n});\n// Define the Document Schema\nconst DocumentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a title\"\n        ],\n        maxlength: [\n            100,\n            \"Title cannot be more than 100 characters\"\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a description\"\n        ]\n    },\n    category: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentCategory),\n        required: [\n            true,\n            \"Please provide a category\"\n        ]\n    },\n    status: {\n        type: String,\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX,\n        // Use a custom validator instead of enum to ensure it works properly with case-insensitivity\n        validate: {\n            validator: function(v) {\n                // Convert both to lowercase for case-insensitive comparison\n                return Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).map((s)=>s.toLowerCase()).includes(v.toLowerCase());\n            },\n            message: (props)=>`${props.value} is not a valid status. Valid statuses are: ${Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).join(\", \")}`\n        }\n    },\n    createdBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    currentLocation: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a current location\"\n        ]\n    },\n    recipientId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\"\n    },\n    relatedDocumentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Document\"\n    },\n    fileUrl: {\n        type: String\n    },\n    fileName: {\n        type: String\n    },\n    fileType: {\n        type: String\n    },\n    trackingNumber: {\n        type: String,\n        // Using sparse index to prevent duplicate key errors with null/undefined values\n        index: {\n            sparse: true\n        },\n        validate: {\n            validator: function(v) {\n                // Allow undefined or null values (will be set later)\n                if (!v) return true;\n                // Validate the format: MGBR2-YYYY-NNNN-NNNN\n                return typeof v === \"string\" && /^MGBR2-\\d{4}-\\d{4}-\\d{4}$/.test(v);\n            },\n            message: (props)=>`${props.value} is not a valid tracking number format. Expected format: MGBR2-YYYY-NNNN-NNNN`\n        }\n    },\n    isOriginal: {\n        type: Boolean,\n        default: false,\n        index: true\n    },\n    journey: [\n        DocumentJourneySchema\n    ]\n}, {\n    timestamps: true\n});\n// Register models\nfunction registerModels() {\n    // Only register models if they haven't been registered yet\n    if (!(mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Document) {\n        mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Document\", DocumentSchema);\n        console.log(\"Document model registered\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/register-models.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/serverInit.ts":
/*!*******************************!*\
  !*** ./src/lib/serverInit.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeServer: () => (/* binding */ initializeServer)\n/* harmony export */ });\n/* harmony import */ var _storage_ensureUploadsDir__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./storage/ensureUploadsDir */ \"(rsc)/./src/lib/storage/ensureUploadsDir.ts\");\n/**\n * Server initialization code\n * This file contains code that runs when the server starts\n */ \n// Flag to track if initialization has been performed\nlet initialized = false;\n/**\n * Initialize server components\n * This function should be called once during server startup\n */ async function initializeServer() {\n    // Only run initialization once\n    if (initialized) {\n        return;\n    }\n    try {\n        console.log(\"Initializing server components...\");\n        // Ensure uploads directory exists and is writable\n        await (0,_storage_ensureUploadsDir__WEBPACK_IMPORTED_MODULE_0__.ensureUploadsDirectory)();\n        // Add other initialization tasks here\n        console.log(\"Server initialization complete.\");\n        initialized = true;\n    } catch (error) {\n        console.error(\"Server initialization failed:\", error);\n    // We don't throw here to prevent the server from crashing\n    // but we log the error so it's visible in the console\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/serverInit.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/storage/ensureUploadsDir.ts":
/*!*********************************************!*\
  !*** ./src/lib/storage/ensureUploadsDir.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureUploadsDirectory: () => (/* binding */ ensureUploadsDirectory)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * Utility to ensure the uploads directory exists and is writable\n * This is called during application startup\n */ \n\n\n/**\n * Ensures that the uploads directory exists and is writable\n * @returns Promise that resolves when the directory is ready\n */ async function ensureUploadsDirectory() {\n    try {\n        // Get the uploads directory path\n        const uploadsDir = process.env.UPLOADS_DIRECTORY || (0,path__WEBPACK_IMPORTED_MODULE_2__.join)(process.cwd(), \"public\", \"uploads\");\n        console.log(`Checking uploads directory: ${uploadsDir}`);\n        // Check if directory exists\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(uploadsDir)) {\n            console.log(\"Uploads directory does not exist. Creating it...\");\n            try {\n                await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.mkdir)(uploadsDir, {\n                    recursive: true,\n                    mode: 493\n                });\n                console.log(\"Uploads directory created successfully.\");\n            } catch (error) {\n                console.error(\"Error creating uploads directory:\", error);\n                throw new Error(`Failed to create uploads directory: ${error instanceof Error ? error.message : String(error)}`);\n            }\n        }\n        // Check if directory is writable\n        try {\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.access)(uploadsDir, fs_promises__WEBPACK_IMPORTED_MODULE_0__.constants.W_OK);\n            console.log(\"Uploads directory is writable.\");\n        } catch (error) {\n            console.error(\"Uploads directory is not writable:\", error);\n            throw new Error(`Uploads directory is not writable: ${error instanceof Error ? error.message : String(error)}`);\n        }\n        // Create a .gitkeep file to ensure the directory is tracked by git\n        const gitkeepPath = (0,path__WEBPACK_IMPORTED_MODULE_2__.join)(uploadsDir, \".gitkeep\");\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(gitkeepPath)) {\n            try {\n                const fs = __webpack_require__(/*! fs */ \"fs\");\n                fs.writeFileSync(gitkeepPath, \"\");\n                console.log(\".gitkeep file created in uploads directory.\");\n            } catch (error) {\n                console.warn(\"Could not create .gitkeep file:\", error);\n            // This is not critical, so we don't throw an error\n            }\n        }\n        console.log(\"Uploads directory is ready.\");\n    } catch (error) {\n        console.error(\"Failed to ensure uploads directory:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/storage/ensureUploadsDir.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/AuditLog.ts":
/*!********************************!*\
  !*** ./src/models/AuditLog.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n\n\nconst AuditLogSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    action: {\n        type: String,\n        enum: Object.values(_types_audit__WEBPACK_IMPORTED_MODULE_1__.AuditLogAction),\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    performedBy: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user who performed the action\"\n        ]\n    },\n    targetId: {\n        type: String\n    },\n    targetType: {\n        type: String\n    },\n    details: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.Mixed\n    },\n    ipAddress: {\n        type: String\n    },\n    userAgent: {\n        type: String\n    }\n}, {\n    timestamps: true\n});\n// Create a text index for searching\nAuditLogSchema.index({\n    action: \"text\",\n    targetType: \"text\"\n});\n// Fix for \"Cannot read properties of undefined (reading 'AuditLog')\" error\n// Check if the model exists in mongoose.models before trying to access it\nlet AuditLog;\ntry {\n    // Try to get the existing model\n    AuditLog = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AuditLog\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    AuditLog = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AuditLog\", AuditLogSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuditLog);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/AuditLog.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\nconst SessionInfoSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    token: {\n        type: String,\n        required: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    },\n    lastActive: {\n        type: Date,\n        default: Date.now\n    },\n    userAgent: {\n        type: String\n    },\n    ipAddress: {\n        type: String\n    }\n});\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a name\"\n        ],\n        maxlength: [\n            60,\n            \"Name cannot be more than 60 characters\"\n        ],\n        unique: true,\n        trim: true\n    },\n    email: {\n        type: String,\n        required: false,\n        lowercase: true,\n        trim: true,\n        unique: true,\n        sparse: true\n    },\n    password: {\n        type: String,\n        select: false\n    },\n    role: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.UserRole),\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.UserRole.EMPLOYEE\n    },\n    division: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a division\"\n        ]\n    },\n    image: {\n        type: String\n    },\n    activeSessions: {\n        type: [\n            SessionInfoSchema\n        ],\n        default: []\n    }\n}, {\n    timestamps: true\n});\n// Fix for \"Cannot read properties of undefined\" error\nlet User;\ntry {\n    // Try to get the existing model\n    User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (User);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(rsc)/./src/polyfills.js":
/*!**************************!*\
  !*** ./src/polyfills.js ***!
  \**************************/
/***/ (() => {

eval("// This file contains only the polyfills we actually need\n// It will be imported in the main layout file\n// Only include polyfills for features that are actually used in the codebase\n// and not supported by our target browsers\n// For example, if we need Promise.allSettled\nif (!Promise.allSettled) {\n    Promise.allSettled = function(promises) {\n        return Promise.all(promises.map((p)=>Promise.resolve(p).then((value)=>({\n                    status: \"fulfilled\",\n                    value\n                })).catch((reason)=>({\n                    status: \"rejected\",\n                    reason\n                }))));\n    };\n} // Add other polyfills as needed, but only if they're actually used in the codebase\n // and not supported by our target browsers\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kb2N1bWVudC10cmFja2VyLy4vc3JjL3BvbHlmaWxscy5qcz8xMWMzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgZmlsZSBjb250YWlucyBvbmx5IHRoZSBwb2x5ZmlsbHMgd2UgYWN0dWFsbHkgbmVlZFxuLy8gSXQgd2lsbCBiZSBpbXBvcnRlZCBpbiB0aGUgbWFpbiBsYXlvdXQgZmlsZVxuXG4vLyBPbmx5IGluY2x1ZGUgcG9seWZpbGxzIGZvciBmZWF0dXJlcyB0aGF0IGFyZSBhY3R1YWxseSB1c2VkIGluIHRoZSBjb2RlYmFzZVxuLy8gYW5kIG5vdCBzdXBwb3J0ZWQgYnkgb3VyIHRhcmdldCBicm93c2Vyc1xuXG4vLyBGb3IgZXhhbXBsZSwgaWYgd2UgbmVlZCBQcm9taXNlLmFsbFNldHRsZWRcbmlmICghUHJvbWlzZS5hbGxTZXR0bGVkKSB7XG4gIFByb21pc2UuYWxsU2V0dGxlZCA9IGZ1bmN0aW9uKHByb21pc2VzKSB7XG4gICAgcmV0dXJuIFByb21pc2UuYWxsKFxuICAgICAgcHJvbWlzZXMubWFwKHAgPT4gXG4gICAgICAgIFByb21pc2UucmVzb2x2ZShwKVxuICAgICAgICAgIC50aGVuKHZhbHVlID0+ICh7IHN0YXR1czogJ2Z1bGZpbGxlZCcsIHZhbHVlIH0pKVxuICAgICAgICAgIC5jYXRjaChyZWFzb24gPT4gKHsgc3RhdHVzOiAncmVqZWN0ZWQnLCByZWFzb24gfSkpXG4gICAgICApXG4gICAgKTtcbiAgfTtcbn1cblxuLy8gQWRkIG90aGVyIHBvbHlmaWxscyBhcyBuZWVkZWQsIGJ1dCBvbmx5IGlmIHRoZXkncmUgYWN0dWFsbHkgdXNlZCBpbiB0aGUgY29kZWJhc2Vcbi8vIGFuZCBub3Qgc3VwcG9ydGVkIGJ5IG91ciB0YXJnZXQgYnJvd3NlcnNcbiJdLCJuYW1lcyI6WyJQcm9taXNlIiwiYWxsU2V0dGxlZCIsInByb21pc2VzIiwiYWxsIiwibWFwIiwicCIsInJlc29sdmUiLCJ0aGVuIiwidmFsdWUiLCJzdGF0dXMiLCJjYXRjaCIsInJlYXNvbiJdLCJtYXBwaW5ncyI6IkFBQUEseURBQXlEO0FBQ3pELDhDQUE4QztBQUU5Qyw2RUFBNkU7QUFDN0UsMkNBQTJDO0FBRTNDLDZDQUE2QztBQUM3QyxJQUFJLENBQUNBLFFBQVFDLFVBQVUsRUFBRTtJQUN2QkQsUUFBUUMsVUFBVSxHQUFHLFNBQVNDLFFBQVE7UUFDcEMsT0FBT0YsUUFBUUcsR0FBRyxDQUNoQkQsU0FBU0UsR0FBRyxDQUFDQyxDQUFBQSxJQUNYTCxRQUFRTSxPQUFPLENBQUNELEdBQ2JFLElBQUksQ0FBQ0MsQ0FBQUEsUUFBVSxDQUFBO29CQUFFQyxRQUFRO29CQUFhRDtnQkFBTSxDQUFBLEdBQzVDRSxLQUFLLENBQUNDLENBQUFBLFNBQVcsQ0FBQTtvQkFBRUYsUUFBUTtvQkFBWUU7Z0JBQU8sQ0FBQTtJQUd2RDtBQUNGLEVBRUEsbUZBQW1GO0NBQ25GLDJDQUEyQyIsImZpbGUiOiIocnNjKS8uL3NyYy9wb2x5ZmlsbHMuanMiLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/polyfills.js\n");

/***/ }),

/***/ "(rsc)/./src/types/audit.ts":
/*!****************************!*\
  !*** ./src/types/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditLogAction: () => (/* binding */ AuditLogAction)\n/* harmony export */ });\nvar AuditLogAction;\n(function(AuditLogAction) {\n    AuditLogAction[\"USER_CREATED\"] = \"USER_CREATED\";\n    AuditLogAction[\"USER_UPDATED\"] = \"USER_UPDATED\";\n    AuditLogAction[\"USER_DELETED\"] = \"USER_DELETED\";\n    AuditLogAction[\"USER_LOGIN\"] = \"USER_LOGIN\";\n    AuditLogAction[\"DOCUMENT_CREATED\"] = \"DOCUMENT_CREATED\";\n    AuditLogAction[\"DOCUMENT_UPDATED\"] = \"DOCUMENT_UPDATED\";\n    AuditLogAction[\"DOCUMENT_DELETED\"] = \"DOCUMENT_DELETED\";\n    AuditLogAction[\"DOCUMENT_STATUS_CHANGED\"] = \"DOCUMENT_STATUS_CHANGED\";\n    AuditLogAction[\"DOCUMENT_SHARED\"] = \"DOCUMENT_SHARED\";\n    AuditLogAction[\"DOCUMENT_FORWARDED\"] = \"DOCUMENT_FORWARDED\";\n    AuditLogAction[\"DOCUMENT_RECEIVED\"] = \"DOCUMENT_RECEIVED\";\n    AuditLogAction[\"DOCUMENT_PROCESSED\"] = \"DOCUMENT_PROCESSED\";\n    AuditLogAction[\"PROFILE_CHANGE_REQUESTED\"] = \"PROFILE_CHANGE_REQUESTED\";\n    AuditLogAction[\"PROFILE_CHANGE_APPROVED\"] = \"PROFILE_CHANGE_APPROVED\";\n    AuditLogAction[\"PROFILE_CHANGE_REJECTED\"] = \"PROFILE_CHANGE_REJECTED\";\n    AuditLogAction[\"FILE_UPLOADED\"] = \"FILE_UPLOADED\";\n    AuditLogAction[\"FILE_DOWNLOADED\"] = \"FILE_DOWNLOADED\";\n    AuditLogAction[\"FILE_DELETED\"] = \"FILE_DELETED\";\n    AuditLogAction[\"FILE_ACCESSED\"] = \"FILE_ACCESSED\";\n    AuditLogAction[\"FEEDBACK_CREATED\"] = \"FEEDBACK_CREATED\";\n    AuditLogAction[\"FEEDBACK_UPDATED\"] = \"FEEDBACK_UPDATED\";\n    AuditLogAction[\"FEEDBACK_DELETED\"] = \"FEEDBACK_DELETED\";\n    AuditLogAction[\"FEEDBACK_STATUS_CHANGED\"] = \"FEEDBACK_STATUS_CHANGED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_GENERATED\"] = \"FEEDBACK_AI_SUGGESTION_GENERATED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_REMOVED\"] = \"FEEDBACK_AI_SUGGESTION_REMOVED\";\n    AuditLogAction[\"SEARCH_PERFORMED\"] = \"SEARCH_PERFORMED\";\n    AuditLogAction[\"DATA_CLEANUP\"] = \"DATA_CLEANUP\";\n    AuditLogAction[\"SYSTEM_MAINTENANCE\"] = \"SYSTEM_MAINTENANCE\";\n    AuditLogAction[\"ARCHIVE_EXPORTED\"] = \"ARCHIVE_EXPORTED\";\n})(AuditLogAction || (AuditLogAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Division: () => (/* binding */ Division),\n/* harmony export */   DocumentAction: () => (/* binding */ DocumentAction),\n/* harmony export */   DocumentCategory: () => (/* binding */ DocumentCategory),\n/* harmony export */   DocumentStatus: () => (/* binding */ DocumentStatus),\n/* harmony export */   FeedbackCategory: () => (/* binding */ FeedbackCategory),\n/* harmony export */   FeedbackStatus: () => (/* binding */ FeedbackStatus),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\n// User Roles\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n    UserRole[\"REGIONAL_DIRECTOR\"] = \"REGIONAL_DIRECTOR\";\n    UserRole[\"DIVISION_CHIEF\"] = \"DIVISION_CHIEF\";\n    UserRole[\"EMPLOYEE\"] = \"EMPLOYEE\";\n})(UserRole || (UserRole = {}));\nvar Division;\n(function(Division) {\n    Division[\"ORD\"] = \"ORD\";\n    Division[\"FAD\"] = \"FAD\";\n    Division[\"MMD\"] = \"MMD\";\n    Division[\"MSESDD\"] = \"MSESDD\";\n    Division[\"GSD\"] = \"GSD\";\n})(Division || (Division = {}));\nvar DocumentStatus;\n(function(DocumentStatus) {\n    DocumentStatus[\"INBOX\"] = \"INBOX\";\n    DocumentStatus[\"SENT\"] = \"SENT\";\n    DocumentStatus[\"RECEIVED\"] = \"RECEIVED\";\n    DocumentStatus[\"FORWARDED\"] = \"FORWARDED\";\n    DocumentStatus[\"PENDING\"] = \"PENDING\";\n    DocumentStatus[\"PROCESSED\"] = \"PROCESSED\";\n    DocumentStatus[\"ARCHIVED\"] = \"ARCHIVED\"; // Document has been archived for storage\n})(DocumentStatus || (DocumentStatus = {}));\nvar DocumentCategory;\n(function(DocumentCategory) {\n    // Common Office Documents\n    DocumentCategory[\"MEMO\"] = \"MEMO\";\n    DocumentCategory[\"LETTER\"] = \"LETTER\";\n    DocumentCategory[\"REPORT\"] = \"REPORT\";\n    DocumentCategory[\"PROPOSAL\"] = \"PROPOSAL\";\n    DocumentCategory[\"MINUTES\"] = \"MINUTES\";\n    DocumentCategory[\"FORM\"] = \"FORM\";\n    // Official Documents\n    DocumentCategory[\"CIRCULAR\"] = \"CIRCULAR\";\n    DocumentCategory[\"ADVISORY\"] = \"ADVISORY\";\n    DocumentCategory[\"BULLETIN\"] = \"BULLETIN\";\n    DocumentCategory[\"NOTICE\"] = \"NOTICE\";\n    DocumentCategory[\"ANNOUNCEMENT\"] = \"ANNOUNCEMENT\";\n    DocumentCategory[\"RESOLUTION\"] = \"RESOLUTION\";\n    DocumentCategory[\"POLICY\"] = \"POLICY\";\n    DocumentCategory[\"GUIDELINE\"] = \"GUIDELINE\";\n    DocumentCategory[\"DIRECTIVE\"] = \"DIRECTIVE\";\n    DocumentCategory[\"MEMORANDUM_ORDER\"] = \"MEMORANDUM ORDER\";\n    DocumentCategory[\"MEMORANDUM_CIRCULAR\"] = \"MEMORANDUM CIRCULAR\";\n    DocumentCategory[\"EXECUTIVE_ORDER\"] = \"EXECUTIVE ORDER\";\n    DocumentCategory[\"ADMINISTRATIVE_ORDER\"] = \"ADMINISTRATIVE ORDER\";\n    // Legal & Financial Documents\n    DocumentCategory[\"CONTRACT\"] = \"CONTRACT\";\n    DocumentCategory[\"CERTIFICATE\"] = \"CERTIFICATE\";\n    DocumentCategory[\"ENDORSEMENT\"] = \"ENDORSEMENT\";\n    DocumentCategory[\"MANUAL\"] = \"MANUAL\";\n    DocumentCategory[\"INVOICE\"] = \"INVOICE\";\n    DocumentCategory[\"RECEIPT\"] = \"RECEIPT\";\n    DocumentCategory[\"VOUCHER\"] = \"VOUCHER\";\n    DocumentCategory[\"REQUISITION\"] = \"REQUISITION\";\n    DocumentCategory[\"PURCHASE_ORDER\"] = \"PURCHASE ORDER\";\n    DocumentCategory[\"BUDGET_REQUEST\"] = \"BUDGET REQUEST\";\n    DocumentCategory[\"TRAVEL_ORDER\"] = \"TRAVEL ORDER\";\n    DocumentCategory[\"LEAVE_FORM\"] = \"LEAVE FORM\";\n    // Other\n    DocumentCategory[\"OTHER\"] = \"OTHER\";\n})(DocumentCategory || (DocumentCategory = {}));\nvar DocumentAction;\n(function(DocumentAction) {\n    DocumentAction[\"NONE\"] = \"No specific action required\";\n    // Actions from the image (A-S)\n    DocumentAction[\"FOR_INFO\"] = \"A - For information/guidance/reference\";\n    DocumentAction[\"FOR_COMMENTS\"] = \"B - For comments/recommendations\";\n    DocumentAction[\"TAKE_UP\"] = \"C - Pls. take up with me\";\n    DocumentAction[\"DRAFT_ANSWER\"] = \"D - Pls. draft answer/memo/acknow.\";\n    DocumentAction[\"FOR_ACTION\"] = \"E - For appropriate action\";\n    DocumentAction[\"IMMEDIATE_INVESTIGATION\"] = \"F - Pls. immediate investigation\";\n    DocumentAction[\"ATTACH_SUPPORTING\"] = \"G - Pls. attach supporting papers\";\n    DocumentAction[\"FOR_APPROVAL\"] = \"H - For approval\";\n    DocumentAction[\"FOR_SIGNATURE\"] = \"I - For initial/signature\";\n    DocumentAction[\"STUDY_EVALUATE\"] = \"J - Pls. study / evaluate\";\n    DocumentAction[\"RELEASE_FILE\"] = \"K - Pls. release/file\";\n    DocumentAction[\"UPDATE_STATUS\"] = \"L - Update status of case\";\n    DocumentAction[\"FILE_CLOSE\"] = \"M - Filed / Close\";\n    DocumentAction[\"FOR_ADA\"] = \"N - For ADA / Check Preparation\";\n    DocumentAction[\"FOR_DISCUSSION\"] = \"O - FOD (For Discussion)\";\n    DocumentAction[\"FOR_REVISION\"] = \"P - For Revision\";\n    DocumentAction[\"ATTACH_DRAFT\"] = \"Q - Pls. Attach Draft File\";\n    DocumentAction[\"SAVED\"] = \"R - Saved\";\n    DocumentAction[\"FOR_SCANNING\"] = \"S - For Scanning\";\n    // Additional useful actions for office work\n    DocumentAction[\"URGENT\"] = \"URGENT - Requires immediate attention\";\n    DocumentAction[\"CONFIDENTIAL\"] = \"CONFIDENTIAL - Restricted access\";\n    DocumentAction[\"FOR_REVIEW\"] = \"FOR REVIEW - Please review and provide feedback\";\n    DocumentAction[\"FOR_COORDINATION\"] = \"FOR COORDINATION - Coordinate with relevant departments\";\n    DocumentAction[\"FOR_COMPLIANCE\"] = \"FOR COMPLIANCE - Ensure compliance with regulations\";\n    DocumentAction[\"FOR_IMPLEMENTATION\"] = \"FOR IMPLEMENTATION - Implement the described actions\";\n    DocumentAction[\"FOR_FILING\"] = \"FOR FILING - File for future reference\";\n    DocumentAction[\"FOR_DISTRIBUTION\"] = \"FOR DISTRIBUTION - Distribute to concerned parties\";\n    DocumentAction[\"FOR_ENDORSEMENT\"] = \"FOR ENDORSEMENT - Endorse to appropriate authority\";\n    DocumentAction[\"FOR_VERIFICATION\"] = \"FOR VERIFICATION - Verify information/data\";\n    DocumentAction[\"FOR_RECORDING\"] = \"FOR RECORDING - Record in the system\";\n})(DocumentAction || (DocumentAction = {}));\nvar FeedbackCategory;\n(function(FeedbackCategory) {\n    FeedbackCategory[\"BUG\"] = \"bug\";\n    FeedbackCategory[\"FEATURE\"] = \"feature\";\n    FeedbackCategory[\"IMPROVEMENT\"] = \"improvement\";\n    FeedbackCategory[\"OTHER\"] = \"other\";\n})(FeedbackCategory || (FeedbackCategory = {}));\nvar FeedbackStatus;\n(function(FeedbackStatus) {\n    FeedbackStatus[\"PENDING\"] = \"pending\";\n    FeedbackStatus[\"REVIEWED\"] = \"reviewed\";\n    FeedbackStatus[\"IMPLEMENTED\"] = \"implemented\";\n    FeedbackStatus[\"REJECTED\"] = \"rejected\";\n})(FeedbackStatus || (FeedbackStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/audit.ts":
/*!****************************!*\
  !*** ./src/utils/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logAuditEvent: () => (/* binding */ logAuditEvent)\n/* harmony export */ });\n/* harmony import */ var _models_AuditLog__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/AuditLog */ \"(rsc)/./src/models/AuditLog.ts\");\n\nasync function logAuditEvent({ action, performedBy, targetId, targetType, details, request }) {\n    try {\n        const auditLogData = {\n            action,\n            performedBy,\n            targetId,\n            targetType,\n            details\n        };\n        // Add request information if available\n        if (request) {\n            auditLogData.ipAddress = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n            auditLogData.userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        }\n        // Create the audit log entry\n        await _models_AuditLog__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create(auditLogData);\n    } catch (error) {\n        console.error(\"Error creating audit log:\", error);\n    // Don't throw the error - we don't want to break the main functionality\n    // if audit logging fails\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/serverTimestamp.ts":
/*!**************************************!*\
  !*** ./src/utils/serverTimestamp.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerTimestamp: () => (/* binding */ getServerTimestamp)\n/* harmony export */ });\n/**\n * This utility manages the server timestamp used to invalidate sessions on server restart\n */ // Use a more stable identifier that doesn't change on every development hot reload\n// In production, this will still change on server restart\nlet SERVER_START_TIMESTAMP;\n// Try to use a timestamp that persists across hot reloads in development\nif (true) {\n    // In development, use a timestamp that changes daily instead of on every restart\n    // This prevents constant logouts during development\n    const today = new Date();\n    const dateString = `${today.getFullYear()}-${today.getMonth()}-${today.getDate()}`;\n    SERVER_START_TIMESTAMP = dateString;\n} else {}\n// Function to get the current server timestamp\nfunction getServerTimestamp() {\n    return SERVER_START_TIMESTAMP;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/serverTimestamp.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/sessionToken.ts":
/*!***********************************!*\
  !*** ./src/utils/sessionToken.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupAllExpiredSessions: () => (/* binding */ cleanupAllExpiredSessions),\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   removeAllOtherSessions: () => (/* binding */ removeAllOtherSessions),\n/* harmony export */   removeSession: () => (/* binding */ removeSession),\n/* harmony export */   validateSession: () => (/* binding */ validateSession)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n\n\n\n// Maximum number of sessions per user\nconst MAX_SESSIONS_PER_USER = 5;\n// Session expiration time in milliseconds (24 hours)\nconst SESSION_EXPIRATION_MS = 24 * 60 * 60 * 1000;\n/**\n * Generate a unique session token\n * @returns A random session token\n */ function generateSessionToken() {\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes)(32).toString(\"hex\");\n}\n/**\n * Create a new session for a user\n * @param userId The user ID\n * @param userAgent The user agent string\n * @param ipAddress The IP address\n * @returns The session token\n */ async function createSession(userId, userAgent, ipAddress) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const sessionToken = generateSessionToken();\n    const now = new Date();\n    const sessionInfo = {\n        token: sessionToken,\n        createdAt: now,\n        lastActive: now,\n        userAgent,\n        ipAddress\n    };\n    // Get the user with their current sessions\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    if (!user) {\n        throw new Error(\"User not found\");\n    }\n    // Clean up expired sessions first\n    await cleanupExpiredSessions(userId);\n    // If the user has too many active sessions, remove the oldest ones\n    if (user.activeSessions && user.activeSessions.length >= MAX_SESSIONS_PER_USER) {\n        // Sort sessions by lastActive (oldest first)\n        const sortedSessions = [\n            ...user.activeSessions\n        ].sort((a, b)=>a.lastActive.getTime() - b.lastActive.getTime());\n        // Calculate how many sessions to remove\n        const sessionsToRemove = Math.max(0, sortedSessions.length - MAX_SESSIONS_PER_USER + 1);\n        if (sessionsToRemove > 0) {\n            // Get the tokens of the oldest sessions\n            const tokensToRemove = sortedSessions.slice(0, sessionsToRemove).map((session)=>session.token);\n            // Remove the oldest sessions\n            await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n                _id: userId\n            }, {\n                $pull: {\n                    activeSessions: {\n                        token: {\n                            $in: tokensToRemove\n                        }\n                    }\n                }\n            });\n        }\n    }\n    // Add the new session\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findByIdAndUpdate(userId, {\n        $push: {\n            activeSessions: sessionInfo\n        }\n    }, {\n        new: true\n    });\n    return sessionToken;\n}\n/**\n * Clean up expired sessions for a user\n * @param userId The user ID\n */ async function cleanupExpiredSessions(userId) {\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                lastActive: {\n                    $lt: expirationThreshold\n                }\n            }\n        }\n    });\n}\n/**\n * Validate a session token for a user\n * @param userId The user ID\n * @param sessionToken The session token to validate\n * @returns True if the session is valid, false otherwise\n */ async function validateSession(userId, sessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // Clean up expired sessions first\n    await cleanupExpiredSessions(userId);\n    // Find the user and check if they have this session token\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findOne({\n        _id: userId,\n        \"activeSessions.token\": sessionToken\n    });\n    if (!user) {\n        return false;\n    }\n    // Find the specific session\n    const session = user.activeSessions.find((s)=>s.token === sessionToken);\n    if (!session) {\n        return false;\n    }\n    // Check if the session has expired\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    if (session.lastActive < expirationThreshold) {\n        // Session has expired, remove it\n        await removeSession(userId, sessionToken);\n        return false;\n    }\n    // Update the last active time for this session\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId,\n        \"activeSessions.token\": sessionToken\n    }, {\n        $set: {\n            \"activeSessions.$.lastActive\": now\n        }\n    });\n    return true;\n}\n/**\n * Remove a session for a user\n * @param userId The user ID\n * @param sessionToken The session token to remove\n */ async function removeSession(userId, sessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: sessionToken\n            }\n        }\n    });\n}\n/**\n * Get all active sessions for a user\n * @param userId The user ID\n * @returns Array of active sessions\n */ async function getUserSessions(userId) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    return user?.activeSessions || [];\n}\n/**\n * Remove all sessions for a user except the current one\n * @param userId The user ID\n * @param currentSessionToken The current session token to keep\n * @returns The number of sessions removed\n */ async function removeAllOtherSessions(userId, currentSessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // First clean up expired sessions\n    await cleanupExpiredSessions(userId);\n    // Get the user to count sessions before removal\n    const userBefore = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    const sessionCountBefore = userBefore?.activeSessions?.length || 0;\n    // Remove all other sessions\n    const result = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: {\n                    $ne: currentSessionToken\n                }\n            }\n        }\n    });\n    // Get the user again to count sessions after removal\n    const userAfter = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    const sessionCountAfter = userAfter?.activeSessions?.length || 0;\n    // Return the number of sessions removed\n    return sessionCountBefore - sessionCountAfter;\n}\n/**\n * Cleanup all expired sessions in the database\n * This can be run as a scheduled task\n */ async function cleanupAllExpiredSessions() {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    // Find all users with expired sessions\n    const users = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].find({\n        \"activeSessions.lastActive\": {\n            $lt: expirationThreshold\n        }\n    });\n    let totalRemoved = 0;\n    // Clean up expired sessions for each user\n    for (const user of users){\n        const sessionCountBefore = user.activeSessions.length;\n        // Remove expired sessions\n        await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n            _id: user._id\n        }, {\n            $pull: {\n                activeSessions: {\n                    lastActive: {\n                        $lt: expirationThreshold\n                    }\n                }\n            }\n        });\n        // Get the user again to count sessions after removal\n        const updatedUser = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(user._id);\n        const sessionCountAfter = updatedUser?.activeSessions?.length || 0;\n        totalRemoved += sessionCountBefore - sessionCountAfter;\n    }\n    return totalRemoved;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/sessionToken.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@babel","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();