"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx":
/*!**************************************************!*\
  !*** ./src/components/chatbot/ChatInterface.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AlertProvider */ \"(app-pages-browser)/./src/components/AlertProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatInterface(param) {\n    var _this = this;\n    var isOpen = param.isOpen, onClose = param.onClose, documentContext = param.documentContext;\n    _s();\n    var _useSession = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)(), session = _useSession.data;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var showAlert = (0,_components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__.useAlert)().showAlert;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), messages = _useState[0], setMessages = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), inputMessage = _useState1[0], setInputMessage = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isLoading = _useState2[0], setIsLoading = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), sessionId = _useState3[0], setSessionId = _useState3[1];\n    var _useState4 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isTyping = _useState4[0], setIsTyping = _useState4[1];\n    var messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        messages\n    ]);\n    // Focus input when chat opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isOpen && inputRef.current) {\n            inputRef.current.focus();\n        }\n    }, [\n        isOpen\n    ]);\n    // Start chat session when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isOpen && !sessionId && (session === null || session === void 0 ? void 0 : session.user)) {\n            // Try to restore session from localStorage first\n            var savedSessionId = localStorage.getItem(\"chatbot_session_id\");\n            var savedSessionTime = localStorage.getItem(\"chatbot_session_time\");\n            // Check if saved session is still valid (less than 4 hours old)\n            if (savedSessionId && savedSessionTime) {\n                var sessionTime = new Date(savedSessionTime);\n                var fourHoursAgo = new Date(Date.now() - 4 * 60 * 60 * 1000);\n                if (sessionTime > fourHoursAgo) {\n                    console.log(\"Restoring chat session from localStorage:\", savedSessionId);\n                    setSessionId(savedSessionId);\n                    return;\n                } else {\n                    // Clear expired session data\n                    localStorage.removeItem(\"chatbot_session_id\");\n                    localStorage.removeItem(\"chatbot_session_time\");\n                }\n            }\n            startChatSession();\n        }\n    }, [\n        isOpen,\n        session\n    ]);\n    // Update document context when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (sessionId && documentContext) {\n            updateDocumentContext();\n        }\n    }, [\n        sessionId,\n        documentContext\n    ]);\n    var startChatSession = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var response, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            4,\n                            5\n                        ]);\n                        setIsLoading(true);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"start_session\",\n                                    context: {\n                                        currentPage: pathname,\n                                        systemState: {\n                                            timestamp: new Date().toISOString()\n                                        }\n                                    }\n                                })\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            throw new Error(\"Failed to start chat session\");\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        data = _state.sent();\n                        setSessionId(data.data.sessionId);\n                        setMessages(data.data.messages);\n                        // Save session to localStorage for persistence\n                        localStorage.setItem(\"chatbot_session_id\", data.data.sessionId);\n                        localStorage.setItem(\"chatbot_session_time\", new Date().toISOString());\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error starting chat session:\", error);\n                        showAlert({\n                            message: \"Failed to start chat session\",\n                            type: \"error\"\n                        });\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setIsLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function startChatSession() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var updateDocumentContext = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!sessionId || !documentContext) return [\n                            2\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"update_document_context\",\n                                    sessionId: sessionId,\n                                    documentData: documentContext\n                                })\n                            })\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error updating document context:\", error);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function updateDocumentContext() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var sendMessage = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function(message) {\n            var userMessage, response, errorData, data, error, _error_message;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!message.trim() || isLoading) return [\n                            2\n                        ];\n                        if (!!sessionId) return [\n                            3,\n                            2\n                        ];\n                        showAlert({\n                            message: \"Starting chat session...\",\n                            type: \"info\"\n                        });\n                        return [\n                            4,\n                            startChatSession()\n                        ];\n                    case 1:\n                        _state.sent();\n                        // Wait a bit for session to be created, then try again\n                        setTimeout(function() {\n                            if (sessionId) {\n                                sendMessage(message);\n                            }\n                        }, 1000);\n                        return [\n                            2\n                        ];\n                    case 2:\n                        userMessage = {\n                            id: \"temp_\".concat(Date.now()),\n                            role: \"user\",\n                            content: message,\n                            timestamp: new Date()\n                        };\n                        setMessages(function(prev) {\n                            return (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(prev).concat([\n                                userMessage\n                            ]);\n                        });\n                        setInputMessage(\"\");\n                        setIsLoading(true);\n                        setIsTyping(true);\n                        _state.label = 3;\n                    case 3:\n                        _state.trys.push([\n                            3,\n                            8,\n                            9,\n                            10\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"send_message\",\n                                    sessionId: sessionId,\n                                    message: message,\n                                    context: {\n                                        currentPage: pathname\n                                    }\n                                })\n                            })\n                        ];\n                    case 4:\n                        response = _state.sent();\n                        if (!!response.ok) return [\n                            3,\n                            6\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 5:\n                        errorData = _state.sent();\n                        // Handle session expiration\n                        if (response.status === 410 && errorData.error === \"SESSION_EXPIRED\") {\n                            showAlert({\n                                message: \"Chat session expired. Starting a new conversation...\",\n                                type: \"warning\"\n                            });\n                            // Clear current session and start a new one\n                            setSessionId(null);\n                            setMessages([]);\n                            // Clear localStorage\n                            localStorage.removeItem(\"chatbot_session_id\");\n                            localStorage.removeItem(\"chatbot_session_time\");\n                            // Restart the session\n                            setTimeout(function() {\n                                if (session === null || session === void 0 ? void 0 : session.user) {\n                                    startChatSession();\n                                }\n                            }, 1000);\n                            return [\n                                2\n                            ];\n                        }\n                        throw new Error(errorData.message || \"Failed to send message\");\n                    case 6:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 7:\n                        data = _state.sent();\n                        setMessages(data.data.messages);\n                        return [\n                            3,\n                            10\n                        ];\n                    case 8:\n                        error = _state.sent();\n                        console.error(\"Error sending message:\", error);\n                        // Don't show error for session expiration as we handle it above\n                        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"session expired\"))) {\n                            showAlert({\n                                message: error.message || \"Failed to send message\",\n                                type: \"error\"\n                            });\n                        }\n                        return [\n                            3,\n                            10\n                        ];\n                    case 9:\n                        setIsLoading(false);\n                        setIsTyping(false);\n                        return [\n                            7\n                        ];\n                    case 10:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function sendMessage(message) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var handleQuickReply = function(reply) {\n        sendMessage(reply);\n    };\n    var handleAction = function(action) {\n        switch(action.type){\n            case \"navigate\":\n                var _action_data;\n                if ((_action_data = action.data) === null || _action_data === void 0 ? void 0 : _action_data.url) {\n                    window.location.href = action.data.url;\n                }\n                break;\n            case \"search\":\n                // Implement search functionality\n                console.log(\"Search action:\", action.data);\n                break;\n            case \"help\":\n                // Show help modal or navigate to help\n                console.log(\"Help action:\", action.data);\n                break;\n            default:\n                console.log(\"Unknown action:\", action);\n        }\n    };\n    var formatMessage = function(content) {\n        // Handle undefined, null, or empty content\n        if (!content || typeof content !== \"string\") {\n            return \"<div>No content available</div>\";\n        }\n        // Split content by lines and format each line separately to avoid nesting issues\n        var lines = content.split(\"\\n\");\n        var formattedLines = lines.map(function(line) {\n            return line.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\").replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n        });\n        // Join with div elements instead of br tags to avoid nesting issues\n        return formattedLines.map(function(line) {\n            return line.trim() ? \"<div>\".concat(line, \"</div>\") : \"<div>&nbsp;</div>\";\n        }).join(\"\");\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-end justify-end p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black bg-opacity-25\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-96 h-[600px] flex flex-col border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-lg\",\n                                                    children: \"\\uD83E\\uDD16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-white\",\n                                                children: \"MGB Bot\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"Typing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\",\n                                                                    style: {\n                                                                        animationDelay: \"0.1s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\",\n                                                                    style: {\n                                                                        animationDelay: \"0.2s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this) : \"AI Assistant • Online\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-white hover:text-blue-200 transition-all duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-20 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50\",\n                                title: \"Close MGB Bot\",\n                                \"aria-label\": \"Close chatbot\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2.5,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                        children: [\n                            messages.map(function(message) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                    children: [\n                                        message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"\\uD83E\\uDD16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm \".concat(message.role === \"user\" ? \"bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md\" : \"bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 rounded-bl-md\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"prose prose-sm max-w-none\",\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: formatMessage(message.content)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                message.actions && message.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 space-y-2\",\n                                                    children: message.actions.map(function(action, index) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: function() {\n                                                                return handleAction(action);\n                                                            },\n                                                            className: \"flex items-center justify-center w-full px-3 py-2 text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1\",\n                                                                    children: \"\\uD83D\\uDD17\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                action.label\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                message.quickReplies && message.quickReplies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2\",\n                                                    children: message.quickReplies.map(function(reply, index) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: function() {\n                                                                return handleQuickReply(reply);\n                                                            },\n                                                            className: \"px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-sm\",\n                                                            children: [\n                                                                \"\\uD83D\\uDCAC \",\n                                                                reply\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, _this);\n                            }),\n                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"\\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                    children: \"MGB Bot is thinking\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 ml-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.1s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.2s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: inputRef,\n                                                type: \"text\",\n                                                value: inputMessage,\n                                                onChange: function(e) {\n                                                    return setInputMessage(e.target.value);\n                                                },\n                                                onKeyDown: function(e) {\n                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                        e.preventDefault();\n                                                        sendMessage(inputMessage);\n                                                    }\n                                                },\n                                                placeholder: \"Type your message...\",\n                                                disabled: isLoading,\n                                                className: \"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return sendMessage(inputMessage);\n                                        },\n                                        disabled: isLoading || !inputMessage.trim(),\n                                        className: \"px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-sm\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 flex flex-wrap gap-2\",\n                                children: [\n                                    \"How to send documents?\",\n                                    \"Check my inbox\",\n                                    \"Document workflow\"\n                                ].map(function(suggestion, index) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return sendMessage(suggestion);\n                                        },\n                                        disabled: isLoading,\n                                        className: \"px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-full transition-colors duration-200 disabled:opacity-50\",\n                                        children: suggestion\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"V6NTTLWUzvNZRus1A3jSe2LATHk=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__.useAlert\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx\n"));

/***/ })

});