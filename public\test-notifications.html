<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Notifications - Document Tracker</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2d3748;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            border-color: #4299e1;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .feature-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .feature-description {
            color: #4a5568;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 1rem;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .btn.danger {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }
        .btn.danger:hover {
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-weight: 500;
        }
        .result.success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        .result.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }
        .instructions {
            background: #ebf8ff;
            border: 1px solid #90cdf4;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            color: #2b6cb0;
            margin-top: 0;
        }
        .instructions ol {
            color: #2d3748;
            line-height: 1.6;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            font-style: italic;
            color: #4a5568;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Enhanced Notification System Test</h1>
        
        <div class="instructions">
            <h3>📋 How to Test the Enhanced Notifications:</h3>
            <ol>
                <li><strong>Login first:</strong> Go to <a href="/auth/signin" target="_blank">Sign In</a> to authenticate</li>
                <li><strong>Create test notifications</strong> using the buttons below</li>
                <li><strong>Check the notification bell</strong> in the top-right corner of the main app</li>
                <li><strong>Notice the real-time updates</strong> - notifications appear instantly</li>
                <li><strong>Test the features:</strong> Mark as read, delete, mark all as read</li>
                <li><strong>Clean up</strong> when done testing</li>
            </ol>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-title">
                    📢 Regular Notification
                </div>
                <div class="feature-description">
                    Create a standard notification to test basic functionality and real-time updates.
                </div>
                <button class="btn" onclick="createNotification('regular')">
                    Create Regular Notification
                </button>
                <div id="regular-result"></div>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    🤖 Smart AI Notification
                </div>
                <div class="feature-description">
                    Create an AI-powered notification with priority levels, suggested actions, and smart features.
                </div>
                <button class="btn" onclick="createNotification('smart')">
                    Create Smart Notification
                </button>
                <div id="smart-result"></div>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    🎯 Both Types
                </div>
                <div class="feature-description">
                    Create both regular and smart notifications at once to test the combined system.
                </div>
                <button class="btn" onclick="createNotification('both')">
                    Create Both Types
                </button>
                <div id="both-result"></div>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    🧹 Clean Up
                </div>
                <div class="feature-description">
                    Remove all test notifications to clean up your notification list.
                </div>
                <button class="btn danger" onclick="cleanupNotifications()">
                    Delete All Test Notifications
                </button>
                <div id="cleanup-result"></div>
            </div>
        </div>

        <div class="status" id="status">
            Ready to test enhanced notifications! 🚀
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/dashboard" class="btn" style="display: inline-block; text-decoration: none; max-width: 200px;">
                🏠 Back to Dashboard
            </a>
        </div>
    </div>

    <script>
        async function createNotification(type) {
            const button = event.target;
            const resultDiv = document.getElementById(`${type}-result`);
            const statusDiv = document.getElementById('status');
            
            button.disabled = true;
            button.textContent = 'Creating...';
            statusDiv.textContent = `Creating ${type} notification(s)...`;
            
            try {
                const response = await fetch('/api/notifications/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ type })
                });

                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `<div class="result success">✅ ${data.message}</div>`;
                    statusDiv.textContent = `✅ ${data.message} - Check the notification bell!`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Error: ${data.message}</div>`;
                    statusDiv.textContent = `❌ Failed to create notification`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
                statusDiv.textContent = `❌ Network error occurred`;
            } finally {
                button.disabled = false;
                button.textContent = button.textContent.replace('Creating...', 
                    type === 'regular' ? 'Create Regular Notification' :
                    type === 'smart' ? 'Create Smart Notification' :
                    'Create Both Types'
                );
            }
        }

        async function cleanupNotifications() {
            const button = event.target;
            const resultDiv = document.getElementById('cleanup-result');
            const statusDiv = document.getElementById('status');
            
            button.disabled = true;
            button.textContent = 'Cleaning up...';
            statusDiv.textContent = 'Deleting test notifications...';
            
            try {
                const response = await fetch('/api/notifications/test', {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `<div class="result success">✅ ${data.message}</div>`;
                    statusDiv.textContent = `✅ ${data.message}`;
                    
                    // Clear other result divs
                    ['regular', 'smart', 'both'].forEach(type => {
                        const div = document.getElementById(`${type}-result`);
                        if (div) div.innerHTML = '';
                    });
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Error: ${data.message}</div>`;
                    statusDiv.textContent = `❌ Failed to delete notifications`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
                statusDiv.textContent = `❌ Network error occurred`;
            } finally {
                button.disabled = false;
                button.textContent = 'Delete All Test Notifications';
            }
        }
    </script>
</body>
</html>
