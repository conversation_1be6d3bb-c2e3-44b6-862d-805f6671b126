'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, X, Calendar, User, FileText, Tag, MapPin } from 'lucide-react';

interface SearchFilters {
  query: string;
  status: string[];
  category: string[];
  division: string[];
  dateRange: {
    start: string;
    end: string;
  };
  createdBy: string;
  recipient: string;
  trackingNumber: string;
  fileType: string[];
  priority: string[];
}

interface AdvancedSearchProps {
  onSearch: (filters: SearchFilters) => void;
  onClear: () => void;
  initialFilters?: Partial<SearchFilters>;
}

const defaultFilters: SearchFilters = {
  query: '',
  status: [],
  category: [],
  division: [],
  dateRange: { start: '', end: '' },
  createdBy: '',
  recipient: '',
  trackingNumber: '',
  fileType: [],
  priority: []
};

const statusOptions = [
  { value: 'PENDING', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'RECEIVED', label: 'Received', color: 'bg-blue-100 text-blue-800' },
  { value: 'PROCESSED', label: 'Processed', color: 'bg-green-100 text-green-800' },
  { value: 'FORWARDED', label: 'Forwarded', color: 'bg-purple-100 text-purple-800' },
  { value: 'ARCHIVED', label: 'Archived', color: 'bg-gray-100 text-gray-800' },
  { value: 'SENT', label: 'Sent', color: 'bg-indigo-100 text-indigo-800' }
];

const categoryOptions = [
  'MEMO', 'LETTER', 'REPORT', 'REQUEST', 'DIRECTIVE', 'CIRCULAR', 'NOTICE', 'OTHER'
];

const divisionOptions = [
  { value: 'ORD', label: 'Office of Regional Director' },
  { value: 'FAD', label: 'Finance and Administrative Division' },
  { value: 'MMD', label: 'Mines Management Division' },
  { value: 'MSESDD', label: 'Safety & Environmental Division' },
  { value: 'GSD', label: 'Geological Survey Division' }
];

const fileTypeOptions = [
  'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'zip', 'rar'
];

const priorityOptions = [
  { value: 'low', label: 'Low', color: 'bg-gray-100 text-gray-800' },
  { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' }
];

export default function AdvancedSearch({ onSearch, onClear, initialFilters }: AdvancedSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({ ...defaultFilters, ...initialFilters });
  const [isExpanded, setIsExpanded] = useState(false);
  const [users, setUsers] = useState<Array<{ _id: string; name: string; division: string }>>([]);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users?limit=100');
      const data = await response.json();
      if (data.success) {
        setUsers(data.users);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleArrayFilterChange = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => {
      const currentArray = prev[key] as string[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      return { ...prev, [key]: newArray };
    });
  };

  const handleSearch = () => {
    onSearch(filters);
  };

  const handleClear = () => {
    setFilters(defaultFilters);
    onClear();
  };

  const hasActiveFilters = () => {
    return (
      filters.query ||
      filters.status.length > 0 ||
      filters.category.length > 0 ||
      filters.division.length > 0 ||
      filters.dateRange.start ||
      filters.dateRange.end ||
      filters.createdBy ||
      filters.recipient ||
      filters.trackingNumber ||
      filters.fileType.length > 0 ||
      filters.priority.length > 0
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Basic Search */}
      <div className="p-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search documents, tracking numbers, titles..."
              value={filters.query}
              onChange={(e) => handleFilterChange('query', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={`flex items-center space-x-2 px-4 py-2 border rounded-lg transition-colors ${
              isExpanded || hasActiveFilters()
                ? 'border-blue-500 text-blue-600 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300'
            }`}
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            {hasActiveFilters() && (
              <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-1">
                {Object.values(filters).flat().filter(Boolean).length}
              </span>
            )}
          </button>
          <button
            onClick={handleSearch}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Search
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 space-y-4">
          {/* Status and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              <div className="flex flex-wrap gap-2">
                {statusOptions.map(status => (
                  <button
                    key={status.value}
                    onClick={() => handleArrayFilterChange('status', status.value)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      filters.status.includes(status.value)
                        ? status.color
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {status.label}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category
              </label>
              <div className="flex flex-wrap gap-2">
                {categoryOptions.map(category => (
                  <button
                    key={category}
                    onClick={() => handleArrayFilterChange('category', category)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      filters.category.includes(category)
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Division and Priority */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Division
              </label>
              <div className="space-y-2">
                {divisionOptions.map(division => (
                  <label key={division.value} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.division.includes(division.value)}
                      onChange={() => handleArrayFilterChange('division', division.value)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                      {division.label}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Priority
              </label>
              <div className="flex flex-wrap gap-2">
                {priorityOptions.map(priority => (
                  <button
                    key={priority.value}
                    onClick={() => handleArrayFilterChange('priority', priority.value)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      filters.priority.includes(priority.value)
                        ? priority.color
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {priority.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date Range
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <input
                  type="date"
                  value={filters.dateRange.start}
                  onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, start: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Start date"
                />
              </div>
              <div>
                <input
                  type="date"
                  value={filters.dateRange.end}
                  onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, end: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="End date"
                />
              </div>
            </div>
          </div>

          {/* Users and Tracking */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Created By
              </label>
              <select
                value={filters.createdBy}
                onChange={(e) => handleFilterChange('createdBy', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All users</option>
                {users.map(user => (
                  <option key={user._id} value={user._id}>
                    {user.name} ({user.division})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Recipient
              </label>
              <select
                value={filters.recipient}
                onChange={(e) => handleFilterChange('recipient', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All recipients</option>
                {users.map(user => (
                  <option key={user._id} value={user._id}>
                    {user.name} ({user.division})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tracking Number
              </label>
              <input
                type="text"
                value={filters.trackingNumber}
                onChange={(e) => handleFilterChange('trackingNumber', e.target.value)}
                placeholder="MGBR2-YYYY-NNNN-NNNN"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          {/* File Types */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              File Types
            </label>
            <div className="flex flex-wrap gap-2">
              {fileTypeOptions.map(fileType => (
                <button
                  key={fileType}
                  onClick={() => handleArrayFilterChange('fileType', fileType)}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    filters.fileType.includes(fileType)
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                  }`}
                >
                  .{fileType}
                </button>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handleClear}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors"
            >
              <X className="w-4 h-4" />
              <span>Clear all filters</span>
            </button>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsExpanded(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSearch}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
