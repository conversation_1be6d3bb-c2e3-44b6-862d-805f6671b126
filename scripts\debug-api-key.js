/**
 * Detailed API Key Debugging Script
 * Tests the API key with different endpoints and provides detailed diagnostics
 */

require('dotenv').config({ path: '.env.local' });

console.log('🔍 Detailed API Key Diagnostic');
console.log('===============================');
console.log('');

const geminiApiKey = process.env.GEMINI_API_KEY;

if (!geminiApiKey) {
  console.log('❌ No API key found');
  process.exit(1);
}

console.log('📋 API Key Info:');
console.log(`   Length: ${geminiApiKey.length} characters`);
console.log(`   Starts with: ${geminiApiKey.substring(0, 10)}...`);
console.log(`   Ends with: ...${geminiApiKey.substring(geminiApiKey.length - 6)}`);
console.log('');

// Test different API endpoints
const endpoints = [
  {
    name: 'Gemini 1.5 Flash (v1)',
    url: 'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent'
  },
  {
    name: 'Gemini Pro (v1beta)',
    url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent'
  },
  {
    name: 'List Models',
    url: 'https://generativelanguage.googleapis.com/v1/models'
  }
];

async function testEndpoint(endpoint) {
  console.log(`🧪 Testing: ${endpoint.name}`);
  console.log(`   URL: ${endpoint.url}`);

  try {
    let response;

    if (endpoint.name === 'List Models') {
      // Simple GET request to list models
      response = await fetch(`${endpoint.url}?key=${geminiApiKey}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
    } else {
      // POST request for content generation
      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: 'Hello! Please respond with just "API test successful" to confirm this is working.'
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.1,
          topK: 1,
          topP: 0.1,
          maxOutputTokens: 50
        }
      };

      response = await fetch(`${endpoint.url}?key=${geminiApiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });
    }

    console.log(`   Status: ${response.status} ${response.statusText}`);

    if (response.ok) {
      const data = await response.json();

      if (endpoint.name === 'List Models') {
        console.log('   ✅ SUCCESS - API key is valid!');
        console.log(`   Available models: ${data.models ? data.models.length : 'Unknown'}`);
        if (data.models && data.models.length > 0) {
          const geminiModels = data.models.filter(m => m.name.includes('gemini'));
          console.log(`   Gemini models: ${geminiModels.length}`);
          geminiModels.slice(0, 3).forEach(model => {
            console.log(`     - ${model.name}`);
          });
        }
      } else {
        console.log('   ✅ SUCCESS - Content generation working!');
        if (data.candidates && data.candidates[0]) {
          const responseText = data.candidates[0].content.parts[0].text;
          console.log(`   Response: ${responseText}`);
        }
      }
      return true;
    } else {
      const errorText = await response.text();
      console.log('   ❌ FAILED');
      console.log(`   Error: ${errorText}`);

      // Parse error for more details
      try {
        const errorData = JSON.parse(errorText);
        if (errorData.error) {
          console.log(`   Error Code: ${errorData.error.code}`);
          console.log(`   Error Message: ${errorData.error.message}`);
          console.log(`   Error Status: ${errorData.error.status}`);
        }
      } catch (e) {
        // Error text is not JSON
      }

      return false;
    }
  } catch (error) {
    console.log('   ❌ NETWORK ERROR');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function runDetailedDiagnostic() {
  console.log('🔬 Testing API Key with Multiple Endpoints...');
  console.log('');

  let successCount = 0;

  for (const endpoint of endpoints) {
    const success = await testEndpoint(endpoint);
    if (success) successCount++;
    console.log('');
  }

  console.log('📊 Summary:');
  console.log(`   Successful tests: ${successCount}/${endpoints.length}`);
  console.log('');

  if (successCount === 0) {
    console.log('❌ API key appears to be invalid or expired');
    console.log('');
    console.log('🔧 Troubleshooting steps:');
    console.log('   1. Go to: https://makersuite.google.com/app/apikey');
    console.log('   2. Check if your API key is still active');
    console.log('   3. Create a new API key if needed');
    console.log('   4. Make sure you have API quota remaining');
    console.log('   5. Verify the Gemini API is enabled for your project');
  } else if (successCount < endpoints.length) {
    console.log('⚠️  API key works partially - some endpoints failed');
    console.log('   This might be normal - different endpoints have different requirements');
  } else {
    console.log('✅ API key is working perfectly!');
    console.log('   All AI features should be functional now');
  }
}

if (require.main === module) {
  runDetailedDiagnostic();
}

module.exports = { runDetailedDiagnostic };
