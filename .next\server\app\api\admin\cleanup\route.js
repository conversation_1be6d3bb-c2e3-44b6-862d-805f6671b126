"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/cleanup/route";
exports.ids = ["app/api/admin/cleanup/route"];
exports.modules = {

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcleanup%2Froute&page=%2Fapi%2Fadmin%2Fcleanup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcleanup%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcleanup%2Froute&page=%2Fapi%2Fadmin%2Fcleanup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcleanup%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Administrator_Desktop_DocumentTracker_src_app_api_admin_cleanup_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/cleanup/route.ts */ \"(rsc)/./src/app/api/admin/cleanup/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/cleanup/route\",\n        pathname: \"/api/admin/cleanup\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/cleanup/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\app\\\\api\\\\admin\\\\cleanup\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Administrator_Desktop_DocumentTracker_src_app_api_admin_cleanup_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/cleanup/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcleanup%2Froute&page=%2Fapi%2Fadmin%2Fcleanup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcleanup%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/cleanup/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/admin/cleanup/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_auth_options__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth/options */ \"(rsc)/./src/lib/auth/options.ts\");\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n/* harmony import */ var _models_Document__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/Document */ \"(rsc)/./src/models/Document.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n/* harmony import */ var _models_AuditLog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/models/AuditLog */ \"(rsc)/./src/models/AuditLog.ts\");\n/* harmony import */ var _models_Notification__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/models/Notification */ \"(rsc)/./src/models/Notification.ts\");\n/* harmony import */ var _models_Feedback__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/models/Feedback */ \"(rsc)/./src/models/Feedback.ts\");\n/* harmony import */ var _models_Counter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/models/Counter */ \"(rsc)/./src/models/Counter.ts\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_11__);\n\n\n\n\n\n\n\n\n\n\n\n\n// Connect to database\nasync function connectDB() {\n    try {\n        await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        return true;\n    } catch (error) {\n        console.error(\"Database connection error:\", error);\n        return false;\n    }\n}\nasync function POST(req) {\n    try {\n        // Check authentication\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth_options__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session || !session.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        const user = session.user;\n        if (user.role !== _types__WEBPACK_IMPORTED_MODULE_5__.UserRole.ADMIN && user.role !== _types__WEBPACK_IMPORTED_MODULE_5__.UserRole.REGIONAL_DIRECTOR) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden: Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Connect to database\n        const connected = await connectDB();\n        if (!connected) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 500\n            });\n        }\n        // Parse request body\n        const body = await req.json();\n        const { cleanupType, userId, division, cleanDocuments = false, cleanNotifications = false, cleanStatistics = false, cleanAuditLogs = false, cleanFeedback = false } = body;\n        // Validate cleanup type\n        if (!cleanupType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Cleanup type is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Initialize results object\n        const results = {\n            success: true,\n            documentsDeleted: 0,\n            notificationsDeleted: 0,\n            auditLogsDeleted: 0,\n            feedbackDeleted: 0,\n            counterReset: false\n        };\n        // Create filter based on cleanup type\n        let filter = {};\n        if (cleanupType === \"user\" && userId) {\n            // Validate userId\n            if (!mongoose__WEBPACK_IMPORTED_MODULE_11___default().Types.ObjectId.isValid(userId)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid user ID\"\n                }, {\n                    status: 400\n                });\n            }\n            filter.createdBy = userId;\n        } else if (cleanupType === \"division\" && division) {\n            // Validate division\n            if (!Object.values(_types__WEBPACK_IMPORTED_MODULE_5__.Division).includes(division)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid division\"\n                }, {\n                    status: 400\n                });\n            }\n            filter.currentLocation = division;\n        } else if (cleanupType !== \"all\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid cleanup type or missing parameters\"\n            }, {\n                status: 400\n            });\n        }\n        // Perform cleanup operations\n        if (cleanDocuments) {\n            const deleteResult = await _models_Document__WEBPACK_IMPORTED_MODULE_4__[\"default\"].deleteMany(filter);\n            results.documentsDeleted = deleteResult.deletedCount;\n            // Reset the counter if all documents are being deleted\n            if (cleanupType === \"all\") {\n                try {\n                    const currentYear = new Date().getFullYear();\n                    // Reset the counter for the current year\n                    await _models_Counter__WEBPACK_IMPORTED_MODULE_10__[\"default\"].findOneAndUpdate({\n                        name: \"document_tracking_number\",\n                        year: currentYear\n                    }, {\n                        $set: {\n                            sequence: 0\n                        }\n                    }, {\n                        upsert: true\n                    });\n                    results.counterReset = true;\n                    console.log(`Reset document tracking number counter for year ${currentYear}`);\n                } catch (error) {\n                    console.error(\"Error resetting counter:\", error);\n                }\n            }\n        }\n        if (cleanNotifications) {\n            let notificationFilter = {};\n            if (cleanupType === \"user\" && userId) {\n                notificationFilter = {\n                    userId\n                };\n            }\n            const deleteResult = await _models_Notification__WEBPACK_IMPORTED_MODULE_8__[\"default\"].deleteMany(notificationFilter);\n            results.notificationsDeleted = deleteResult.deletedCount;\n        }\n        if (cleanAuditLogs) {\n            let auditLogFilter = {};\n            if (cleanupType === \"user\" && userId) {\n                auditLogFilter = {\n                    userId\n                };\n            }\n            const deleteResult = await _models_AuditLog__WEBPACK_IMPORTED_MODULE_7__[\"default\"].deleteMany(auditLogFilter);\n            results.auditLogsDeleted = deleteResult.deletedCount;\n        }\n        if (cleanFeedback) {\n            let feedbackFilter = {};\n            if (cleanupType === \"user\" && userId) {\n                feedbackFilter = {\n                    userId\n                };\n            }\n            const deleteResult = await _models_Feedback__WEBPACK_IMPORTED_MODULE_9__[\"default\"].deleteMany(feedbackFilter);\n            results.feedbackDeleted = deleteResult.deletedCount;\n        }\n        // Log the cleanup action\n        await _models_AuditLog__WEBPACK_IMPORTED_MODULE_7__[\"default\"].create({\n            action: _types_audit__WEBPACK_IMPORTED_MODULE_6__.AuditLogAction.DATA_CLEANUP,\n            performedBy: user.id,\n            targetType: cleanupType === \"user\" ? \"User\" : cleanupType === \"division\" ? \"Division\" : \"System\",\n            targetId: userId || division || \"all\",\n            details: {\n                cleanupType,\n                userId,\n                division,\n                results\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(results);\n    } catch (error) {\n        console.error(\"Error in cleanup API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hZG1pbi9jbGVhbnVwL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdEO0FBQ047QUFDRDtBQUNSO0FBQ0E7QUFFSTtBQUNFO0FBQ047QUFDUTtBQUNSO0FBQ0Y7QUFDUDtBQUVoQyxzQkFBc0I7QUFDdEIsZUFBZWE7SUFDYixJQUFJO1FBQ0YsTUFBTVYsMkRBQVNBO1FBQ2YsT0FBTztJQUNULEVBQUUsT0FBT1csT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtRQUM1QyxPQUFPO0lBQ1Q7QUFDRjtBQUVPLGVBQWVFLEtBQUtDLEdBQWdCO0lBQ3pDLElBQUk7UUFDRix1QkFBdUI7UUFDdkIsTUFBTUMsVUFBVSxNQUFNakIsZ0VBQWdCQSxDQUFDQywwREFBV0E7UUFDbEQsSUFBSSxDQUFDZ0IsV0FBVyxDQUFDQSxRQUFRQyxJQUFJLEVBQUU7WUFDN0IsT0FBT25CLHFEQUFZQSxDQUFDb0IsSUFBSSxDQUFDO2dCQUFFTixPQUFPO1lBQWUsR0FBRztnQkFBRU8sUUFBUTtZQUFJO1FBQ3BFO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU1GLE9BQU9ELFFBQVFDLElBQUk7UUFDekIsSUFBSUEsS0FBS0csSUFBSSxLQUFLakIsNENBQVFBLENBQUNrQixLQUFLLElBQUlKLEtBQUtHLElBQUksS0FBS2pCLDRDQUFRQSxDQUFDbUIsaUJBQWlCLEVBQUU7WUFDNUUsT0FBT3hCLHFEQUFZQSxDQUFDb0IsSUFBSSxDQUFDO2dCQUFFTixPQUFPO1lBQW1DLEdBQUc7Z0JBQUVPLFFBQVE7WUFBSTtRQUN4RjtRQUVBLHNCQUFzQjtRQUN0QixNQUFNSSxZQUFZLE1BQU1aO1FBQ3hCLElBQUksQ0FBQ1ksV0FBVztZQUNkLE9BQU96QixxREFBWUEsQ0FBQ29CLElBQUksQ0FBQztnQkFBRU4sT0FBTztZQUE2QixHQUFHO2dCQUFFTyxRQUFRO1lBQUk7UUFDbEY7UUFFQSxxQkFBcUI7UUFDckIsTUFBTUssT0FBTyxNQUFNVCxJQUFJRyxJQUFJO1FBQzNCLE1BQU0sRUFDSk8sV0FBVyxFQUNYQyxNQUFNLEVBQ05DLFFBQVEsRUFDUkMsaUJBQWlCLEtBQUssRUFDdEJDLHFCQUFxQixLQUFLLEVBQzFCQyxrQkFBa0IsS0FBSyxFQUN2QkMsaUJBQWlCLEtBQUssRUFDdEJDLGdCQUFnQixLQUFLLEVBQ3RCLEdBQUdSO1FBRUosd0JBQXdCO1FBQ3hCLElBQUksQ0FBQ0MsYUFBYTtZQUNoQixPQUFPM0IscURBQVlBLENBQUNvQixJQUFJLENBQUM7Z0JBQUVOLE9BQU87WUFBMkIsR0FBRztnQkFBRU8sUUFBUTtZQUFJO1FBQ2hGO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU1jLFVBQWU7WUFDbkJDLFNBQVM7WUFDVEMsa0JBQWtCO1lBQ2xCQyxzQkFBc0I7WUFDdEJDLGtCQUFrQjtZQUNsQkMsaUJBQWlCO1lBQ2pCQyxjQUFjO1FBQ2hCO1FBRUEsc0NBQXNDO1FBQ3RDLElBQUlDLFNBQWMsQ0FBQztRQUVuQixJQUFJZixnQkFBZ0IsVUFBVUMsUUFBUTtZQUNwQyxrQkFBa0I7WUFDbEIsSUFBSSxDQUFDaEIsc0RBQWMsQ0FBQ2dDLFFBQVEsQ0FBQ0MsT0FBTyxDQUFDakIsU0FBUztnQkFDNUMsT0FBTzVCLHFEQUFZQSxDQUFDb0IsSUFBSSxDQUFDO29CQUFFTixPQUFPO2dCQUFrQixHQUFHO29CQUFFTyxRQUFRO2dCQUFJO1lBQ3ZFO1lBQ0FxQixPQUFPSSxTQUFTLEdBQUdsQjtRQUNyQixPQUFPLElBQUlELGdCQUFnQixjQUFjRSxVQUFVO1lBQ2pELG9CQUFvQjtZQUNwQixJQUFJLENBQUNrQixPQUFPQyxNQUFNLENBQUMxQyw0Q0FBUUEsRUFBRTJDLFFBQVEsQ0FBQ3BCLFdBQXVCO2dCQUMzRCxPQUFPN0IscURBQVlBLENBQUNvQixJQUFJLENBQUM7b0JBQUVOLE9BQU87Z0JBQW1CLEdBQUc7b0JBQUVPLFFBQVE7Z0JBQUk7WUFDeEU7WUFDQXFCLE9BQU9RLGVBQWUsR0FBR3JCO1FBQzNCLE9BQU8sSUFBSUYsZ0JBQWdCLE9BQU87WUFDaEMsT0FBTzNCLHFEQUFZQSxDQUFDb0IsSUFBSSxDQUFDO2dCQUFFTixPQUFPO1lBQTZDLEdBQUc7Z0JBQUVPLFFBQVE7WUFBSTtRQUNsRztRQUVBLDZCQUE2QjtRQUM3QixJQUFJUyxnQkFBZ0I7WUFDbEIsTUFBTXFCLGVBQWUsTUFBTS9DLHdEQUFRQSxDQUFDZ0QsVUFBVSxDQUFDVjtZQUMvQ1AsUUFBUUUsZ0JBQWdCLEdBQUdjLGFBQWFFLFlBQVk7WUFFcEQsdURBQXVEO1lBQ3ZELElBQUkxQixnQkFBZ0IsT0FBTztnQkFDekIsSUFBSTtvQkFDRixNQUFNMkIsY0FBYyxJQUFJQyxPQUFPQyxXQUFXO29CQUMxQyx5Q0FBeUM7b0JBQ3pDLE1BQU03Qyx3REFBT0EsQ0FBQzhDLGdCQUFnQixDQUM1Qjt3QkFBRUMsTUFBTTt3QkFBNEJDLE1BQU1MO29CQUFZLEdBQ3REO3dCQUFFTSxNQUFNOzRCQUFFQyxVQUFVO3dCQUFFO29CQUFFLEdBQ3hCO3dCQUFFQyxRQUFRO29CQUFLO29CQUVqQjNCLFFBQVFNLFlBQVksR0FBRztvQkFDdkIxQixRQUFRZ0QsR0FBRyxDQUFDLENBQUMsZ0RBQWdELEVBQUVULFlBQVksQ0FBQztnQkFDOUUsRUFBRSxPQUFPeEMsT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7Z0JBQzVDO1lBQ0Y7UUFDRjtRQUVBLElBQUlpQixvQkFBb0I7WUFDdEIsSUFBSWlDLHFCQUFxQixDQUFDO1lBQzFCLElBQUlyQyxnQkFBZ0IsVUFBVUMsUUFBUTtnQkFDcENvQyxxQkFBcUI7b0JBQUVwQztnQkFBTztZQUNoQztZQUNBLE1BQU11QixlQUFlLE1BQU0xQyw0REFBWUEsQ0FBQzJDLFVBQVUsQ0FBQ1k7WUFDbkQ3QixRQUFRRyxvQkFBb0IsR0FBR2EsYUFBYUUsWUFBWTtRQUMxRDtRQUVBLElBQUlwQixnQkFBZ0I7WUFDbEIsSUFBSWdDLGlCQUFpQixDQUFDO1lBQ3RCLElBQUl0QyxnQkFBZ0IsVUFBVUMsUUFBUTtnQkFDcENxQyxpQkFBaUI7b0JBQUVyQztnQkFBTztZQUM1QjtZQUNBLE1BQU11QixlQUFlLE1BQU0zQyx3REFBUUEsQ0FBQzRDLFVBQVUsQ0FBQ2E7WUFDL0M5QixRQUFRSSxnQkFBZ0IsR0FBR1ksYUFBYUUsWUFBWTtRQUN0RDtRQUVBLElBQUluQixlQUFlO1lBQ2pCLElBQUlnQyxpQkFBaUIsQ0FBQztZQUN0QixJQUFJdkMsZ0JBQWdCLFVBQVVDLFFBQVE7Z0JBQ3BDc0MsaUJBQWlCO29CQUFFdEM7Z0JBQU87WUFDNUI7WUFDQSxNQUFNdUIsZUFBZSxNQUFNekMsd0RBQVFBLENBQUMwQyxVQUFVLENBQUNjO1lBQy9DL0IsUUFBUUssZUFBZSxHQUFHVyxhQUFhRSxZQUFZO1FBQ3JEO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU03Qyx3REFBUUEsQ0FBQzJELE1BQU0sQ0FBQztZQUNwQkMsUUFBUTdELHdEQUFjQSxDQUFDOEQsWUFBWTtZQUNuQ0MsYUFBYW5ELEtBQUtvRCxFQUFFO1lBQ3BCQyxZQUFZN0MsZ0JBQWdCLFNBQVMsU0FBU0EsZ0JBQWdCLGFBQWEsYUFBYTtZQUN4RjhDLFVBQVU3QyxVQUFVQyxZQUFZO1lBQ2hDNkMsU0FBUztnQkFDUC9DO2dCQUNBQztnQkFDQUM7Z0JBQ0FNO1lBQ0Y7UUFDRjtRQUVBLE9BQU9uQyxxREFBWUEsQ0FBQ29CLElBQUksQ0FBQ2U7SUFDM0IsRUFBRSxPQUFPckIsT0FBWTtRQUNuQkMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBT2QscURBQVlBLENBQUNvQixJQUFJLENBQUM7WUFBRU4sT0FBT0EsTUFBTTZELE9BQU8sSUFBSTtRQUF3QixHQUFHO1lBQUV0RCxRQUFRO1FBQUk7SUFDOUY7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2RvY3VtZW50LXRyYWNrZXIvLi9zcmMvYXBwL2FwaS9hZG1pbi9jbGVhbnVwL3JvdXRlLnRzPzFjYWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvbmV4dCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgvb3B0aW9ucyc7XG5pbXBvcnQgZGJDb25uZWN0IGZyb20gJ0AvbGliL2RiL21vbmdvZGInO1xuaW1wb3J0IERvY3VtZW50IGZyb20gJ0AvbW9kZWxzL0RvY3VtZW50JztcbmltcG9ydCBVc2VyIGZyb20gJ0AvbW9kZWxzL1VzZXInO1xuaW1wb3J0IHsgVXNlclJvbGUsIERpdmlzaW9uIH0gZnJvbSAnQC90eXBlcyc7XG5pbXBvcnQgeyBBdWRpdExvZ0FjdGlvbiB9IGZyb20gJ0AvdHlwZXMvYXVkaXQnO1xuaW1wb3J0IEF1ZGl0TG9nIGZyb20gJ0AvbW9kZWxzL0F1ZGl0TG9nJztcbmltcG9ydCBOb3RpZmljYXRpb24gZnJvbSAnQC9tb2RlbHMvTm90aWZpY2F0aW9uJztcbmltcG9ydCBGZWVkYmFjayBmcm9tICdAL21vZGVscy9GZWVkYmFjayc7XG5pbXBvcnQgQ291bnRlciBmcm9tICdAL21vZGVscy9Db3VudGVyJztcbmltcG9ydCBtb25nb29zZSBmcm9tICdtb25nb29zZSc7XG5cbi8vIENvbm5lY3QgdG8gZGF0YWJhc2VcbmFzeW5jIGZ1bmN0aW9uIGNvbm5lY3REQigpIHtcbiAgdHJ5IHtcbiAgICBhd2FpdCBkYkNvbm5lY3QoKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdEYXRhYmFzZSBjb25uZWN0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxOiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIC8vIENoZWNrIGF1dGhlbnRpY2F0aW9uXG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpO1xuICAgIGlmICghc2Vzc2lvbiB8fCAhc2Vzc2lvbi51c2VyKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfSwgeyBzdGF0dXM6IDQwMSB9KTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGFkbWluXG4gICAgY29uc3QgdXNlciA9IHNlc3Npb24udXNlciBhcyBhbnk7XG4gICAgaWYgKHVzZXIucm9sZSAhPT0gVXNlclJvbGUuQURNSU4gJiYgdXNlci5yb2xlICE9PSBVc2VyUm9sZS5SRUdJT05BTF9ESVJFQ1RPUikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdGb3JiaWRkZW46IEFkbWluIGFjY2VzcyByZXF1aXJlZCcgfSwgeyBzdGF0dXM6IDQwMyB9KTtcbiAgICB9XG5cbiAgICAvLyBDb25uZWN0IHRvIGRhdGFiYXNlXG4gICAgY29uc3QgY29ubmVjdGVkID0gYXdhaXQgY29ubmVjdERCKCk7XG4gICAgaWYgKCFjb25uZWN0ZWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnRGF0YWJhc2UgY29ubmVjdGlvbiBmYWlsZWQnIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gICAgfVxuXG4gICAgLy8gUGFyc2UgcmVxdWVzdCBib2R5XG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcS5qc29uKCk7XG4gICAgY29uc3Qge1xuICAgICAgY2xlYW51cFR5cGUsXG4gICAgICB1c2VySWQsXG4gICAgICBkaXZpc2lvbixcbiAgICAgIGNsZWFuRG9jdW1lbnRzID0gZmFsc2UsXG4gICAgICBjbGVhbk5vdGlmaWNhdGlvbnMgPSBmYWxzZSxcbiAgICAgIGNsZWFuU3RhdGlzdGljcyA9IGZhbHNlLFxuICAgICAgY2xlYW5BdWRpdExvZ3MgPSBmYWxzZSxcbiAgICAgIGNsZWFuRmVlZGJhY2sgPSBmYWxzZVxuICAgIH0gPSBib2R5O1xuXG4gICAgLy8gVmFsaWRhdGUgY2xlYW51cCB0eXBlXG4gICAgaWYgKCFjbGVhbnVwVHlwZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdDbGVhbnVwIHR5cGUgaXMgcmVxdWlyZWQnIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuXG4gICAgLy8gSW5pdGlhbGl6ZSByZXN1bHRzIG9iamVjdFxuICAgIGNvbnN0IHJlc3VsdHM6IGFueSA9IHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkb2N1bWVudHNEZWxldGVkOiAwLFxuICAgICAgbm90aWZpY2F0aW9uc0RlbGV0ZWQ6IDAsXG4gICAgICBhdWRpdExvZ3NEZWxldGVkOiAwLFxuICAgICAgZmVlZGJhY2tEZWxldGVkOiAwLFxuICAgICAgY291bnRlclJlc2V0OiBmYWxzZVxuICAgIH07XG5cbiAgICAvLyBDcmVhdGUgZmlsdGVyIGJhc2VkIG9uIGNsZWFudXAgdHlwZVxuICAgIGxldCBmaWx0ZXI6IGFueSA9IHt9O1xuXG4gICAgaWYgKGNsZWFudXBUeXBlID09PSAndXNlcicgJiYgdXNlcklkKSB7XG4gICAgICAvLyBWYWxpZGF0ZSB1c2VySWRcbiAgICAgIGlmICghbW9uZ29vc2UuVHlwZXMuT2JqZWN0SWQuaXNWYWxpZCh1c2VySWQpKSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnSW52YWxpZCB1c2VyIElEJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgICAgfVxuICAgICAgZmlsdGVyLmNyZWF0ZWRCeSA9IHVzZXJJZDtcbiAgICB9IGVsc2UgaWYgKGNsZWFudXBUeXBlID09PSAnZGl2aXNpb24nICYmIGRpdmlzaW9uKSB7XG4gICAgICAvLyBWYWxpZGF0ZSBkaXZpc2lvblxuICAgICAgaWYgKCFPYmplY3QudmFsdWVzKERpdmlzaW9uKS5pbmNsdWRlcyhkaXZpc2lvbiBhcyBEaXZpc2lvbikpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdJbnZhbGlkIGRpdmlzaW9uJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgICAgfVxuICAgICAgZmlsdGVyLmN1cnJlbnRMb2NhdGlvbiA9IGRpdmlzaW9uO1xuICAgIH0gZWxzZSBpZiAoY2xlYW51cFR5cGUgIT09ICdhbGwnKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0ludmFsaWQgY2xlYW51cCB0eXBlIG9yIG1pc3NpbmcgcGFyYW1ldGVycycgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICAvLyBQZXJmb3JtIGNsZWFudXAgb3BlcmF0aW9uc1xuICAgIGlmIChjbGVhbkRvY3VtZW50cykge1xuICAgICAgY29uc3QgZGVsZXRlUmVzdWx0ID0gYXdhaXQgRG9jdW1lbnQuZGVsZXRlTWFueShmaWx0ZXIpO1xuICAgICAgcmVzdWx0cy5kb2N1bWVudHNEZWxldGVkID0gZGVsZXRlUmVzdWx0LmRlbGV0ZWRDb3VudDtcblxuICAgICAgLy8gUmVzZXQgdGhlIGNvdW50ZXIgaWYgYWxsIGRvY3VtZW50cyBhcmUgYmVpbmcgZGVsZXRlZFxuICAgICAgaWYgKGNsZWFudXBUeXBlID09PSAnYWxsJykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpO1xuICAgICAgICAgIC8vIFJlc2V0IHRoZSBjb3VudGVyIGZvciB0aGUgY3VycmVudCB5ZWFyXG4gICAgICAgICAgYXdhaXQgQ291bnRlci5maW5kT25lQW5kVXBkYXRlKFxuICAgICAgICAgICAgeyBuYW1lOiAnZG9jdW1lbnRfdHJhY2tpbmdfbnVtYmVyJywgeWVhcjogY3VycmVudFllYXIgfSxcbiAgICAgICAgICAgIHsgJHNldDogeyBzZXF1ZW5jZTogMCB9IH0sXG4gICAgICAgICAgICB7IHVwc2VydDogdHJ1ZSB9XG4gICAgICAgICAgKTtcbiAgICAgICAgICByZXN1bHRzLmNvdW50ZXJSZXNldCA9IHRydWU7XG4gICAgICAgICAgY29uc29sZS5sb2coYFJlc2V0IGRvY3VtZW50IHRyYWNraW5nIG51bWJlciBjb3VudGVyIGZvciB5ZWFyICR7Y3VycmVudFllYXJ9YCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVzZXR0aW5nIGNvdW50ZXI6JywgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGNsZWFuTm90aWZpY2F0aW9ucykge1xuICAgICAgbGV0IG5vdGlmaWNhdGlvbkZpbHRlciA9IHt9O1xuICAgICAgaWYgKGNsZWFudXBUeXBlID09PSAndXNlcicgJiYgdXNlcklkKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbkZpbHRlciA9IHsgdXNlcklkIH07XG4gICAgICB9XG4gICAgICBjb25zdCBkZWxldGVSZXN1bHQgPSBhd2FpdCBOb3RpZmljYXRpb24uZGVsZXRlTWFueShub3RpZmljYXRpb25GaWx0ZXIpO1xuICAgICAgcmVzdWx0cy5ub3RpZmljYXRpb25zRGVsZXRlZCA9IGRlbGV0ZVJlc3VsdC5kZWxldGVkQ291bnQ7XG4gICAgfVxuXG4gICAgaWYgKGNsZWFuQXVkaXRMb2dzKSB7XG4gICAgICBsZXQgYXVkaXRMb2dGaWx0ZXIgPSB7fTtcbiAgICAgIGlmIChjbGVhbnVwVHlwZSA9PT0gJ3VzZXInICYmIHVzZXJJZCkge1xuICAgICAgICBhdWRpdExvZ0ZpbHRlciA9IHsgdXNlcklkIH07XG4gICAgICB9XG4gICAgICBjb25zdCBkZWxldGVSZXN1bHQgPSBhd2FpdCBBdWRpdExvZy5kZWxldGVNYW55KGF1ZGl0TG9nRmlsdGVyKTtcbiAgICAgIHJlc3VsdHMuYXVkaXRMb2dzRGVsZXRlZCA9IGRlbGV0ZVJlc3VsdC5kZWxldGVkQ291bnQ7XG4gICAgfVxuXG4gICAgaWYgKGNsZWFuRmVlZGJhY2spIHtcbiAgICAgIGxldCBmZWVkYmFja0ZpbHRlciA9IHt9O1xuICAgICAgaWYgKGNsZWFudXBUeXBlID09PSAndXNlcicgJiYgdXNlcklkKSB7XG4gICAgICAgIGZlZWRiYWNrRmlsdGVyID0geyB1c2VySWQgfTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGRlbGV0ZVJlc3VsdCA9IGF3YWl0IEZlZWRiYWNrLmRlbGV0ZU1hbnkoZmVlZGJhY2tGaWx0ZXIpO1xuICAgICAgcmVzdWx0cy5mZWVkYmFja0RlbGV0ZWQgPSBkZWxldGVSZXN1bHQuZGVsZXRlZENvdW50O1xuICAgIH1cblxuICAgIC8vIExvZyB0aGUgY2xlYW51cCBhY3Rpb25cbiAgICBhd2FpdCBBdWRpdExvZy5jcmVhdGUoe1xuICAgICAgYWN0aW9uOiBBdWRpdExvZ0FjdGlvbi5EQVRBX0NMRUFOVVAsXG4gICAgICBwZXJmb3JtZWRCeTogdXNlci5pZCxcbiAgICAgIHRhcmdldFR5cGU6IGNsZWFudXBUeXBlID09PSAndXNlcicgPyAnVXNlcicgOiBjbGVhbnVwVHlwZSA9PT0gJ2RpdmlzaW9uJyA/ICdEaXZpc2lvbicgOiAnU3lzdGVtJyxcbiAgICAgIHRhcmdldElkOiB1c2VySWQgfHwgZGl2aXNpb24gfHwgJ2FsbCcsXG4gICAgICBkZXRhaWxzOiB7XG4gICAgICAgIGNsZWFudXBUeXBlLFxuICAgICAgICB1c2VySWQsXG4gICAgICAgIGRpdmlzaW9uLFxuICAgICAgICByZXN1bHRzXG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24ocmVzdWx0cyk7XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBjbGVhbnVwIEFQSTonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgJ0ludGVybmFsIHNlcnZlciBlcnJvcicgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImdldFNlcnZlclNlc3Npb24iLCJhdXRoT3B0aW9ucyIsImRiQ29ubmVjdCIsIkRvY3VtZW50IiwiVXNlclJvbGUiLCJEaXZpc2lvbiIsIkF1ZGl0TG9nQWN0aW9uIiwiQXVkaXRMb2ciLCJOb3RpZmljYXRpb24iLCJGZWVkYmFjayIsIkNvdW50ZXIiLCJtb25nb29zZSIsImNvbm5lY3REQiIsImVycm9yIiwiY29uc29sZSIsIlBPU1QiLCJyZXEiLCJzZXNzaW9uIiwidXNlciIsImpzb24iLCJzdGF0dXMiLCJyb2xlIiwiQURNSU4iLCJSRUdJT05BTF9ESVJFQ1RPUiIsImNvbm5lY3RlZCIsImJvZHkiLCJjbGVhbnVwVHlwZSIsInVzZXJJZCIsImRpdmlzaW9uIiwiY2xlYW5Eb2N1bWVudHMiLCJjbGVhbk5vdGlmaWNhdGlvbnMiLCJjbGVhblN0YXRpc3RpY3MiLCJjbGVhbkF1ZGl0TG9ncyIsImNsZWFuRmVlZGJhY2siLCJyZXN1bHRzIiwic3VjY2VzcyIsImRvY3VtZW50c0RlbGV0ZWQiLCJub3RpZmljYXRpb25zRGVsZXRlZCIsImF1ZGl0TG9nc0RlbGV0ZWQiLCJmZWVkYmFja0RlbGV0ZWQiLCJjb3VudGVyUmVzZXQiLCJmaWx0ZXIiLCJUeXBlcyIsIk9iamVjdElkIiwiaXNWYWxpZCIsImNyZWF0ZWRCeSIsIk9iamVjdCIsInZhbHVlcyIsImluY2x1ZGVzIiwiY3VycmVudExvY2F0aW9uIiwiZGVsZXRlUmVzdWx0IiwiZGVsZXRlTWFueSIsImRlbGV0ZWRDb3VudCIsImN1cnJlbnRZZWFyIiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwiZmluZE9uZUFuZFVwZGF0ZSIsIm5hbWUiLCJ5ZWFyIiwiJHNldCIsInNlcXVlbmNlIiwidXBzZXJ0IiwibG9nIiwibm90aWZpY2F0aW9uRmlsdGVyIiwiYXVkaXRMb2dGaWx0ZXIiLCJmZWVkYmFja0ZpbHRlciIsImNyZWF0ZSIsImFjdGlvbiIsIkRBVEFfQ0xFQU5VUCIsInBlcmZvcm1lZEJ5IiwiaWQiLCJ0YXJnZXRUeXBlIiwidGFyZ2V0SWQiLCJkZXRhaWxzIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/cleanup/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/options.ts":
/*!*********************************!*\
  !*** ./src/lib/auth/options.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _utils_audit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/audit */ \"(rsc)/./src/utils/audit.ts\");\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n/* harmony import */ var _utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/serverTimestamp */ \"(rsc)/./src/utils/serverTimestamp.ts\");\n/* harmony import */ var _utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/sessionToken */ \"(rsc)/./src/utils/sessionToken.ts\");\n\n\n\n\n\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            id: \"credentials\",\n            name: \"Credentials\",\n            // Define the credentials that will be submitted from the login form\n            credentials: {\n                name: {\n                    label: \"Name\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials, req) {\n                // Add more detailed logging of received credentials\n                console.log(\"Received credentials:\", credentials);\n                if (!credentials?.name || !credentials?.password) {\n                    console.error(\"Missing credentials:\", {\n                        name: !!credentials?.name,\n                        password: !!credentials?.password\n                    });\n                    throw new Error(\"Name and password required\");\n                }\n                try {\n                    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                    console.log(\"Looking up user with name:\", credentials.name);\n                    // Make the search case-insensitive and escape special characters in the regex\n                    const escapedName = credentials.name.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n                        name: {\n                            $regex: new RegExp(`^${escapedName}$`, \"i\")\n                        }\n                    }).select(\"+password\");\n                    if (!user) {\n                        console.log(\"User not found with name:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"User found, checking password\");\n                    if (!user.password) {\n                        console.log(\"User has no password set\");\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    const isPasswordCorrect = await (0,bcrypt__WEBPACK_IMPORTED_MODULE_1__.compare)(credentials.password, user.password);\n                    if (!isPasswordCorrect) {\n                        console.log(\"Password incorrect for user:\", credentials.name);\n                        throw new Error(\"Invalid name or password\");\n                    }\n                    console.log(\"Authentication successful for user:\", credentials.name);\n                    const userId = user._id?.toString() || \"\";\n                    // Create a new session token\n                    const userAgent = req?.headers?.[\"user-agent\"];\n                    const ipAddress = req?.headers?.[\"x-forwarded-for\"] || req?.socket?.remoteAddress || \"unknown\";\n                    const sessionToken = await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__.createSession)(userId, userAgent, ipAddress);\n                    // Log successful login\n                    await (0,_utils_audit__WEBPACK_IMPORTED_MODULE_4__.logAuditEvent)({\n                        action: _types_audit__WEBPACK_IMPORTED_MODULE_5__.AuditLogAction.USER_LOGIN,\n                        performedBy: userId,\n                        targetId: userId,\n                        targetType: \"User\",\n                        details: {\n                            name: user.name,\n                            email: user.email,\n                            role: user.role,\n                            division: user.division,\n                            sessionToken: sessionToken\n                        }\n                    });\n                    return {\n                        id: userId,\n                        name: user.name,\n                        email: user.email,\n                        role: user.role,\n                        division: user.division,\n                        image: user.image,\n                        sessionToken: sessionToken\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    throw new Error(\"Authentication failed. Please try again.\");\n                }\n                // This code is unreachable due to the try/catch block above\n                // but we'll keep it as a fallback\n                console.error(\"Warning: Reached unreachable code in NextAuth authorize callback\");\n                return null;\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async jwt ({ token, user, trigger }) {\n            console.log(\"JWT callback called with user:\", user);\n            console.log(\"Initial token:\", token);\n            if (user) {\n                // Add user data to token\n                token.id = user.id;\n                // Cast user to any to access custom properties\n                const customUser = user;\n                token.name = customUser.name; // Ensure name is included\n                token.role = customUser.role;\n                token.division = customUser.division;\n                // Add session token to JWT\n                if (customUser.sessionToken) {\n                    token.sessionToken = customUser.sessionToken;\n                }\n                // Add server timestamp to token to invalidate sessions on server restart\n                token.serverTimestamp = (0,_utils_serverTimestamp__WEBPACK_IMPORTED_MODULE_6__.getServerTimestamp)();\n                console.log(\"Updated token with user data:\", token);\n            }\n            // Handle sign out\n            if (trigger === \"signOut\") {\n                // Remove the session from the database when the user signs out\n                if (token.id && token.sessionToken) {\n                    await (0,_utils_sessionToken__WEBPACK_IMPORTED_MODULE_7__.removeSession)(token.id, token.sessionToken);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"Session callback called with token:\", token);\n            console.log(\"Initial session:\", session);\n            if (token) {\n                // Add user data to session\n                session.user.id = token.id;\n                session.user.name = token.name; // Ensure name is included\n                session.user.role = token.role;\n                session.user.division = token.division;\n                // Add server timestamp to session\n                session.serverTimestamp = token.serverTimestamp;\n                // Add session token to session\n                if (token.sessionToken) {\n                    session.sessionToken = token.sessionToken;\n                }\n            }\n            console.log(\"Returning session:\", session);\n            return session;\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 24 * 60 * 60\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/options.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/mongodb.ts":
/*!*******************************!*\
  !*** ./src/lib/db/mongodb.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isConnectionHealthy: () => (/* binding */ isConnectionHealthy)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _register_models__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./register-models */ \"(rsc)/./src/lib/db/register-models.ts\");\n\n\nconst MONGODB_URI = process.env.MONGODB_URI || \"mongodb://localhost:27017/document-tracker\";\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable\");\n}\n// Connection options\nconst connectionOptions = {\n    bufferCommands: false,\n    serverSelectionTimeoutMS: 10000,\n    socketTimeoutMS: 45000,\n    family: 4,\n    // Enable auto reconnect\n    autoIndex: true,\n    autoCreate: true,\n    // Connection health monitoring\n    heartbeatFrequencyMS: 10000\n};\n// Initialize cached connection object\nlet cached = global.mongoose || {\n    conn: null,\n    promise: null,\n    isConnecting: false,\n    lastConnectionAttempt: 0\n};\nif (!global.mongoose) {\n    global.mongoose = cached;\n}\n// Set up connection event listeners\nfunction setupConnectionMonitoring() {\n    // Only set up listeners once\n    if (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.listenerCount(\"connected\") > 0) return;\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"connected\", ()=>{\n        console.log(\"MongoDB connection established successfully\");\n    });\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"error\", (err)=>{\n        console.error(\"MongoDB connection error:\", err);\n    });\n    mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"disconnected\", ()=>{\n        console.warn(\"MongoDB disconnected. Will attempt to reconnect automatically.\");\n    });\n    // Handle process termination\n    process.on(\"SIGINT\", async ()=>{\n        try {\n            await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.close();\n            console.log(\"MongoDB connection closed due to application termination\");\n            process.exit(0);\n        } catch (err) {\n            console.error(\"Error closing MongoDB connection:\", err);\n            process.exit(1);\n        }\n    });\n}\n// Check if connection is healthy\nfunction isConnectionHealthy() {\n    return (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection).readyState === 1; // 1 = connected\n}\n// Main connection function\nasync function dbConnect() {\n    // Set up connection monitoring\n    setupConnectionMonitoring();\n    // If we already have a connection and it's healthy, return it\n    if (cached.conn && isConnectionHealthy()) {\n        console.log(\"Using existing MongoDB connection\");\n        return cached.conn;\n    }\n    console.log(\"No healthy MongoDB connection found, creating a new one\");\n    // If we're already trying to connect, wait for that promise\n    if (cached.isConnecting && cached.promise) {\n        try {\n            cached.conn = await cached.promise;\n            return cached.conn;\n        } catch (error) {\n            // If the current connection attempt fails, we'll try again below\n            console.error(\"Ongoing connection attempt failed:\", error);\n        }\n    }\n    // Prevent connection attempts in rapid succession (throttle to once per 5 seconds)\n    const now = Date.now();\n    const minTimeBetweenAttempts = 5000; // 5 seconds\n    if (now - cached.lastConnectionAttempt < minTimeBetweenAttempts) {\n        console.warn(\"Connection attempt throttled. Waiting before retrying...\");\n        await new Promise((resolve)=>setTimeout(resolve, minTimeBetweenAttempts));\n    }\n    // Start a new connection attempt\n    cached.isConnecting = true;\n    cached.lastConnectionAttempt = Date.now();\n    cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, connectionOptions).then((mongoose)=>{\n        console.log(\"MongoDB connected successfully\");\n        cached.isConnecting = false;\n        // Register models after successful connection\n        (0,_register_models__WEBPACK_IMPORTED_MODULE_1__.registerModels)();\n        return mongoose;\n    }).catch((error)=>{\n        console.error(\"MongoDB connection error:\", error);\n        cached.isConnecting = false;\n        cached.promise = null; // Reset the promise on error\n        throw error;\n    });\n    try {\n        cached.conn = await cached.promise;\n        return cached.conn;\n    } catch (error) {\n        console.error(\"Failed to connect to MongoDB:\", error);\n        // Implement exponential backoff for retries in production\n        if (false) {}\n        throw error;\n    }\n}\n// Export the connection function and health check\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/register-models.ts":
/*!***************************************!*\
  !*** ./src/lib/db/register-models.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerModels: () => (/* binding */ registerModels)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\n// Define the Document Journey Schema\nconst DocumentJourneySchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    action: {\n        type: String,\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    fromDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    toDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    byUser: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    timestamp: {\n        type: Date,\n        default: Date.now\n    },\n    notes: {\n        type: String\n    }\n});\n// Define the Document Schema\nconst DocumentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a title\"\n        ],\n        maxlength: [\n            100,\n            \"Title cannot be more than 100 characters\"\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a description\"\n        ]\n    },\n    category: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentCategory),\n        required: [\n            true,\n            \"Please provide a category\"\n        ]\n    },\n    status: {\n        type: String,\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX,\n        // Use a custom validator instead of enum to ensure it works properly with case-insensitivity\n        validate: {\n            validator: function(v) {\n                // Convert both to lowercase for case-insensitive comparison\n                return Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).map((s)=>s.toLowerCase()).includes(v.toLowerCase());\n            },\n            message: (props)=>`${props.value} is not a valid status. Valid statuses are: ${Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).join(\", \")}`\n        }\n    },\n    createdBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    currentLocation: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a current location\"\n        ]\n    },\n    recipientId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\"\n    },\n    relatedDocumentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Document\"\n    },\n    fileUrl: {\n        type: String\n    },\n    fileName: {\n        type: String\n    },\n    fileType: {\n        type: String\n    },\n    trackingNumber: {\n        type: String,\n        // Using sparse index to prevent duplicate key errors with null/undefined values\n        index: {\n            sparse: true\n        },\n        validate: {\n            validator: function(v) {\n                // Allow undefined or null values (will be set later)\n                if (!v) return true;\n                // Validate the format: MGBR2-YYYY-NNNN-NNNN\n                return typeof v === \"string\" && /^MGBR2-\\d{4}-\\d{4}-\\d{4}$/.test(v);\n            },\n            message: (props)=>`${props.value} is not a valid tracking number format. Expected format: MGBR2-YYYY-NNNN-NNNN`\n        }\n    },\n    isOriginal: {\n        type: Boolean,\n        default: false,\n        index: true\n    },\n    journey: [\n        DocumentJourneySchema\n    ]\n}, {\n    timestamps: true\n});\n// Register models\nfunction registerModels() {\n    // Only register models if they haven't been registered yet\n    if (!(mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Document) {\n        mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Document\", DocumentSchema);\n        console.log(\"Document model registered\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/register-models.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/AuditLog.ts":
/*!********************************!*\
  !*** ./src/models/AuditLog.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types_audit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/audit */ \"(rsc)/./src/types/audit.ts\");\n\n\nconst AuditLogSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    action: {\n        type: String,\n        enum: Object.values(_types_audit__WEBPACK_IMPORTED_MODULE_1__.AuditLogAction),\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    performedBy: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user who performed the action\"\n        ]\n    },\n    targetId: {\n        type: String\n    },\n    targetType: {\n        type: String\n    },\n    details: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.Mixed\n    },\n    ipAddress: {\n        type: String\n    },\n    userAgent: {\n        type: String\n    }\n}, {\n    timestamps: true\n});\n// Create a text index for searching\nAuditLogSchema.index({\n    action: \"text\",\n    targetType: \"text\"\n});\n// Fix for \"Cannot read properties of undefined (reading 'AuditLog')\" error\n// Check if the model exists in mongoose.models before trying to access it\nlet AuditLog;\ntry {\n    // Try to get the existing model\n    AuditLog = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AuditLog\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    AuditLog = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AuditLog\", AuditLogSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuditLog);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/AuditLog.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Counter.ts":
/*!*******************************!*\
  !*** ./src/models/Counter.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CounterSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a counter name\"\n        ],\n        enum: [\n            \"document_tracking_number\"\n        ],\n        default: \"document_tracking_number\"\n    },\n    year: {\n        type: Number,\n        required: [\n            true,\n            \"Please provide a year\"\n        ]\n    },\n    sequence: {\n        type: Number,\n        required: [\n            true,\n            \"Please provide a sequence number\"\n        ],\n        default: 0\n    }\n}, {\n    timestamps: true\n});\n// Create a compound index on name and year to ensure uniqueness\nCounterSchema.index({\n    name: 1,\n    year: 1\n}, {\n    unique: true\n});\n/**\n * Get the next sequence number for a counter\n * @param name The name of the counter\n * @param year The year for the counter\n * @returns The next sequence number\n */ CounterSchema.statics.getNextSequence = async function(name, year) {\n    const result = await this.findOneAndUpdate({\n        name,\n        year\n    }, {\n        $inc: {\n            sequence: 1\n        }\n    }, {\n        upsert: true,\n        new: true\n    });\n    return result.sequence;\n};\n// Fix for \"Cannot read properties of undefined\" error\nlet Counter;\ntry {\n    // Try to get the existing model\n    Counter = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Counter\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    Counter = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Counter\", CounterSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Counter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Counter.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Document.ts":
/*!********************************!*\
  !*** ./src/models/Document.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\nconst DocumentJourneySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    action: {\n        type: String,\n        required: [\n            true,\n            \"Please provide an action\"\n        ]\n    },\n    fromDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    toDivision: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division)\n    },\n    byUser: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    timestamp: {\n        type: Date,\n        default: Date.now\n    },\n    notes: {\n        type: String\n    }\n});\nconst DocumentSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a title\"\n        ],\n        maxlength: [\n            100,\n            \"Title cannot be more than 100 characters\"\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a description\"\n        ]\n    },\n    category: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentCategory),\n        required: [\n            true,\n            \"Please provide a category\"\n        ]\n    },\n    action: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentAction),\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentAction.NONE\n    },\n    status: {\n        type: String,\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.INBOX,\n        // Use a custom validator instead of enum to ensure it works properly\n        validate: {\n            validator: function(v) {\n                // Convert both to lowercase for case-insensitive comparison\n                return Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).map((s)=>s.toLowerCase()).includes(v.toLowerCase());\n            },\n            message: (props)=>`${props.value} is not a valid status. Valid statuses are: ${Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus).join(\", \")}`\n        }\n    },\n    createdBy: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    currentLocation: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a current location\"\n        ]\n    },\n    recipientId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\"\n    },\n    relatedDocumentId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Document\"\n    },\n    fileUrl: {\n        type: String\n    },\n    fileName: {\n        type: String\n    },\n    fileType: {\n        type: String\n    },\n    trackingNumber: {\n        type: String,\n        // Removed unique constraint to allow multiple documents to share the same tracking number\n        // This is necessary for forwarded documents to maintain the same tracking number\n        // Using sparse index to prevent duplicate key errors with null/undefined values\n        index: {\n            sparse: true\n        },\n        validate: {\n            validator: function(v) {\n                // Allow undefined or null values (will be set later)\n                if (!v) return true;\n                // Validate the format: MGBR2-YYYY-NNNN-NNNN\n                return typeof v === \"string\" && /^MGBR2-\\d{4}-\\d{4}-\\d{4}$/.test(v);\n            },\n            message: (props)=>`${props.value} is not a valid tracking number format. Expected format: MGBR2-YYYY-NNNN-NNNN`\n        }\n    },\n    isOriginal: {\n        type: Boolean,\n        default: false,\n        index: true\n    },\n    journey: [\n        DocumentJourneySchema\n    ]\n}, {\n    timestamps: true\n});\n// Fix for \"Cannot read properties of undefined\" error\nlet DocumentModel;\ntry {\n    // Try to get the existing model\n    DocumentModel = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Document\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    DocumentModel = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Document\", DocumentSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocumentModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Document.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Feedback.ts":
/*!********************************!*\
  !*** ./src/models/Feedback.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\nconst FeedbackSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    userName: {\n        type: String,\n        required: true\n    },\n    userDivision: {\n        type: String,\n        required: true\n    },\n    title: {\n        type: String,\n        required: true,\n        trim: true\n    },\n    description: {\n        type: String,\n        required: true,\n        trim: true\n    },\n    category: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.FeedbackCategory),\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.FeedbackCategory.OTHER\n    },\n    status: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.FeedbackStatus),\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.FeedbackStatus.PENDING\n    },\n    adminResponse: {\n        type: String,\n        trim: true\n    },\n    aiSuggestion: {\n        type: String,\n        trim: true\n    },\n    aiSuggestionGenerated: {\n        type: Boolean,\n        default: false\n    },\n    aiSuggestionGeneratedAt: {\n        type: Date\n    }\n}, {\n    timestamps: true\n});\n// Check if model already exists to prevent overwriting during hot reloads\nconst Feedback = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Feedback || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Feedback\", FeedbackSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Feedback);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Feedback.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Notification.ts":
/*!************************************!*\
  !*** ./src/models/Notification.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst NotificationSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: [\n            true,\n            \"Please provide a user\"\n        ]\n    },\n    documentId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Document\",\n        required: [\n            true,\n            \"Please provide a document\"\n        ]\n    },\n    message: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a message\"\n        ]\n    },\n    isRead: {\n        type: Boolean,\n        default: false\n    },\n    type: {\n        type: String,\n        default: \"GENERAL\"\n    }\n}, {\n    timestamps: true\n});\n// Fix for \"Cannot read properties of undefined\" error\nconst Notification = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Notification || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Notification\", NotificationSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notification);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Notification.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\nconst SessionInfoSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    token: {\n        type: String,\n        required: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    },\n    lastActive: {\n        type: Date,\n        default: Date.now\n    },\n    userAgent: {\n        type: String\n    },\n    ipAddress: {\n        type: String\n    }\n});\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"Please provide a name\"\n        ],\n        maxlength: [\n            60,\n            \"Name cannot be more than 60 characters\"\n        ],\n        unique: true,\n        trim: true\n    },\n    email: {\n        type: String,\n        required: false,\n        lowercase: true,\n        trim: true,\n        unique: true,\n        sparse: true\n    },\n    password: {\n        type: String,\n        select: false\n    },\n    role: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.UserRole),\n        default: _types__WEBPACK_IMPORTED_MODULE_1__.UserRole.EMPLOYEE\n    },\n    division: {\n        type: String,\n        enum: Object.values(_types__WEBPACK_IMPORTED_MODULE_1__.Division),\n        required: [\n            true,\n            \"Please provide a division\"\n        ]\n    },\n    image: {\n        type: String\n    },\n    activeSessions: {\n        type: [\n            SessionInfoSchema\n        ],\n        default: []\n    }\n}, {\n    timestamps: true\n});\n// Fix for \"Cannot read properties of undefined\" error\nlet User;\ntry {\n    // Try to get the existing model\n    User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\");\n} catch (error) {\n    // Model doesn't exist, create a new one\n    User = mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (User);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/audit.ts":
/*!****************************!*\
  !*** ./src/types/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditLogAction: () => (/* binding */ AuditLogAction)\n/* harmony export */ });\nvar AuditLogAction;\n(function(AuditLogAction) {\n    AuditLogAction[\"USER_CREATED\"] = \"USER_CREATED\";\n    AuditLogAction[\"USER_UPDATED\"] = \"USER_UPDATED\";\n    AuditLogAction[\"USER_DELETED\"] = \"USER_DELETED\";\n    AuditLogAction[\"USER_LOGIN\"] = \"USER_LOGIN\";\n    AuditLogAction[\"DOCUMENT_CREATED\"] = \"DOCUMENT_CREATED\";\n    AuditLogAction[\"DOCUMENT_UPDATED\"] = \"DOCUMENT_UPDATED\";\n    AuditLogAction[\"DOCUMENT_DELETED\"] = \"DOCUMENT_DELETED\";\n    AuditLogAction[\"DOCUMENT_STATUS_CHANGED\"] = \"DOCUMENT_STATUS_CHANGED\";\n    AuditLogAction[\"DOCUMENT_SHARED\"] = \"DOCUMENT_SHARED\";\n    AuditLogAction[\"DOCUMENT_FORWARDED\"] = \"DOCUMENT_FORWARDED\";\n    AuditLogAction[\"DOCUMENT_RECEIVED\"] = \"DOCUMENT_RECEIVED\";\n    AuditLogAction[\"DOCUMENT_PROCESSED\"] = \"DOCUMENT_PROCESSED\";\n    AuditLogAction[\"PROFILE_CHANGE_REQUESTED\"] = \"PROFILE_CHANGE_REQUESTED\";\n    AuditLogAction[\"PROFILE_CHANGE_APPROVED\"] = \"PROFILE_CHANGE_APPROVED\";\n    AuditLogAction[\"PROFILE_CHANGE_REJECTED\"] = \"PROFILE_CHANGE_REJECTED\";\n    AuditLogAction[\"FILE_UPLOADED\"] = \"FILE_UPLOADED\";\n    AuditLogAction[\"FILE_DOWNLOADED\"] = \"FILE_DOWNLOADED\";\n    AuditLogAction[\"FILE_DELETED\"] = \"FILE_DELETED\";\n    AuditLogAction[\"FILE_ACCESSED\"] = \"FILE_ACCESSED\";\n    AuditLogAction[\"FEEDBACK_CREATED\"] = \"FEEDBACK_CREATED\";\n    AuditLogAction[\"FEEDBACK_UPDATED\"] = \"FEEDBACK_UPDATED\";\n    AuditLogAction[\"FEEDBACK_DELETED\"] = \"FEEDBACK_DELETED\";\n    AuditLogAction[\"FEEDBACK_STATUS_CHANGED\"] = \"FEEDBACK_STATUS_CHANGED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_GENERATED\"] = \"FEEDBACK_AI_SUGGESTION_GENERATED\";\n    AuditLogAction[\"FEEDBACK_AI_SUGGESTION_REMOVED\"] = \"FEEDBACK_AI_SUGGESTION_REMOVED\";\n    AuditLogAction[\"SEARCH_PERFORMED\"] = \"SEARCH_PERFORMED\";\n    AuditLogAction[\"DATA_CLEANUP\"] = \"DATA_CLEANUP\";\n    AuditLogAction[\"SYSTEM_MAINTENANCE\"] = \"SYSTEM_MAINTENANCE\";\n    AuditLogAction[\"ARCHIVE_EXPORTED\"] = \"ARCHIVE_EXPORTED\";\n})(AuditLogAction || (AuditLogAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Division: () => (/* binding */ Division),\n/* harmony export */   DocumentAction: () => (/* binding */ DocumentAction),\n/* harmony export */   DocumentCategory: () => (/* binding */ DocumentCategory),\n/* harmony export */   DocumentStatus: () => (/* binding */ DocumentStatus),\n/* harmony export */   FeedbackCategory: () => (/* binding */ FeedbackCategory),\n/* harmony export */   FeedbackStatus: () => (/* binding */ FeedbackStatus),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\n// User Roles\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n    UserRole[\"REGIONAL_DIRECTOR\"] = \"REGIONAL_DIRECTOR\";\n    UserRole[\"DIVISION_CHIEF\"] = \"DIVISION_CHIEF\";\n    UserRole[\"EMPLOYEE\"] = \"EMPLOYEE\";\n})(UserRole || (UserRole = {}));\nvar Division;\n(function(Division) {\n    Division[\"ORD\"] = \"ORD\";\n    Division[\"FAD\"] = \"FAD\";\n    Division[\"MMD\"] = \"MMD\";\n    Division[\"MSESDD\"] = \"MSESDD\";\n    Division[\"GSD\"] = \"GSD\";\n})(Division || (Division = {}));\nvar DocumentStatus;\n(function(DocumentStatus) {\n    DocumentStatus[\"INBOX\"] = \"INBOX\";\n    DocumentStatus[\"SENT\"] = \"SENT\";\n    DocumentStatus[\"RECEIVED\"] = \"RECEIVED\";\n    DocumentStatus[\"FORWARDED\"] = \"FORWARDED\";\n    DocumentStatus[\"PENDING\"] = \"PENDING\";\n    DocumentStatus[\"PROCESSED\"] = \"PROCESSED\";\n    DocumentStatus[\"ARCHIVED\"] = \"ARCHIVED\"; // Document has been archived for storage\n})(DocumentStatus || (DocumentStatus = {}));\nvar DocumentCategory;\n(function(DocumentCategory) {\n    // Common Office Documents\n    DocumentCategory[\"MEMO\"] = \"MEMO\";\n    DocumentCategory[\"LETTER\"] = \"LETTER\";\n    DocumentCategory[\"REPORT\"] = \"REPORT\";\n    DocumentCategory[\"PROPOSAL\"] = \"PROPOSAL\";\n    DocumentCategory[\"MINUTES\"] = \"MINUTES\";\n    DocumentCategory[\"FORM\"] = \"FORM\";\n    // Official Documents\n    DocumentCategory[\"CIRCULAR\"] = \"CIRCULAR\";\n    DocumentCategory[\"ADVISORY\"] = \"ADVISORY\";\n    DocumentCategory[\"BULLETIN\"] = \"BULLETIN\";\n    DocumentCategory[\"NOTICE\"] = \"NOTICE\";\n    DocumentCategory[\"ANNOUNCEMENT\"] = \"ANNOUNCEMENT\";\n    DocumentCategory[\"RESOLUTION\"] = \"RESOLUTION\";\n    DocumentCategory[\"POLICY\"] = \"POLICY\";\n    DocumentCategory[\"GUIDELINE\"] = \"GUIDELINE\";\n    DocumentCategory[\"DIRECTIVE\"] = \"DIRECTIVE\";\n    DocumentCategory[\"MEMORANDUM_ORDER\"] = \"MEMORANDUM ORDER\";\n    DocumentCategory[\"MEMORANDUM_CIRCULAR\"] = \"MEMORANDUM CIRCULAR\";\n    DocumentCategory[\"EXECUTIVE_ORDER\"] = \"EXECUTIVE ORDER\";\n    DocumentCategory[\"ADMINISTRATIVE_ORDER\"] = \"ADMINISTRATIVE ORDER\";\n    // Legal & Financial Documents\n    DocumentCategory[\"CONTRACT\"] = \"CONTRACT\";\n    DocumentCategory[\"CERTIFICATE\"] = \"CERTIFICATE\";\n    DocumentCategory[\"ENDORSEMENT\"] = \"ENDORSEMENT\";\n    DocumentCategory[\"MANUAL\"] = \"MANUAL\";\n    DocumentCategory[\"INVOICE\"] = \"INVOICE\";\n    DocumentCategory[\"RECEIPT\"] = \"RECEIPT\";\n    DocumentCategory[\"VOUCHER\"] = \"VOUCHER\";\n    DocumentCategory[\"REQUISITION\"] = \"REQUISITION\";\n    DocumentCategory[\"PURCHASE_ORDER\"] = \"PURCHASE ORDER\";\n    DocumentCategory[\"BUDGET_REQUEST\"] = \"BUDGET REQUEST\";\n    DocumentCategory[\"TRAVEL_ORDER\"] = \"TRAVEL ORDER\";\n    DocumentCategory[\"LEAVE_FORM\"] = \"LEAVE FORM\";\n    // Other\n    DocumentCategory[\"OTHER\"] = \"OTHER\";\n})(DocumentCategory || (DocumentCategory = {}));\nvar DocumentAction;\n(function(DocumentAction) {\n    DocumentAction[\"NONE\"] = \"No specific action required\";\n    // Actions from the image (A-S)\n    DocumentAction[\"FOR_INFO\"] = \"A - For information/guidance/reference\";\n    DocumentAction[\"FOR_COMMENTS\"] = \"B - For comments/recommendations\";\n    DocumentAction[\"TAKE_UP\"] = \"C - Pls. take up with me\";\n    DocumentAction[\"DRAFT_ANSWER\"] = \"D - Pls. draft answer/memo/acknow.\";\n    DocumentAction[\"FOR_ACTION\"] = \"E - For appropriate action\";\n    DocumentAction[\"IMMEDIATE_INVESTIGATION\"] = \"F - Pls. immediate investigation\";\n    DocumentAction[\"ATTACH_SUPPORTING\"] = \"G - Pls. attach supporting papers\";\n    DocumentAction[\"FOR_APPROVAL\"] = \"H - For approval\";\n    DocumentAction[\"FOR_SIGNATURE\"] = \"I - For initial/signature\";\n    DocumentAction[\"STUDY_EVALUATE\"] = \"J - Pls. study / evaluate\";\n    DocumentAction[\"RELEASE_FILE\"] = \"K - Pls. release/file\";\n    DocumentAction[\"UPDATE_STATUS\"] = \"L - Update status of case\";\n    DocumentAction[\"FILE_CLOSE\"] = \"M - Filed / Close\";\n    DocumentAction[\"FOR_ADA\"] = \"N - For ADA / Check Preparation\";\n    DocumentAction[\"FOR_DISCUSSION\"] = \"O - FOD (For Discussion)\";\n    DocumentAction[\"FOR_REVISION\"] = \"P - For Revision\";\n    DocumentAction[\"ATTACH_DRAFT\"] = \"Q - Pls. Attach Draft File\";\n    DocumentAction[\"SAVED\"] = \"R - Saved\";\n    DocumentAction[\"FOR_SCANNING\"] = \"S - For Scanning\";\n    // Additional useful actions for office work\n    DocumentAction[\"URGENT\"] = \"URGENT - Requires immediate attention\";\n    DocumentAction[\"CONFIDENTIAL\"] = \"CONFIDENTIAL - Restricted access\";\n    DocumentAction[\"FOR_REVIEW\"] = \"FOR REVIEW - Please review and provide feedback\";\n    DocumentAction[\"FOR_COORDINATION\"] = \"FOR COORDINATION - Coordinate with relevant departments\";\n    DocumentAction[\"FOR_COMPLIANCE\"] = \"FOR COMPLIANCE - Ensure compliance with regulations\";\n    DocumentAction[\"FOR_IMPLEMENTATION\"] = \"FOR IMPLEMENTATION - Implement the described actions\";\n    DocumentAction[\"FOR_FILING\"] = \"FOR FILING - File for future reference\";\n    DocumentAction[\"FOR_DISTRIBUTION\"] = \"FOR DISTRIBUTION - Distribute to concerned parties\";\n    DocumentAction[\"FOR_ENDORSEMENT\"] = \"FOR ENDORSEMENT - Endorse to appropriate authority\";\n    DocumentAction[\"FOR_VERIFICATION\"] = \"FOR VERIFICATION - Verify information/data\";\n    DocumentAction[\"FOR_RECORDING\"] = \"FOR RECORDING - Record in the system\";\n})(DocumentAction || (DocumentAction = {}));\nvar FeedbackCategory;\n(function(FeedbackCategory) {\n    FeedbackCategory[\"BUG\"] = \"bug\";\n    FeedbackCategory[\"FEATURE\"] = \"feature\";\n    FeedbackCategory[\"IMPROVEMENT\"] = \"improvement\";\n    FeedbackCategory[\"OTHER\"] = \"other\";\n})(FeedbackCategory || (FeedbackCategory = {}));\nvar FeedbackStatus;\n(function(FeedbackStatus) {\n    FeedbackStatus[\"PENDING\"] = \"pending\";\n    FeedbackStatus[\"REVIEWED\"] = \"reviewed\";\n    FeedbackStatus[\"IMPLEMENTED\"] = \"implemented\";\n    FeedbackStatus[\"REJECTED\"] = \"rejected\";\n})(FeedbackStatus || (FeedbackStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/audit.ts":
/*!****************************!*\
  !*** ./src/utils/audit.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logAuditEvent: () => (/* binding */ logAuditEvent)\n/* harmony export */ });\n/* harmony import */ var _models_AuditLog__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/models/AuditLog */ \"(rsc)/./src/models/AuditLog.ts\");\n\nasync function logAuditEvent({ action, performedBy, targetId, targetType, details, request }) {\n    try {\n        const auditLogData = {\n            action,\n            performedBy,\n            targetId,\n            targetType,\n            details\n        };\n        // Add request information if available\n        if (request) {\n            auditLogData.ipAddress = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n            auditLogData.userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        }\n        // Create the audit log entry\n        await _models_AuditLog__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create(auditLogData);\n    } catch (error) {\n        console.error(\"Error creating audit log:\", error);\n    // Don't throw the error - we don't want to break the main functionality\n    // if audit logging fails\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/serverTimestamp.ts":
/*!**************************************!*\
  !*** ./src/utils/serverTimestamp.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerTimestamp: () => (/* binding */ getServerTimestamp)\n/* harmony export */ });\n/**\n * This utility manages the server timestamp used to invalidate sessions on server restart\n */ // Use a more stable identifier that doesn't change on every development hot reload\n// In production, this will still change on server restart\nlet SERVER_START_TIMESTAMP;\n// Try to use a timestamp that persists across hot reloads in development\nif (true) {\n    // In development, use a timestamp that changes daily instead of on every restart\n    // This prevents constant logouts during development\n    const today = new Date();\n    const dateString = `${today.getFullYear()}-${today.getMonth()}-${today.getDate()}`;\n    SERVER_START_TIMESTAMP = dateString;\n} else {}\n// Function to get the current server timestamp\nfunction getServerTimestamp() {\n    return SERVER_START_TIMESTAMP;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/serverTimestamp.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/sessionToken.ts":
/*!***********************************!*\
  !*** ./src/utils/sessionToken.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupAllExpiredSessions: () => (/* binding */ cleanupAllExpiredSessions),\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   removeAllOtherSessions: () => (/* binding */ removeAllOtherSessions),\n/* harmony export */   removeSession: () => (/* binding */ removeSession),\n/* harmony export */   validateSession: () => (/* binding */ validateSession)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/mongodb */ \"(rsc)/./src/lib/db/mongodb.ts\");\n\n\n\n// Maximum number of sessions per user\nconst MAX_SESSIONS_PER_USER = 5;\n// Session expiration time in milliseconds (24 hours)\nconst SESSION_EXPIRATION_MS = 24 * 60 * 60 * 1000;\n/**\n * Generate a unique session token\n * @returns A random session token\n */ function generateSessionToken() {\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes)(32).toString(\"hex\");\n}\n/**\n * Create a new session for a user\n * @param userId The user ID\n * @param userAgent The user agent string\n * @param ipAddress The IP address\n * @returns The session token\n */ async function createSession(userId, userAgent, ipAddress) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const sessionToken = generateSessionToken();\n    const now = new Date();\n    const sessionInfo = {\n        token: sessionToken,\n        createdAt: now,\n        lastActive: now,\n        userAgent,\n        ipAddress\n    };\n    // Get the user with their current sessions\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    if (!user) {\n        throw new Error(\"User not found\");\n    }\n    // Clean up expired sessions first\n    await cleanupExpiredSessions(userId);\n    // If the user has too many active sessions, remove the oldest ones\n    if (user.activeSessions && user.activeSessions.length >= MAX_SESSIONS_PER_USER) {\n        // Sort sessions by lastActive (oldest first)\n        const sortedSessions = [\n            ...user.activeSessions\n        ].sort((a, b)=>a.lastActive.getTime() - b.lastActive.getTime());\n        // Calculate how many sessions to remove\n        const sessionsToRemove = Math.max(0, sortedSessions.length - MAX_SESSIONS_PER_USER + 1);\n        if (sessionsToRemove > 0) {\n            // Get the tokens of the oldest sessions\n            const tokensToRemove = sortedSessions.slice(0, sessionsToRemove).map((session)=>session.token);\n            // Remove the oldest sessions\n            await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n                _id: userId\n            }, {\n                $pull: {\n                    activeSessions: {\n                        token: {\n                            $in: tokensToRemove\n                        }\n                    }\n                }\n            });\n        }\n    }\n    // Add the new session\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findByIdAndUpdate(userId, {\n        $push: {\n            activeSessions: sessionInfo\n        }\n    }, {\n        new: true\n    });\n    return sessionToken;\n}\n/**\n * Clean up expired sessions for a user\n * @param userId The user ID\n */ async function cleanupExpiredSessions(userId) {\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                lastActive: {\n                    $lt: expirationThreshold\n                }\n            }\n        }\n    });\n}\n/**\n * Validate a session token for a user\n * @param userId The user ID\n * @param sessionToken The session token to validate\n * @returns True if the session is valid, false otherwise\n */ async function validateSession(userId, sessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // Clean up expired sessions first\n    await cleanupExpiredSessions(userId);\n    // Find the user and check if they have this session token\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findOne({\n        _id: userId,\n        \"activeSessions.token\": sessionToken\n    });\n    if (!user) {\n        return false;\n    }\n    // Find the specific session\n    const session = user.activeSessions.find((s)=>s.token === sessionToken);\n    if (!session) {\n        return false;\n    }\n    // Check if the session has expired\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    if (session.lastActive < expirationThreshold) {\n        // Session has expired, remove it\n        await removeSession(userId, sessionToken);\n        return false;\n    }\n    // Update the last active time for this session\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId,\n        \"activeSessions.token\": sessionToken\n    }, {\n        $set: {\n            \"activeSessions.$.lastActive\": now\n        }\n    });\n    return true;\n}\n/**\n * Remove a session for a user\n * @param userId The user ID\n * @param sessionToken The session token to remove\n */ async function removeSession(userId, sessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: sessionToken\n            }\n        }\n    });\n}\n/**\n * Get all active sessions for a user\n * @param userId The user ID\n * @returns Array of active sessions\n */ async function getUserSessions(userId) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const user = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    return user?.activeSessions || [];\n}\n/**\n * Remove all sessions for a user except the current one\n * @param userId The user ID\n * @param currentSessionToken The current session token to keep\n * @returns The number of sessions removed\n */ async function removeAllOtherSessions(userId, currentSessionToken) {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // First clean up expired sessions\n    await cleanupExpiredSessions(userId);\n    // Get the user to count sessions before removal\n    const userBefore = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    const sessionCountBefore = userBefore?.activeSessions?.length || 0;\n    // Remove all other sessions\n    const result = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n        _id: userId\n    }, {\n        $pull: {\n            activeSessions: {\n                token: {\n                    $ne: currentSessionToken\n                }\n            }\n        }\n    });\n    // Get the user again to count sessions after removal\n    const userAfter = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(userId);\n    const sessionCountAfter = userAfter?.activeSessions?.length || 0;\n    // Return the number of sessions removed\n    return sessionCountBefore - sessionCountAfter;\n}\n/**\n * Cleanup all expired sessions in the database\n * This can be run as a scheduled task\n */ async function cleanupAllExpiredSessions() {\n    await (0,_lib_db_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const now = new Date();\n    const expirationThreshold = new Date(now.getTime() - SESSION_EXPIRATION_MS);\n    // Find all users with expired sessions\n    const users = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].find({\n        \"activeSessions.lastActive\": {\n            $lt: expirationThreshold\n        }\n    });\n    let totalRemoved = 0;\n    // Clean up expired sessions for each user\n    for (const user of users){\n        const sessionCountBefore = user.activeSessions.length;\n        // Remove expired sessions\n        await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateOne({\n            _id: user._id\n        }, {\n            $pull: {\n                activeSessions: {\n                    lastActive: {\n                        $lt: expirationThreshold\n                    }\n                }\n            }\n        });\n        // Get the user again to count sessions after removal\n        const updatedUser = await _models_User__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(user._id);\n        const sessionCountAfter = updatedUser?.activeSessions?.length || 0;\n        totalRemoved += sessionCountBefore - sessionCountAfter;\n    }\n    return totalRemoved;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/sessionToken.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@babel","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcleanup%2Froute&page=%2Fapi%2Fadmin%2Fcleanup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcleanup%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CDocumentTracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();