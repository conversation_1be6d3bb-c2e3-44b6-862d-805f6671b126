/**
 * Chatbot Service
 * Handles AI-powered chatbot interactions and responses
 */

import { geminiClient } from '../core/geminiClient';
import {
  ChatMessage,
  ChatContext,
  IntentAnalysisResult,
  ContextualHelpResult
} from '../core/types';

export class ChatbotService {
  /**
   * Generate chat response using Gemini AI
   * @param message User message
   * @param context Chat context
   * @param conversationHistory Previous messages
   * @returns AI-generated response
   */
  static async generateChatResponse(
    message: string,
    context: ChatContext,
    conversationHistory: ChatMessage[] = []
  ): Promise<{
    message: string;
    actions?: Array<{
      type: 'navigate' | 'search' | 'create' | 'help' | 'info';
      label: string;
      data?: any;
    }>;
    quickReplies?: string[];
  }> {
    try {
      const prompt = this.createChatPrompt(message, context, conversationHistory);

      if (geminiClient.isAvailable()) {
        try {
          const response = await geminiClient.generateContent(prompt);
          return this.formatChatResponse(response, message, context);
        } catch (apiError) {
          console.warn('Gemini API failed for chat, using fallback:', apiError);
        }
      }

      // Fallback to local generation
      return this.generateLocalChatResponse(message, context);
    } catch (error) {
      console.error('Error generating chat response:', error);
      throw error;
    }
  }

  /**
   * Analyze user intent from message
   * @param message User message
   * @returns Intent analysis result
   */
  static async analyzeIntent(message: string): Promise<IntentAnalysisResult> {
    try {
      if (geminiClient.isAvailable()) {
        try {
          const prompt = this.createIntentAnalysisPrompt(message);
          const response = await geminiClient.generateContent(prompt);
          return this.parseIntentAnalysis(response);
        } catch (apiError) {
          console.warn('Gemini API failed for intent analysis, using fallback:', apiError);
        }
      }

      // Fallback to local analysis
      return this.analyzeIntentLocally(message);
    } catch (error) {
      console.error('Error analyzing intent:', error);
      throw error;
    }
  }

  /**
   * Generate contextual help
   * @param context Current context
   * @returns Contextual help information
   */
  static async generateContextualHelp(context: ChatContext): Promise<ContextualHelpResult> {
    try {
      if (geminiClient.isAvailable()) {
        try {
          const prompt = this.createContextualHelpPrompt(context);
          const response = await geminiClient.generateContent(prompt);
          return this.parseContextualHelp(response);
        } catch (apiError) {
          console.warn('Gemini API failed for contextual help, using fallback:', apiError);
        }
      }

      // Fallback to local generation
      return this.generateLocalContextualHelp(context);
    } catch (error) {
      console.error('Error generating contextual help:', error);
      throw error;
    }
  }

  /**
   * Create chat prompt for Gemini API
   */
  private static createChatPrompt(
    message: string,
    context: ChatContext,
    conversationHistory: ChatMessage[]
  ): string {
    const historyText = conversationHistory
      .slice(-5) // Last 5 messages for context
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    return `
You are MGB Bot, an AI assistant for the MGB Document Tracking System.

CONTEXT:
- Current Page: ${context.currentPage || 'Unknown'}
- User Role: ${context.userRole || 'Unknown'}
- User Division: ${context.userDivision || 'Unknown'}
- Pending Documents: ${context.pendingDocuments || 0}

CONVERSATION HISTORY:
${historyText}

USER MESSAGE: ${message}

Provide a helpful, concise response that:
1. Addresses the user's question or request
2. Considers their role and current context
3. Offers specific, actionable guidance
4. Uses a friendly, professional tone
5. Stays focused on document tracking system features

If the user asks about:
- Document creation: Guide them through the process
- Document status: Explain the workflow and statuses
- Navigation: Help them find the right page or feature
- Troubleshooting: Provide step-by-step solutions
- System features: Explain functionality clearly

Keep responses under 200 words and be conversational.
`;
  }

  /**
   * Create intent analysis prompt
   */
  private static createIntentAnalysisPrompt(message: string): string {
    return `
Analyze the user's intent from this message: "${message}"

Classify the intent and extract entities. Respond in JSON format:
{
  "intent": "intent_name",
  "confidence": 0.85,
  "entities": [
    {"type": "entity_type", "value": "entity_value"}
  ],
  "suggestions": ["suggestion1", "suggestion2"]
}

Common intents:
- document_create, document_search, document_status
- navigation_help, feature_explanation, troubleshooting
- greeting, goodbye, help_request
`;
  }

  /**
   * Create contextual help prompt
   */
  private static createContextualHelpPrompt(context: ChatContext): string {
    return `
Generate contextual help for a user in the Document Tracking System.

CONTEXT:
- Current Page: ${context.currentPage || 'Unknown'}
- User Role: ${context.userRole || 'Unknown'}
- Pending Documents: ${context.pendingDocuments || 0}

Provide help in JSON format:
{
  "helpMessage": "Contextual help message",
  "quickActions": [
    {"label": "Action", "action": "action_id", "description": "What it does"}
  ],
  "tutorials": [
    {"title": "Tutorial", "steps": ["Step 1", "Step 2"]}
  ]
}
`;
  }

  /**
   * Parse intent analysis response
   */
  private static parseIntentAnalysis(response: string): IntentAnalysisResult {
    try {
      const parsed = JSON.parse(response);
      return {
        intent: parsed.intent || 'unknown',
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || [],
        suggestions: parsed.suggestions || []
      };
    } catch (error) {
      return {
        intent: 'unknown',
        confidence: 0.5,
        entities: [],
        suggestions: []
      };
    }
  }

  /**
   * Parse contextual help response
   */
  private static parseContextualHelp(response: string): ContextualHelpResult {
    try {
      const parsed = JSON.parse(response);
      return {
        helpMessage: parsed.helpMessage || 'How can I help you today?',
        quickActions: parsed.quickActions || [],
        tutorials: parsed.tutorials || []
      };
    } catch (error) {
      return this.generateLocalContextualHelp({});
    }
  }

  /**
   * Format chat response from Gemini API
   */
  private static formatChatResponse(
    response: string,
    userMessage: string,
    context: ChatContext
  ): {
    message: string;
    actions?: Array<{
      type: 'navigate' | 'search' | 'create' | 'help' | 'info';
      label: string;
      data?: any;
    }>;
    quickReplies?: string[];
  } {
    // Generate contextual actions and quick replies based on the response and context
    const actions = this.generateContextualActions(userMessage, context);
    const quickReplies = this.generateQuickReplies(userMessage, context);

    return {
      message: response || 'I apologize, but I\'m having trouble generating a response right now. How can I help you with the document tracking system?',
      actions,
      quickReplies
    };
  }

  /**
   * Generate local chat response fallback
   */
  private static generateLocalChatResponse(
    message: string,
    context: ChatContext
  ): {
    message: string;
    actions?: Array<{
      type: 'navigate' | 'search' | 'create' | 'help' | 'info';
      label: string;
      data?: any;
    }>;
    quickReplies?: string[];
  } {
    const lowerMessage = message.toLowerCase();
    let responseMessage = '';

    // Greeting responses
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
      responseMessage = `Hello! I'm MGB Bot, your intelligent AI assistant. I can help you with the document tracking system, answer general questions, provide information on various topics, and much more. What would you like to know or do today?`;
    }
    // Help requests
    else if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      if (context.currentPage?.includes('documents/add')) {
        responseMessage = `I can help you create a document! Fill in the title, description, select a category and recipient, then attach your file. Make sure all required fields are completed before sending.`;
      } else if (context.currentPage?.includes('documents')) {
        responseMessage = `You're viewing your documents. Use the filters (Inbox, Sent, Pending, etc.) to organize them. Click on any document to view details, or use the "Send Document" button to create a new one.`;
      } else {
        responseMessage = `I can help you with:
• Creating and sending documents
• Checking document status and tracking
• Understanding the document workflow
• Navigating the system
• Managing your inbox

What specific task would you like help with?`;
      }
    }
    // Quick tour requests
    else if (lowerMessage.includes('quick tour') || lowerMessage.includes('tour')) {
      responseMessage = `🚀 **Welcome to the MGB Document Tracking System Quick Tour!**

**📊 Dashboard** - Your central hub showing:
• Document statistics and activity
• Recent documents and notifications
• Quick access to main features

**📝 Document Management**:
• **Send Document**: Create and send new documents
• **Inbox**: Receive and process incoming documents
• **Sent**: Track documents you've sent
• **Pending**: Documents awaiting action

**🔄 Document Workflow**:
1. **Create** → Fill details, select recipient, attach file
2. **Send** → Document gets a tracking number (DTN)
3. **Receive** → Recipient accepts the document
4. **Process** → Take required action
5. **Forward/Archive** → Complete the workflow

**🔍 Key Features**:
• Advanced search and filtering
• Real-time status tracking
• Routing slip management
• Document journey history
• Role-based permissions

Ready to explore? Try creating your first document!`;
    }
    // System features requests
    else if (lowerMessage.includes('features') || lowerMessage.includes('system features')) {
      responseMessage = `🎯 **MGB Document Tracking System Features**

**📋 Core Features**:
• **Document Creation & Management** - Create, send, and track documents
• **Digital Workflow** - Paperless document routing and approval
• **Real-time Tracking** - Monitor document status and location
• **Advanced Search** - Find documents by various criteria

**🔐 Security & Access**:
• **Role-based Permissions** - Admin, Division Chief, Employee levels
• **Secure Authentication** - Protected access to sensitive documents
• **Audit Trail** - Complete document journey history

**📊 Reporting & Analytics**:
• **Dashboard Analytics** - Visual document statistics
• **Performance Metrics** - Track processing times and efficiency
• **Custom Reports** - Generate reports by division, date, status

**🔄 Workflow Management**:
• **Routing Slips** - Define document paths and approvals
• **Status Tracking** - Pending, Received, Processed, Archived
• **Notifications** - Real-time alerts and updates

**🏢 Division Support**:
• ORD - Office of Regional Director
• FAD - Finance and Administrative Division
• MMD - Mines Management Division
• MSESDD - Safety & Environmental Division
• GSD - Geological Survey Division

Which feature would you like to explore in detail?`;
    }
    // Document-related queries
    else if (lowerMessage.includes('document') || lowerMessage.includes('send') || lowerMessage.includes('create')) {
      responseMessage = `To create a document:
1. Click "Send Document" or go to Documents → Add New
2. Fill in the title and description
3. Select the document category
4. Choose the recipient and their division
5. Select the required action
6. Attach your file
7. Review and send

Need help with any specific step?`;
    }
    // Status queries
    else if (lowerMessage.includes('status') || lowerMessage.includes('track')) {
      responseMessage = `Document statuses in our system:
• Pending: Waiting to be received
• Received: Accepted by recipient
• Processed: Action completed
• Forwarded: Sent to another person
• Archived: Filed away

You can track any document using its DTN (Document Tracking Number) or check your dashboard for status updates.`;
    }
    // Navigation help
    else if (lowerMessage.includes('navigate') || lowerMessage.includes('find') || lowerMessage.includes('where')) {
      responseMessage = `Here's how to navigate:
• Dashboard: Overview and recent activity
• Documents: View all your documents with filters
• Send Document: Create new documents
• Notifications: Check alerts and updates
• Settings: Customize your preferences

Use the sidebar menu to access these sections. What are you looking for?`;
    }
    // General knowledge and random questions
    else {
      responseMessage = this.generateGeneralResponse(message, lowerMessage);
    }

    // Generate contextual actions and quick replies
    const actions = this.generateContextualActions(message, context);
    const quickReplies = this.generateQuickReplies(message, context);

    return {
      message: responseMessage,
      actions,
      quickReplies
    };
  }

  /**
   * Generate general response for random questions (ChatGPT-like capability)
   */
  private static generateGeneralResponse(message: string, lowerMessage: string): string {
    // Science and Technology
    if (lowerMessage.includes('science') || lowerMessage.includes('technology') || lowerMessage.includes('ai') || lowerMessage.includes('artificial intelligence')) {
      return `🔬 **Science & Technology**

I'd be happy to discuss science and technology topics! Here are some fascinating areas:

**🤖 Artificial Intelligence**: AI is revolutionizing how we work, from document processing systems like ours to advanced language models, computer vision, and machine learning.

**🧬 Scientific Discoveries**: Recent breakthroughs in quantum computing, gene editing, renewable energy, and space exploration are shaping our future.

**💻 Technology Trends**: Cloud computing, blockchain, IoT, and automation are transforming industries worldwide.

What specific aspect interests you? I can provide more detailed information on any topic!`;
    }

    // Weather and Nature
    if (lowerMessage.includes('weather') || lowerMessage.includes('climate') || lowerMessage.includes('nature') || lowerMessage.includes('environment')) {
      return `🌤️ **Weather & Environment**

Weather and environmental topics are fascinating! Here's what I can share:

**🌡️ Weather Patterns**: Weather is influenced by atmospheric pressure, temperature, humidity, and wind patterns. Climate change is affecting global weather systems.

**🌱 Environmental Conservation**: Protecting our environment through sustainable practices, renewable energy, and conservation efforts is crucial for future generations.

**🌍 Climate Science**: Understanding greenhouse gases, carbon cycles, and ecosystem interactions helps us address environmental challenges.

**🌿 Philippines Climate**: The Philippines has a tropical climate with wet and dry seasons, influenced by monsoons and typhoons.

What specific environmental or weather topic would you like to explore?`;
    }

    // History and Culture
    if (lowerMessage.includes('history') || lowerMessage.includes('culture') || lowerMessage.includes('philippines') || lowerMessage.includes('filipino')) {
      return `🏛️ **History & Culture**

History and culture are rich topics! Let me share some insights:

**🇵🇭 Philippine History**: The Philippines has a diverse history spanning pre-colonial kingdoms, Spanish colonization, American period, Japanese occupation, and independence in 1946.

**🎭 Filipino Culture**: Rich traditions including festivals, cuisine, languages (170+ languages!), arts, and strong family values shape Filipino identity.

**🏛️ World History**: From ancient civilizations to modern times, history teaches us about human progress, conflicts, and achievements.

**🎨 Cultural Heritage**: Art, literature, music, and traditions preserve and transmit cultural knowledge across generations.

What historical period or cultural aspect interests you most?`;
    }

    // Mathematics and Logic
    if (lowerMessage.includes('math') || lowerMessage.includes('mathematics') || lowerMessage.includes('calculate') || lowerMessage.includes('equation')) {
      return `🔢 **Mathematics & Logic**

Mathematics is the language of the universe! Here's what I can help with:

**📊 Basic Math**: Arithmetic, algebra, geometry, and statistics for everyday problem-solving.

**🧮 Advanced Topics**: Calculus, linear algebra, probability, and discrete mathematics for complex analysis.

**💡 Problem Solving**: Breaking down complex problems into manageable steps using logical reasoning.

**📈 Real-World Applications**: Math in finance, engineering, data analysis, and even document tracking systems!

Do you have a specific math problem or concept you'd like help with? I can explain concepts or work through problems step by step!`;
    }

    // Health and Wellness
    if (lowerMessage.includes('health') || lowerMessage.includes('wellness') || lowerMessage.includes('fitness') || lowerMessage.includes('medical')) {
      return `🏥 **Health & Wellness**

Health and wellness are fundamental to a good life! Here are some key areas:

**💪 Physical Health**: Regular exercise, balanced nutrition, adequate sleep, and preventive care maintain physical well-being.

**🧠 Mental Health**: Stress management, mindfulness, social connections, and work-life balance support mental wellness.

**🥗 Nutrition**: Eating a variety of fruits, vegetables, whole grains, and lean proteins provides essential nutrients.

**⚕️ Healthcare**: Regular check-ups, vaccinations, and early detection help prevent and manage health issues.

**Note**: I can provide general health information, but always consult healthcare professionals for medical advice!

What aspect of health and wellness interests you?`;
    }

    // Programming and Technology
    if (lowerMessage.includes('programming') || lowerMessage.includes('coding') || lowerMessage.includes('software') || lowerMessage.includes('computer')) {
      return `💻 **Programming & Software**

Programming is an exciting field! Here's what I can share:

**🚀 Programming Languages**: JavaScript, Python, Java, C++, and many others each have unique strengths for different applications.

**🌐 Web Development**: Frontend (React, Vue, Angular) and backend (Node.js, Python, PHP) technologies create modern web applications.

**📱 Mobile Development**: iOS (Swift), Android (Kotlin/Java), and cross-platform (React Native, Flutter) development.

**🗄️ Databases**: SQL and NoSQL databases store and manage application data efficiently.

**🔧 Our System**: This document tracking system uses Next.js, React, TypeScript, MongoDB, and modern web technologies!

What programming topic would you like to explore? I can explain concepts, best practices, or help with specific questions!`;
    }

    // Food and Cooking
    if (lowerMessage.includes('food') || lowerMessage.includes('cooking') || lowerMessage.includes('recipe') || lowerMessage.includes('cuisine')) {
      return `🍽️ **Food & Cooking**

Food brings people together! Here's some culinary knowledge:

**🇵🇭 Filipino Cuisine**: Adobo, sinigang, lechon, pancit, and halo-halo represent the rich flavors of Philippine cooking.

**🍳 Cooking Basics**: Understanding heat, seasoning, timing, and ingredient combinations creates delicious meals.

**🥘 World Cuisines**: Italian pasta, Japanese sushi, Mexican tacos, Indian curry - each culture has unique flavors and techniques.

**🥗 Healthy Cooking**: Fresh ingredients, balanced nutrition, and proper cooking methods promote health and taste.

**👨‍🍳 Cooking Tips**: Mise en place (prep everything first), taste as you go, and don't be afraid to experiment!

What type of cuisine or cooking technique interests you? I can share recipes, tips, or cultural food traditions!`;
    }

    // Filipino Recipes (specific)
    if (lowerMessage.includes('filipino recipes') || lowerMessage.includes('adobo') || (lowerMessage.includes('give me') && lowerMessage.includes('recipes'))) {
      return `🇵🇭 **Filipino Recipes**

Here are some beloved Filipino recipes to try:

**🥩 Classic Pork Adobo**
*Ingredients:*
• 2 lbs pork belly or shoulder, cut in chunks
• 1/2 cup soy sauce
• 1/4 cup white vinegar
• 1 head garlic, minced
• 2 bay leaves
• 1 tsp black peppercorns
• 2 tbsp cooking oil

*Instructions:*
1. Marinate pork in soy sauce, vinegar, and garlic for 30 minutes
2. Heat oil in a pot, brown the pork pieces
3. Add marinade, bay leaves, and peppercorns
4. Simmer covered for 45 minutes until tender
5. Remove cover, cook until sauce reduces
6. Serve with steamed rice

**🍲 Other Filipino Favorites:**
• **Sinigang**: Sour soup with tamarind, pork/shrimp, and vegetables
• **Pancit**: Stir-fried noodles with vegetables and meat
• **Lechon Kawali**: Crispy pork belly
• **Kare-Kare**: Oxtail stew with peanut sauce
• **Halo-Halo**: Mixed dessert with shaved ice and toppings

**💡 Filipino Cooking Tips:**
• Balance sweet, sour, and salty flavors
• Use fresh ingredients when possible
• Don't rush the cooking process
• Taste and adjust seasoning as you go

Want a specific recipe or cooking technique? Just ask!`;
    }

    // Cooking Tips (specific)
    if (lowerMessage.includes('cooking tips') || lowerMessage.includes('cooking techniques')) {
      return `🍳 **Cooking Tips & Techniques**

Master these essential cooking skills:

**🔥 Heat Control:**
• **Low Heat**: For delicate sauces, melting chocolate, slow cooking
• **Medium Heat**: For sautéing vegetables, cooking eggs, most everyday cooking
• **High Heat**: For searing meat, stir-frying, boiling water

**🧂 Seasoning Secrets:**
• **Salt Early**: Season meat 40 minutes before cooking for better flavor
• **Taste as You Go**: Adjust seasoning throughout cooking
• **Acid Balance**: Add lemon juice or vinegar to brighten flavors
• **Fresh Herbs**: Add at the end to preserve flavor and color

**⏰ Timing Tips:**
• **Mise en Place**: Prep all ingredients before you start cooking
• **Rest Meat**: Let cooked meat rest 5-10 minutes before slicing
• **Don't Overcrowd**: Cook in batches for better browning
• **Preheat Properly**: Always preheat pans and ovens

**🔪 Knife Skills:**
• Keep knives sharp for safety and efficiency
• Use proper cutting board (wood for meat, plastic for vegetables)
• Learn basic cuts: dice, julienne, chiffonade

**🇵🇭 Filipino Cooking Wisdom:**
• **"Lasa ng Lola"**: Taste like grandma - cook with love and patience
• **Balance Flavors**: Sweet, sour, salty, and umami in harmony
• **Fresh is Best**: Use fresh garlic, ginger, and herbs when possible

What specific cooking technique would you like to master?`;
    }

    // Healthy Eating (specific)
    if (lowerMessage.includes('healthy eating') || lowerMessage.includes('nutrition') || lowerMessage.includes('healthy habits')) {
      return `🥗 **Healthy Eating Guide**

Build better eating habits for life:

**🌈 Balanced Plate Method:**
• **1/2 Plate**: Vegetables and fruits (variety of colors)
• **1/4 Plate**: Lean protein (fish, chicken, beans, tofu)
• **1/4 Plate**: Whole grains (brown rice, quinoa, whole wheat)
• **Healthy Fats**: Avocado, nuts, olive oil in moderation

**🇵🇭 Healthy Filipino Choices:**
• **Choose**: Grilled bangus over fried
• **Swap**: Brown rice for white rice
• **Add**: More vegetables to pancit and other dishes
• **Reduce**: Sodium in adobo by using less soy sauce
• **Include**: Fresh fruits like mango, papaya, banana

**💧 Hydration & Portions:**
• Drink 8-10 glasses of water daily
• Use smaller plates to control portions
• Eat slowly and mindfully
• Stop eating when 80% full

**🕐 Meal Timing:**
• **Breakfast**: Don't skip - fuel your day
• **Lunch**: Largest meal with balanced nutrients
• **Dinner**: Lighter, earlier (3 hours before bed)
• **Snacks**: Fruits, nuts, or yogurt between meals

**🛒 Smart Shopping:**
• Shop the perimeter of grocery stores first
• Read nutrition labels
• Choose whole foods over processed
• Plan meals to avoid impulse buying

**⚠️ Note**: This is general nutrition information. Consult healthcare professionals for personalized dietary advice.

What specific aspect of healthy eating interests you?`;
    }

    // Travel and Geography
    if (lowerMessage.includes('travel') || lowerMessage.includes('geography') || lowerMessage.includes('country') || lowerMessage.includes('city')) {
      return `🌍 **Travel & Geography**

The world is full of amazing places to explore!

**🏝️ Philippines**: 7,641 islands with stunning beaches, mountains, rice terraces, and vibrant cities like Manila, Cebu, and Davao.

**🗺️ World Geography**: From the Amazon rainforest to the Sahara desert, Mount Everest to the Mariana Trench - Earth's diversity is incredible.

**✈️ Travel Tips**: Research destinations, respect local cultures, try local foods, and create meaningful connections with people.

**🏛️ Cultural Sites**: UNESCO World Heritage sites preserve humanity's greatest cultural and natural treasures.

**🌏 Regional Focus**: Cagayan Valley (Region II) where MGB operates has beautiful landscapes, caves, and cultural heritage.

Where would you like to learn about or visit? I can share information about destinations, cultures, and travel experiences!`;
    }

    // Default general response
    return `🤖 **I'm here to help!**

I'm MGB Bot, your versatile AI assistant! I can help you with:

**📋 Document System**: Creating documents, tracking status, understanding workflows, and system navigation.

**🧠 General Knowledge**: Science, technology, history, culture, mathematics, health, programming, food, travel, and much more!

**💡 Problem Solving**: Breaking down complex questions, providing explanations, and offering practical solutions.

**🎯 Specific Topics**: Ask me about anything - from "How does photosynthesis work?" to "What's the best way to cook adobo?" to "Explain quantum physics!"

I'm designed to be helpful, informative, and engaging. What would you like to know or discuss? Feel free to ask me anything - no question is too random or off-topic!

**Examples of what you can ask:**
• "Explain how black holes work"
• "What's the history of the Philippines?"
• "How do I solve this math problem?"
• "Give me a recipe for pancit"
• "What's the weather like in different climates?"
• "How does artificial intelligence work?"

What's on your mind today?`;
  }

  /**
   * Generate contextual actions based on user message and context
   */
  private static generateContextualActions(
    message: string,
    context: ChatContext
  ): Array<{
    type: 'navigate' | 'search' | 'create' | 'help' | 'info';
    label: string;
    data?: any;
  }> {
    const lowerMessage = message.toLowerCase();
    const actions: Array<{
      type: 'navigate' | 'search' | 'create' | 'help' | 'info';
      label: string;
      data?: any;
    }> = [];

    // Document creation related
    if (lowerMessage.includes('create') || lowerMessage.includes('send') || lowerMessage.includes('document')) {
      actions.push({ type: 'navigate', label: '📝 Create Document', data: { url: '/documents/add' } });
    }

    // Navigation related
    if (lowerMessage.includes('dashboard') || lowerMessage.includes('overview')) {
      actions.push({ type: 'navigate', label: '📊 Dashboard', data: { url: '/dashboard' } });
    }

    // Inbox related
    if (lowerMessage.includes('inbox') || lowerMessage.includes('pending')) {
      actions.push({ type: 'navigate', label: '📥 Check Inbox', data: { url: '/documents?filter=inbox' } });
    }

    // Help related
    if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      actions.push({ type: 'help', label: '📚 Help Guide', data: { topic: 'help' } });
    }

    // Tour related
    if (lowerMessage.includes('tour') || lowerMessage.includes('quick tour')) {
      actions.push(
        { type: 'navigate', label: '📝 Try Creating Document', data: { url: '/documents/add' } },
        { type: 'navigate', label: '📥 Check Inbox', data: { url: '/documents?filter=inbox' } },
        { type: 'help', label: '🎯 System Features', data: { topic: 'features' } }
      );
    }

    // Features related
    if (lowerMessage.includes('features') || lowerMessage.includes('system features')) {
      actions.push(
        { type: 'navigate', label: '📊 Dashboard', data: { url: '/dashboard' } },
        { type: 'navigate', label: '📝 Create Document', data: { url: '/documents/add' } },
        { type: 'help', label: '🚀 Quick Tour', data: { topic: 'getting_started' } }
      );
    }

    // Science and Technology
    if (lowerMessage.includes('science') || lowerMessage.includes('technology') || lowerMessage.includes('ai')) {
      actions.push(
        { type: 'help', label: '🤖 More about AI', data: { topic: 'ai_details' } },
        { type: 'help', label: '🔬 Scientific Topics', data: { topic: 'science_topics' } },
        { type: 'help', label: '💻 Tech Trends', data: { topic: 'tech_trends' } }
      );
    }

    // Programming and Coding
    if (lowerMessage.includes('programming') || lowerMessage.includes('coding') || lowerMessage.includes('software')) {
      actions.push(
        { type: 'help', label: '🚀 Programming Languages', data: { topic: 'programming_languages' } },
        { type: 'help', label: '🌐 Web Development', data: { topic: 'web_development' } },
        { type: 'help', label: '📱 Mobile Development', data: { topic: 'mobile_development' } }
      );
    }

    // Food and Cooking
    if (lowerMessage.includes('food') || lowerMessage.includes('cooking') || lowerMessage.includes('recipe')) {
      actions.push(
        { type: 'help', label: '🇵🇭 Filipino Recipes', data: { topic: 'filipino_recipes' } },
        { type: 'help', label: '🍳 Cooking Tips', data: { topic: 'cooking_tips' } },
        { type: 'help', label: '🥗 Healthy Eating', data: { topic: 'healthy_eating' } }
      );
    }

    // Travel and Geography
    if (lowerMessage.includes('travel') || lowerMessage.includes('geography') || lowerMessage.includes('country')) {
      actions.push(
        { type: 'help', label: '🏝️ Philippines Travel', data: { topic: 'philippines_travel' } },
        { type: 'help', label: '🌍 World Geography', data: { topic: 'world_geography' } },
        { type: 'help', label: '✈️ Travel Tips', data: { topic: 'travel_tips' } }
      );
    }

    // Math and Education
    if (lowerMessage.includes('math') || lowerMessage.includes('calculate') || lowerMessage.includes('equation')) {
      actions.push(
        { type: 'help', label: '🔢 Math Help', data: { topic: 'math_help' } },
        { type: 'help', label: '📊 Statistics', data: { topic: 'statistics' } },
        { type: 'help', label: '💡 Problem Solving', data: { topic: 'problem_solving' } }
      );
    }

    // Health and Wellness
    if (lowerMessage.includes('health') || lowerMessage.includes('wellness') || lowerMessage.includes('fitness')) {
      actions.push(
        { type: 'help', label: '💪 Fitness Tips', data: { topic: 'fitness_tips' } },
        { type: 'help', label: '🥗 Nutrition Guide', data: { topic: 'nutrition_guide' } },
        { type: 'help', label: '🧠 Mental Health', data: { topic: 'mental_health' } }
      );
    }

    // Default actions if none specific
    if (actions.length === 0) {
      actions.push(
        { type: 'navigate', label: '📊 Dashboard', data: { url: '/dashboard' } },
        { type: 'navigate', label: '📝 Create Document', data: { url: '/documents/add' } },
        { type: 'help', label: '🧠 Ask Me Anything', data: { topic: 'general_help' } }
      );
    }

    return actions.slice(0, 3); // Limit to 3 actions
  }

  /**
   * Generate quick replies based on user message and context
   */
  private static generateQuickReplies(message: string, context: ChatContext): string[] {
    const lowerMessage = message.toLowerCase();
    const currentPage = context.currentPage || '';

    // Page-specific quick replies
    if (currentPage.includes('/documents/add')) {
      return [
        'How to fill document details?',
        'What document categories are available?',
        'How to select recipients?',
        'File upload requirements'
      ];
    } else if (currentPage.includes('/documents') && currentPage.includes('filter=inbox')) {
      return [
        'How to receive documents?',
        'Document status meanings',
        'How to process documents?',
        'Forwarding documents'
      ];
    } else if (currentPage.includes('/dashboard')) {
      return [
        'How to send documents?',
        'Check my inbox',
        'Document workflow',
        'System features'
      ];
    }

    // Message-specific quick replies
    if (lowerMessage.includes('tour') || lowerMessage.includes('quick tour')) {
      return [
        'How to create documents?',
        'Show me the dashboard',
        'What are document statuses?',
        'System features'
      ];
    } else if (lowerMessage.includes('features') || lowerMessage.includes('system features')) {
      return [
        'Document workflow process',
        'User roles and permissions',
        'Search and filtering',
        'Quick tour'
      ];
    } else if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      return [
        'How to send documents?',
        'Check document status',
        'System navigation',
        'Quick tour'
      ];
    } else if (lowerMessage.includes('document')) {
      return [
        'Create new document',
        'Check document status',
        'Document workflow',
        'File requirements'
      ];
    } else if (lowerMessage.includes('status') || lowerMessage.includes('track')) {
      return [
        'Check my documents',
        'Pending documents',
        'Document history',
        'Status meanings'
      ];
    }

    // Topic-specific quick replies
    if (lowerMessage.includes('science') || lowerMessage.includes('technology')) {
      return [
        'Tell me about artificial intelligence',
        'How does quantum computing work?',
        'Latest technology trends',
        'Science discoveries'
      ];
    } else if (lowerMessage.includes('programming') || lowerMessage.includes('coding')) {
      return [
        'Best programming languages to learn',
        'How to start web development?',
        'Mobile app development',
        'Database design tips'
      ];
    } else if (lowerMessage.includes('food') || lowerMessage.includes('cooking')) {
      return [
        'Filipino adobo recipe',
        'Healthy cooking tips',
        'World cuisine facts',
        'Cooking techniques'
      ];
    } else if (lowerMessage.includes('travel') || lowerMessage.includes('geography')) {
      return [
        'Best places in Philippines',
        'World geography facts',
        'Travel planning tips',
        'Cultural experiences'
      ];
    } else if (lowerMessage.includes('math') || lowerMessage.includes('calculate')) {
      return [
        'Solve math problems',
        'Statistics explained',
        'Algebra basics',
        'Geometry concepts'
      ];
    } else if (lowerMessage.includes('health') || lowerMessage.includes('wellness')) {
      return [
        'Fitness workout tips',
        'Healthy eating habits',
        'Mental wellness',
        'Preventive healthcare'
      ];
    } else if (lowerMessage.includes('history') || lowerMessage.includes('culture')) {
      return [
        'Philippine history',
        'World civilizations',
        'Cultural traditions',
        'Historical events'
      ];
    }

    // Default quick replies with mix of system and general topics
    return [
      'How to send documents?',
      'Ask me about science',
      'Tell me a fun fact',
      'System help'
    ];
  }

  /**
   * Analyze intent locally
   */
  private static analyzeIntentLocally(message: string): IntentAnalysisResult {
    const lowerMessage = message.toLowerCase();
    let intent = 'unknown';
    let confidence = 0.5;
    const entities: Array<{ type: string; value: string }> = [];
    const suggestions: string[] = [];

    // Intent classification
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      intent = 'greeting';
      confidence = 0.9;
    } else if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      intent = 'help_request';
      confidence = 0.8;
    } else if (lowerMessage.includes('create') || lowerMessage.includes('send') || lowerMessage.includes('new document')) {
      intent = 'document_create';
      confidence = 0.85;
    } else if (lowerMessage.includes('status') || lowerMessage.includes('track')) {
      intent = 'document_status';
      confidence = 0.8;
    } else if (lowerMessage.includes('find') || lowerMessage.includes('navigate') || lowerMessage.includes('where')) {
      intent = 'navigation_help';
      confidence = 0.75;
    }

    // Entity extraction
    const documentTypes = ['memo', 'letter', 'report', 'notice', 'directive'];
    documentTypes.forEach(type => {
      if (lowerMessage.includes(type)) {
        entities.push({ type: 'document_type', value: type.toUpperCase() });
      }
    });

    const divisions = ['ord', 'fad', 'mmd', 'msesdd', 'gsd'];
    divisions.forEach(division => {
      if (lowerMessage.includes(division)) {
        entities.push({ type: 'division', value: division.toUpperCase() });
      }
    });

    // DTN pattern
    const dtnPattern = /mgbr2-\d{4}-\d{4}-\d{4}/i;
    const dtnMatch = message.match(dtnPattern);
    if (dtnMatch) {
      entities.push({ type: 'document_tracking_number', value: dtnMatch[0].toUpperCase() });
    }

    // Generate suggestions based on intent
    switch (intent) {
      case 'document_create':
        suggestions.push('Show me how to create a document', 'What document types are available?');
        break;
      case 'document_status':
        suggestions.push('Check my pending documents', 'Explain document statuses');
        break;
      case 'navigation_help':
        suggestions.push('Show me the dashboard', 'How do I access my inbox?');
        break;
      default:
        suggestions.push('How can I help you?', 'Tell me about the system features');
    }

    return {
      intent,
      confidence,
      entities,
      suggestions
    };
  }

  /**
   * Generate local contextual help
   */
  private static generateLocalContextualHelp(context: ChatContext): ContextualHelpResult {
    const currentPage = context.currentPage || '';
    const userRole = context.userRole || 'EMPLOYEE';
    const pendingDocs = context.pendingDocuments || 0;

    let helpMessage = 'Here to help you navigate the document tracking system.';
    const quickActions: any[] = [];
    const tutorials: any[] = [];

    // Page-specific help
    if (currentPage.includes('/documents/add')) {
      helpMessage = 'You\'re creating a new document. Fill in all required fields and select the appropriate recipient.';
      quickActions.push(
        { label: 'Document Categories', action: 'show_categories', description: 'Learn about document types' },
        { label: 'Action Types', action: 'show_actions', description: 'Understand document actions' }
      );
      tutorials.push({
        title: 'Creating Documents',
        steps: [
          'Enter a clear, descriptive title',
          'Provide detailed description',
          'Select appropriate category',
          'Choose recipient and division',
          'Select required action',
          'Upload file if needed',
          'Review and send'
        ]
      });
    } else if (currentPage.includes('/documents') && currentPage.includes('filter=inbox')) {
      helpMessage = `You have ${pendingDocs} pending documents in your inbox. Click "Receive" to accept them.`;
      quickActions.push(
        { label: 'Receive All', action: 'receive_all', description: 'Accept all pending documents' },
        { label: 'Sort by Priority', action: 'sort_priority', description: 'View urgent documents first' }
      );
      tutorials.push({
        title: 'Managing Inbox',
        steps: [
          'Review document details',
          'Click "Receive" to accept',
          'Process when ready',
          'Forward if needed',
          'Archive when complete'
        ]
      });
    } else if (currentPage.includes('/dashboard')) {
      helpMessage = 'Your dashboard shows document statistics and recent activity. Use the sidebar to navigate.';
      quickActions.push(
        { label: 'Quick Send', action: 'quick_send', description: 'Send a document quickly' },
        { label: 'Check Inbox', action: 'check_inbox', description: 'View pending documents' }
      );
    }

    // Role-specific help
    if (userRole === 'ADMIN' || userRole === 'REGIONAL_DIRECTOR') {
      quickActions.push(
        { label: 'Admin Panel', action: 'admin_panel', description: 'Access administrative functions' },
        { label: 'System Reports', action: 'system_reports', description: 'View system analytics' }
      );
    }

    return {
      helpMessage,
      quickActions,
      tutorials
    };
  }
}
