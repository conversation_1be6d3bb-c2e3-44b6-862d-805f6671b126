/**
 * Chatbot Service
 * Handles AI-powered chatbot interactions and responses
 */

import { geminiClient } from '../core/geminiClient';
import {
  ChatMessage,
  ChatContext,
  IntentAnalysisResult,
  ContextualHelpResult
} from '../core/types';

export class ChatbotService {
  /**
   * Generate chat response using Gemini AI
   * @param message User message
   * @param context Chat context
   * @param conversationHistory Previous messages
   * @returns AI-generated response
   */
  static async generateChatResponse(
    message: string,
    context: ChatContext,
    conversationHistory: ChatMessage[] = []
  ): Promise<{
    message: string;
    actions?: Array<{
      type: 'navigate' | 'search' | 'create' | 'help' | 'info';
      label: string;
      data?: any;
    }>;
    quickReplies?: string[];
  }> {
    try {
      const prompt = this.createChatPrompt(message, context, conversationHistory);

      if (geminiClient.isAvailable()) {
        try {
          const response = await geminiClient.generateContent(prompt);
          return this.formatChatResponse(response, message, context);
        } catch (apiError) {
          console.warn('Gemini API failed for chat, using fallback:', apiError);
        }
      }

      // Fallback to local generation
      return this.generateLocalChatResponse(message, context);
    } catch (error) {
      console.error('Error generating chat response:', error);
      throw error;
    }
  }

  /**
   * Analyze user intent from message
   * @param message User message
   * @returns Intent analysis result
   */
  static async analyzeIntent(message: string): Promise<IntentAnalysisResult> {
    try {
      if (geminiClient.isAvailable()) {
        try {
          const prompt = this.createIntentAnalysisPrompt(message);
          const response = await geminiClient.generateContent(prompt);
          return this.parseIntentAnalysis(response);
        } catch (apiError) {
          console.warn('Gemini API failed for intent analysis, using fallback:', apiError);
        }
      }

      // Fallback to local analysis
      return this.analyzeIntentLocally(message);
    } catch (error) {
      console.error('Error analyzing intent:', error);
      throw error;
    }
  }

  /**
   * Generate contextual help
   * @param context Current context
   * @returns Contextual help information
   */
  static async generateContextualHelp(context: ChatContext): Promise<ContextualHelpResult> {
    try {
      if (geminiClient.isAvailable()) {
        try {
          const prompt = this.createContextualHelpPrompt(context);
          const response = await geminiClient.generateContent(prompt);
          return this.parseContextualHelp(response);
        } catch (apiError) {
          console.warn('Gemini API failed for contextual help, using fallback:', apiError);
        }
      }

      // Fallback to local generation
      return this.generateLocalContextualHelp(context);
    } catch (error) {
      console.error('Error generating contextual help:', error);
      throw error;
    }
  }

  /**
   * Create chat prompt for Gemini API
   */
  private static createChatPrompt(
    message: string,
    context: ChatContext,
    conversationHistory: ChatMessage[]
  ): string {
    const historyText = conversationHistory
      .slice(-5) // Last 5 messages for context
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    return `
You are MGB Bot, an AI assistant for the MGB Document Tracking System.

CONTEXT:
- Current Page: ${context.currentPage || 'Unknown'}
- User Role: ${context.userRole || 'Unknown'}
- User Division: ${context.userDivision || 'Unknown'}
- Pending Documents: ${context.pendingDocuments || 0}

CONVERSATION HISTORY:
${historyText}

USER MESSAGE: ${message}

Provide a helpful, concise response that:
1. Addresses the user's question or request
2. Considers their role and current context
3. Offers specific, actionable guidance
4. Uses a friendly, professional tone
5. Stays focused on document tracking system features

If the user asks about:
- Document creation: Guide them through the process
- Document status: Explain the workflow and statuses
- Navigation: Help them find the right page or feature
- Troubleshooting: Provide step-by-step solutions
- System features: Explain functionality clearly

Keep responses under 200 words and be conversational.
`;
  }

  /**
   * Create intent analysis prompt
   */
  private static createIntentAnalysisPrompt(message: string): string {
    return `
Analyze the user's intent from this message: "${message}"

Classify the intent and extract entities. Respond in JSON format:
{
  "intent": "intent_name",
  "confidence": 0.85,
  "entities": [
    {"type": "entity_type", "value": "entity_value"}
  ],
  "suggestions": ["suggestion1", "suggestion2"]
}

Common intents:
- document_create, document_search, document_status
- navigation_help, feature_explanation, troubleshooting
- greeting, goodbye, help_request
`;
  }

  /**
   * Create contextual help prompt
   */
  private static createContextualHelpPrompt(context: ChatContext): string {
    return `
Generate contextual help for a user in the Document Tracking System.

CONTEXT:
- Current Page: ${context.currentPage || 'Unknown'}
- User Role: ${context.userRole || 'Unknown'}
- Pending Documents: ${context.pendingDocuments || 0}

Provide help in JSON format:
{
  "helpMessage": "Contextual help message",
  "quickActions": [
    {"label": "Action", "action": "action_id", "description": "What it does"}
  ],
  "tutorials": [
    {"title": "Tutorial", "steps": ["Step 1", "Step 2"]}
  ]
}
`;
  }

  /**
   * Parse intent analysis response
   */
  private static parseIntentAnalysis(response: string): IntentAnalysisResult {
    try {
      const parsed = JSON.parse(response);
      return {
        intent: parsed.intent || 'unknown',
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || [],
        suggestions: parsed.suggestions || []
      };
    } catch (error) {
      return {
        intent: 'unknown',
        confidence: 0.5,
        entities: [],
        suggestions: []
      };
    }
  }

  /**
   * Parse contextual help response
   */
  private static parseContextualHelp(response: string): ContextualHelpResult {
    try {
      const parsed = JSON.parse(response);
      return {
        helpMessage: parsed.helpMessage || 'How can I help you today?',
        quickActions: parsed.quickActions || [],
        tutorials: parsed.tutorials || []
      };
    } catch (error) {
      return this.generateLocalContextualHelp({});
    }
  }

  /**
   * Format chat response from Gemini API
   */
  private static formatChatResponse(
    response: string,
    userMessage: string,
    context: ChatContext
  ): {
    message: string;
    actions?: Array<{
      type: 'navigate' | 'search' | 'create' | 'help' | 'info';
      label: string;
      data?: any;
    }>;
    quickReplies?: string[];
  } {
    // Generate contextual actions and quick replies based on the response and context
    const actions = this.generateContextualActions(userMessage, context);
    const quickReplies = this.generateQuickReplies(userMessage, context);

    return {
      message: response || 'I apologize, but I\'m having trouble generating a response right now. How can I help you with the document tracking system?',
      actions,
      quickReplies
    };
  }

  /**
   * Generate local chat response fallback
   */
  private static generateLocalChatResponse(
    message: string,
    context: ChatContext
  ): {
    message: string;
    actions?: Array<{
      type: 'navigate' | 'search' | 'create' | 'help' | 'info';
      label: string;
      data?: any;
    }>;
    quickReplies?: string[];
  } {
    const lowerMessage = message.toLowerCase();
    let responseMessage = '';

    // Greeting responses
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
      responseMessage = `Hello! I'm MGB Bot, your document tracking assistant. I can help you with creating documents, checking statuses, navigating the system, and more. What would you like to do today?`;
    }
    // Help requests
    else if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      if (context.currentPage?.includes('documents/add')) {
        responseMessage = `I can help you create a document! Fill in the title, description, select a category and recipient, then attach your file. Make sure all required fields are completed before sending.`;
      } else if (context.currentPage?.includes('documents')) {
        responseMessage = `You're viewing your documents. Use the filters (Inbox, Sent, Pending, etc.) to organize them. Click on any document to view details, or use the "Send Document" button to create a new one.`;
      } else {
        responseMessage = `I can help you with:
• Creating and sending documents
• Checking document status and tracking
• Understanding the document workflow
• Navigating the system
• Managing your inbox

What specific task would you like help with?`;
      }
    }
    // Document-related queries
    else if (lowerMessage.includes('document') || lowerMessage.includes('send') || lowerMessage.includes('create')) {
      responseMessage = `To create a document:
1. Click "Send Document" or go to Documents → Add New
2. Fill in the title and description
3. Select the document category
4. Choose the recipient and their division
5. Select the required action
6. Attach your file
7. Review and send

Need help with any specific step?`;
    }
    // Status queries
    else if (lowerMessage.includes('status') || lowerMessage.includes('track')) {
      responseMessage = `Document statuses in our system:
• Pending: Waiting to be received
• Received: Accepted by recipient
• Processed: Action completed
• Forwarded: Sent to another person
• Archived: Filed away

You can track any document using its DTN (Document Tracking Number) or check your dashboard for status updates.`;
    }
    // Navigation help
    else if (lowerMessage.includes('navigate') || lowerMessage.includes('find') || lowerMessage.includes('where')) {
      responseMessage = `Here's how to navigate:
• Dashboard: Overview and recent activity
• Documents: View all your documents with filters
• Send Document: Create new documents
• Notifications: Check alerts and updates
• Settings: Customize your preferences

Use the sidebar menu to access these sections. What are you looking for?`;
    }
    // Default response
    else {
      responseMessage = `I'm here to help with the document tracking system! You can ask me about:
• Creating and sending documents
• Checking document status
• Understanding the workflow
• System navigation
• Troubleshooting issues

What would you like to know?`;
    }

    // Generate contextual actions and quick replies
    const actions = this.generateContextualActions(message, context);
    const quickReplies = this.generateQuickReplies(message, context);

    return {
      message: responseMessage,
      actions,
      quickReplies
    };
  }

  /**
   * Generate contextual actions based on user message and context
   */
  private static generateContextualActions(
    message: string,
    context: ChatContext
  ): Array<{
    type: 'navigate' | 'search' | 'create' | 'help' | 'info';
    label: string;
    data?: any;
  }> {
    const lowerMessage = message.toLowerCase();
    const actions: Array<{
      type: 'navigate' | 'search' | 'create' | 'help' | 'info';
      label: string;
      data?: any;
    }> = [];

    // Document creation related
    if (lowerMessage.includes('create') || lowerMessage.includes('send') || lowerMessage.includes('document')) {
      actions.push({ type: 'navigate', label: '📝 Create Document', data: { url: '/documents/add' } });
    }

    // Navigation related
    if (lowerMessage.includes('dashboard') || lowerMessage.includes('overview')) {
      actions.push({ type: 'navigate', label: '📊 Dashboard', data: { url: '/dashboard' } });
    }

    // Inbox related
    if (lowerMessage.includes('inbox') || lowerMessage.includes('pending')) {
      actions.push({ type: 'navigate', label: '📥 Check Inbox', data: { url: '/documents?filter=inbox' } });
    }

    // Help related
    if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      actions.push({ type: 'help', label: '📚 Help Guide', data: { topic: 'help' } });
    }

    // Default actions if none specific
    if (actions.length === 0) {
      actions.push(
        { type: 'navigate', label: '📊 Dashboard', data: { url: '/dashboard' } },
        { type: 'navigate', label: '📝 Create Document', data: { url: '/documents/add' } },
        { type: 'help', label: '📚 Help Guide', data: { topic: 'help' } }
      );
    }

    return actions.slice(0, 3); // Limit to 3 actions
  }

  /**
   * Generate quick replies based on user message and context
   */
  private static generateQuickReplies(message: string, context: ChatContext): string[] {
    const lowerMessage = message.toLowerCase();
    const currentPage = context.currentPage || '';

    // Page-specific quick replies
    if (currentPage.includes('/documents/add')) {
      return [
        'How to fill document details?',
        'What document categories are available?',
        'How to select recipients?',
        'File upload requirements'
      ];
    } else if (currentPage.includes('/documents') && currentPage.includes('filter=inbox')) {
      return [
        'How to receive documents?',
        'Document status meanings',
        'How to process documents?',
        'Forwarding documents'
      ];
    } else if (currentPage.includes('/dashboard')) {
      return [
        'How to send documents?',
        'Check my inbox',
        'Document workflow',
        'System features'
      ];
    }

    // Message-specific quick replies
    if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      return [
        'How to send documents?',
        'Check document status',
        'System navigation',
        'Troubleshooting'
      ];
    } else if (lowerMessage.includes('document')) {
      return [
        'Create new document',
        'Check document status',
        'Document workflow',
        'File requirements'
      ];
    } else if (lowerMessage.includes('status') || lowerMessage.includes('track')) {
      return [
        'Check my documents',
        'Pending documents',
        'Document history',
        'Status meanings'
      ];
    }

    // Default quick replies
    return [
      'How to send documents?',
      'Check my inbox',
      'Document workflow',
      'System help'
    ];
  }

  /**
   * Analyze intent locally
   */
  private static analyzeIntentLocally(message: string): IntentAnalysisResult {
    const lowerMessage = message.toLowerCase();
    let intent = 'unknown';
    let confidence = 0.5;
    const entities: Array<{ type: string; value: string }> = [];
    const suggestions: string[] = [];

    // Intent classification
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      intent = 'greeting';
      confidence = 0.9;
    } else if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
      intent = 'help_request';
      confidence = 0.8;
    } else if (lowerMessage.includes('create') || lowerMessage.includes('send') || lowerMessage.includes('new document')) {
      intent = 'document_create';
      confidence = 0.85;
    } else if (lowerMessage.includes('status') || lowerMessage.includes('track')) {
      intent = 'document_status';
      confidence = 0.8;
    } else if (lowerMessage.includes('find') || lowerMessage.includes('navigate') || lowerMessage.includes('where')) {
      intent = 'navigation_help';
      confidence = 0.75;
    }

    // Entity extraction
    const documentTypes = ['memo', 'letter', 'report', 'notice', 'directive'];
    documentTypes.forEach(type => {
      if (lowerMessage.includes(type)) {
        entities.push({ type: 'document_type', value: type.toUpperCase() });
      }
    });

    const divisions = ['ord', 'fad', 'mmd', 'msesdd', 'gsd'];
    divisions.forEach(division => {
      if (lowerMessage.includes(division)) {
        entities.push({ type: 'division', value: division.toUpperCase() });
      }
    });

    // DTN pattern
    const dtnPattern = /mgbr2-\d{4}-\d{4}-\d{4}/i;
    const dtnMatch = message.match(dtnPattern);
    if (dtnMatch) {
      entities.push({ type: 'document_tracking_number', value: dtnMatch[0].toUpperCase() });
    }

    // Generate suggestions based on intent
    switch (intent) {
      case 'document_create':
        suggestions.push('Show me how to create a document', 'What document types are available?');
        break;
      case 'document_status':
        suggestions.push('Check my pending documents', 'Explain document statuses');
        break;
      case 'navigation_help':
        suggestions.push('Show me the dashboard', 'How do I access my inbox?');
        break;
      default:
        suggestions.push('How can I help you?', 'Tell me about the system features');
    }

    return {
      intent,
      confidence,
      entities,
      suggestions
    };
  }

  /**
   * Generate local contextual help
   */
  private static generateLocalContextualHelp(context: ChatContext): ContextualHelpResult {
    const currentPage = context.currentPage || '';
    const userRole = context.userRole || 'EMPLOYEE';
    const pendingDocs = context.pendingDocuments || 0;

    let helpMessage = 'Here to help you navigate the document tracking system.';
    const quickActions: any[] = [];
    const tutorials: any[] = [];

    // Page-specific help
    if (currentPage.includes('/documents/add')) {
      helpMessage = 'You\'re creating a new document. Fill in all required fields and select the appropriate recipient.';
      quickActions.push(
        { label: 'Document Categories', action: 'show_categories', description: 'Learn about document types' },
        { label: 'Action Types', action: 'show_actions', description: 'Understand document actions' }
      );
      tutorials.push({
        title: 'Creating Documents',
        steps: [
          'Enter a clear, descriptive title',
          'Provide detailed description',
          'Select appropriate category',
          'Choose recipient and division',
          'Select required action',
          'Upload file if needed',
          'Review and send'
        ]
      });
    } else if (currentPage.includes('/documents') && currentPage.includes('filter=inbox')) {
      helpMessage = `You have ${pendingDocs} pending documents in your inbox. Click "Receive" to accept them.`;
      quickActions.push(
        { label: 'Receive All', action: 'receive_all', description: 'Accept all pending documents' },
        { label: 'Sort by Priority', action: 'sort_priority', description: 'View urgent documents first' }
      );
      tutorials.push({
        title: 'Managing Inbox',
        steps: [
          'Review document details',
          'Click "Receive" to accept',
          'Process when ready',
          'Forward if needed',
          'Archive when complete'
        ]
      });
    } else if (currentPage.includes('/dashboard')) {
      helpMessage = 'Your dashboard shows document statistics and recent activity. Use the sidebar to navigate.';
      quickActions.push(
        { label: 'Quick Send', action: 'quick_send', description: 'Send a document quickly' },
        { label: 'Check Inbox', action: 'check_inbox', description: 'View pending documents' }
      );
    }

    // Role-specific help
    if (userRole === 'ADMIN' || userRole === 'REGIONAL_DIRECTOR') {
      quickActions.push(
        { label: 'Admin Panel', action: 'admin_panel', description: 'Access administrative functions' },
        { label: 'System Reports', action: 'system_reports', description: 'View system analytics' }
      );
    }

    return {
      helpMessage,
      quickActions,
      tutorials
    };
  }
}
