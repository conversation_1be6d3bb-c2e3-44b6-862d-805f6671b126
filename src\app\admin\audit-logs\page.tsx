import { getCurrentUser } from '@/utils/auth';
import { redirect } from 'next/navigation';
import { UserRole } from '@/types';
import AuditLogList from '@/components/admin/AuditLogList';

export default async function AuditLogsPage() {
  const user = await getCurrentUser();

  if (!user || (user.role !== UserRole.ADMIN && user.role !== UserRole.REGIONAL_DIRECTOR)) {
    redirect('/dashboard');
  }

  return (
    <>
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">System Audit Logs</h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Track and monitor all system activities and user actions
            </p>
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Welcome, <span className="font-medium">{user.name}</span>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Activity History</h2>
          <p className="mt-1 sm:mt-0 text-sm text-gray-500 dark:text-gray-400">
            Showing the most recent activities first
          </p>
        </div>
        <AuditLogList />
      </div>
    </>
  );
}
