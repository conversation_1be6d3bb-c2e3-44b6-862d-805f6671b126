# IMPORTANT: Replace this with your actual MongoDB Atlas connection string
# You need to copy it from MongoDB Atlas > Database > Connect > Connect your application
# Make sure to include your actual cluster identifier (the xxxxx part in cluster0.xxxxx.mongodb.net)
# And replace <username> and <password> with your actual database credentials

# LOCAL MONGODB (Default - if you have MongoDB running locally)
MONGODB_URI=mongodb://localhost:27017/document-tracker

# MONGODB ATLAS (Cloud - uncomment and replace with your connection string)
# MONGODB_URI=mongodb+srv://username:<EMAIL>/document-tracker?retryWrites=true&w=majority
# NEXTAUTH_URL is required to avoid warnings
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET="baEtGk41khNuFOH/Qg39VYjj5s5wekLKo/ua0VoWXhY=" # Added by `npx auth`. Read more: https://cli.authjs.dev
# Set DEBUG to false to avoid warnings
DEBUG=false
NEXTAUTH_DEBUG=false

# File Storage Configuration
# Maximum file size in bytes (default: 10MB)
MAX_FILE_SIZE=10485760

# Local file storage directory (optional, defaults to 'public/uploads')
# UPLOADS_DIRECTORY=C:/document-tracker-files

# Google Gemini API Key
# Get your API key from https://makersuite.google.com/app/apikey
# Replace with your new API key from https://makersuite.google.com/app/apikey
# Make sure you have enabled the Gemini Pro API for this key
GEMINI_API_KEY=AIzaSyBzt34MWkmewc4hho80mKOZyQrnPH5YOW4

# NOTE: If AI features aren't working, get a new API key from:
# https://makersuite.google.com/app/apikey
# Then replace the key above and restart the application
