{"version": 3, "file": "webauthn.d.ts", "sourceRoot": "", "sources": ["../src/providers/webauthn.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,6BAA6B,EAC7B,2BAA2B,EAC3B,4BAA4B,EAC5B,0BAA0B,EAC3B,MAAM,wBAAwB,CAAA;AAG/B,OAAO,KAAK,EAAE,qBAAqB,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AACxE,OAAO,KAAK,EACV,+BAA+B,EAC/B,iCAAiC,EACjC,gCAAgC,EAChC,8BAA8B,EAC/B,MAAM,wBAAwB,CAAA;AAE/B,OAAO,KAAK,EACV,eAAe,EACf,eAAe,EACf,YAAY,EACZ,IAAI,EACL,MAAM,aAAa,CAAA;AAEpB,MAAM,MAAM,oBAAoB,GAAG,UAAU,CAAA;AAE7C,eAAO,MAAM,wBAAwB,QAAgB,CAAA;AACrD,eAAO,MAAM,sCAAsC,EAAE,YAAuB,CAAA;AAE5E,MAAM,MAAM,aAAa,GAAG;IAC1B,wDAAwD;IACxD,EAAE,EAAE,MAAM,CAAA;IACV,mDAAmD;IACnD,IAAI,EAAE,MAAM,CAAA;IACZ,uDAAuD;IACvD,MAAM,EAAE,MAAM,CAAA;CACf,CAAA;AAED,KAAK,kBAAkB,GAAG;IACxB,wDAAwD;IACxD,EAAE,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;IACrB,mDAAmD;IACnD,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;IACvB,uDAAuD;IACvD,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;CAC1B,CAAA;AAED,MAAM,MAAM,WAAW,GAAG,CACxB,OAAO,EAAE,eAAe,CAAC,oBAAoB,CAAC,EAC9C,OAAO,EAAE,eAAe,KACrB,OAAO,CACR;IAAE,IAAI,EAAE,IAAI,CAAC;IAAC,MAAM,EAAE,IAAI,CAAA;CAAE,GAC5B;IAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAAC,MAAM,EAAE,KAAK,CAAA;CAAE,GACzC,IAAI,CACP,CAAA;AAED,KAAK,iCAAiC,GAAG,IAAI,CAC3C,iCAAiC,EACjC,MAAM,GAAG,kBAAkB,GAAG,WAAW,CAC1C,CAAA;AACD,KAAK,+BAA+B,GAAG,IAAI,CACzC,+BAA+B,EAC7B,QAAQ,GACR,MAAM,GACN,QAAQ,GACR,UAAU,GACV,WAAW,GACX,iBAAiB,GACjB,oBAAoB,CACvB,CAAA;AACD,KAAK,uCAAuC,GAAG,IAAI,CACjD,gCAAgC,EAC9B,mBAAmB,GACnB,gBAAgB,GAChB,cAAc,GACd,eAAe,GACf,UAAU,CACb,CAAA;AACD,KAAK,qCAAqC,GAAG,IAAI,CAC/C,8BAA8B,EAC9B,mBAAmB,GAAG,gBAAgB,GAAG,cAAc,GAAG,UAAU,CACrE,CAAA;AAED,MAAM,WAAW,cAAe,SAAQ,qBAAqB;IAC3D,IAAI,EAAE,oBAAoB,CAAA;IAC1B;;;;QAII;IACJ,aAAa,CAAC,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAA;IAC3C;;OAEG;IACH,gBAAgB,EAAE,CAChB,OAAO,EAAE,eAAe,CAAC,oBAAoB,CAAC,EAC9C,OAAO,EAAE,eAAe,KACrB,aAAa,CAAA;IAClB;;;;OAIG;IACH,mBAAmB,EAAE,OAAO,CAAA;IAC5B;;;;;OAKG;IACH,4BAA4B,EAAE,YAAY,GAAG,KAAK,CAAA;IAClD;;;;OAIG;IACH,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,CAAA;IAC3C;;OAEG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC,iCAAiC,CAAC,CAAA;IAClE;;OAEG;IACH,mBAAmB,EAAE,OAAO,CAAC,+BAA+B,CAAC,CAAA;IAC7D;;OAEG;IACH,2BAA2B,CAAC,EAAE,OAAO,CAAC,uCAAuC,CAAC,CAAA;IAC9E;;OAEG;IACH,yBAAyB,CAAC,EAAE,OAAO,CAAC,qCAAqC,CAAC,CAAA;IAC1E;;;;;;;;;;;;;;;;OAgBG;IACH,WAAW,EAAE,WAAW,CAAA;IACxB,0EAA0E;IAC1E,cAAc,EAAE;QACd,4BAA4B,EAAE,OAAO,4BAA4B,CAAA;QACjE,0BAA0B,EAAE,OAAO,0BAA0B,CAAA;QAC7D,6BAA6B,EAAE,OAAO,6BAA6B,CAAA;QACnE,2BAA2B,EAAE,OAAO,2BAA2B,CAAA;KAChE,CAAA;CACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,MAAM,CAAC,OAAO,UAAU,QAAQ,CAC9B,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,GAC9B,cAAc,CA0BhB"}