"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_PhilippineCalendar_tsx";
exports.ids = ["_ssr_src_components_PhilippineCalendar_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/PhilippineCalendar.tsx":
/*!***********************************************!*\
  !*** ./src/components/PhilippineCalendar.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PhilippineCalendar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_calendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-calendar */ \"(ssr)/./node_modules/react-calendar/dist/esm/index.js\");\n/* harmony import */ var _styles_calendar_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/calendar.css */ \"(ssr)/./src/styles/calendar.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Import our custom CSS instead of the full library CSS\n\nfunction PhilippineCalendar() {\n    const [date, setDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [holidays, setHolidays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedHoliday, setSelectedHoliday] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Create a cache key based on the year\n        const cacheKey = `philippine_holidays_${date.getFullYear()}`;\n        const fetchHolidays = async ()=>{\n            try {\n                // Check if we have cached data\n                const cachedData = localStorage.getItem(cacheKey);\n                if (cachedData) {\n                    try {\n                        const parsedData = JSON.parse(cachedData);\n                        setHolidays(parsedData);\n                        setLoading(false);\n                        return;\n                    } catch (e) {\n                        // If parsing fails, continue with the fetch\n                        console.error(\"Error parsing cached holiday data:\", e);\n                    }\n                }\n                setLoading(true);\n                const year = date.getFullYear();\n                const controller = new AbortController();\n                const signal = controller.signal;\n                const response = await fetch(`/api/calendar/holidays?year=${year}`, {\n                    signal,\n                    headers: {\n                        \"Cache-Control\": \"max-age=86400\"\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch holidays\");\n                }\n                const data = await response.json();\n                setHolidays(data.holidays);\n                // Cache the data in localStorage\n                try {\n                    localStorage.setItem(cacheKey, JSON.stringify(data.holidays));\n                } catch (e) {\n                    console.error(\"Error caching holiday data:\", e);\n                }\n            } catch (error) {\n                if (error instanceof Error && error.name !== \"AbortError\") {\n                    console.error(\"Error fetching holidays:\", error);\n                }\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchHolidays();\n        // Cleanup function to abort fetch if component unmounts\n        return ()=>{\n        // AbortController cleanup would go here if we had a reference to it\n        };\n    }, [\n        date\n    ]);\n    const isHoliday = (date)=>{\n        const formattedDate = date.toISOString().split(\"T\")[0];\n        return holidays.find((holiday)=>holiday.date === formattedDate);\n    };\n    const tileClassName = ({ date, view })=>{\n        if (view === \"month\") {\n            const holiday = isHoliday(date);\n            if (holiday) {\n                if (holiday.type === \"Regular Holiday\") {\n                    return \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-full font-medium\";\n                } else if (holiday.type === \"Special Non-working Holiday\") {\n                    return \"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full font-medium\";\n                } else if (holiday.type === \"Special Working Day\") {\n                    return \"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded-full font-medium\";\n                } else {\n                    return \"bg-gray-100 dark:bg-gray-700/50 text-gray-800 dark:text-gray-300 rounded-full font-medium\";\n                }\n            }\n        }\n        return null;\n    };\n    const handleDateChange = (value)=>{\n        if (!value) return;\n        let newDate;\n        if (value instanceof Date) {\n            newDate = value;\n        } else if (Array.isArray(value)) {\n            if (value[0] instanceof Date) {\n                newDate = value[0];\n            } else {\n                return; // Invalid date\n            }\n        } else {\n            return; // Invalid value\n        }\n        // Check if year has changed\n        const yearChanged = date.getFullYear() !== newDate.getFullYear();\n        setDate(newDate);\n        // If year changed, we'll trigger a refetch via the useEffect\n        // Otherwise, just update the selected holiday\n        if (!yearChanged) {\n            const holiday = isHoliday(newDate);\n            setSelectedHoliday(holiday || null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"calendar-container\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 dark:border-primary-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_calendar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onChange: handleDateChange,\n                    value: date,\n                    tileClassName: tileClassName,\n                    className: \"rounded-lg border-0 w-full\"\n                }, `calendar-${date.getFullYear()}`, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            selectedHoliday && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 document-card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"document-icon bg-accent-100 dark:bg-accent-900/30 text-accent-600 dark:text-accent-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-5 w-5\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-4 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"document-title\",\n                                    children: selectedHoliday.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"document-meta flex items-center mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 dark:text-gray-400\",\n                                            children: selectedHoliday.type\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mx-2 text-gray-300 dark:text-gray-600\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 dark:text-gray-400\",\n                                            children: selectedHoliday.date\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 grid grid-cols-2 gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-3 h-3 rounded-full mr-2 bg-red-500/30 dark:bg-red-500/30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: \"Regular Holiday\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-3 h-3 rounded-full mr-2 bg-blue-500/30 dark:bg-blue-500/30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: \"Special Non-working\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-3 h-3 rounded-full mr-2 bg-yellow-500/30 dark:bg-yellow-500/30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: \"Special Working\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-3 h-3 rounded-full mr-2 bg-gray-500/30 dark:bg-gray-500/30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: \"Other Holidays\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\PhilippineCalendar.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PhilippineCalendar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/styles/calendar.css":
/*!*********************************!*\
  !*** ./src/styles/calendar.css ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2cb360e5ede4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3R5bGVzL2NhbGVuZGFyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2RvY3VtZW50LXRyYWNrZXIvLi9zcmMvc3R5bGVzL2NhbGVuZGFyLmNzcz9jMjg5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmNiMzYwZTVlZGU0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/calendar.css\n");

/***/ })

};
;