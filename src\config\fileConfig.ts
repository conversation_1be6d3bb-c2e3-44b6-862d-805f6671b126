/**
 * File Configuration
 * 
 * Centralized configuration for file handling in the document tracking system.
 * This allows easy adjustment of file size limits and supported file types.
 */

// File size constants (in bytes)
export const FILE_SIZE_LIMITS = {
  // Standard limits
  SMALL: 5 * 1024 * 1024,      // 5MB
  MEDIUM: 25 * 1024 * 1024,    // 25MB
  LARGE: 50 * 1024 * 1024,     // 50MB
  EXTRA_LARGE: 100 * 1024 * 1024, // 100MB
  
  // Custom limits for specific use cases
  DOCUMENT_DEFAULT: 100 * 1024 * 1024, // 100MB for documents
  IMAGE_DEFAULT: 10 * 1024 * 1024,     // 10MB for images
  ARCHIVE_DEFAULT: 200 * 1024 * 1024,  // 200MB for archives
} as const;

// File size limits in MB for easy display
export const FILE_SIZE_LIMITS_MB = {
  SMALL: 5,
  MEDIUM: 25,
  LARGE: 50,
  EXTRA_LARGE: 100,
  DOCUMENT_DEFAULT: 100,
  IMAGE_DEFAULT: 10,
  ARCHIVE_DEFAULT: 200,
} as const;

// Supported file types by category
export const SUPPORTED_FILE_TYPES = {
  DOCUMENTS: [
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp'
  ],
  IMAGES: [
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'svg'
  ],
  ARCHIVES: [
    'zip', 'rar', '7z', 'tar', 'gz'
  ],
  ALL: [] as string[] // Will be populated below
} as const;

// Populate ALL with all supported types
SUPPORTED_FILE_TYPES.ALL = [
  ...SUPPORTED_FILE_TYPES.DOCUMENTS,
  ...SUPPORTED_FILE_TYPES.IMAGES,
  ...SUPPORTED_FILE_TYPES.ARCHIVES
];

// File type descriptions for user display
export const FILE_TYPE_DESCRIPTIONS = {
  DOCUMENTS: 'PDF, Word, Excel, PowerPoint, Text files',
  IMAGES: 'JPEG, PNG, GIF, WebP, and other image formats',
  ARCHIVES: 'ZIP, RAR, 7Z, and other compressed files',
  ALL: 'Documents, Images, and Archive files'
} as const;

// Configuration presets for different use cases
export const FILE_CONFIG_PRESETS = {
  // Standard document upload (most common use case)
  STANDARD_DOCUMENT: {
    maxSizeInBytes: FILE_SIZE_LIMITS.DOCUMENT_DEFAULT,
    maxSizeInMB: FILE_SIZE_LIMITS_MB.DOCUMENT_DEFAULT,
    allowedFileTypes: SUPPORTED_FILE_TYPES.ALL,
    description: 'Standard document upload (up to 100MB)'
  },
  
  // Large document upload (for scanned documents, large PDFs)
  LARGE_DOCUMENT: {
    maxSizeInBytes: FILE_SIZE_LIMITS.ARCHIVE_DEFAULT,
    maxSizeInMB: FILE_SIZE_LIMITS_MB.ARCHIVE_DEFAULT,
    allowedFileTypes: SUPPORTED_FILE_TYPES.ALL,
    description: 'Large document upload (up to 200MB)'
  },
  
  // Image only upload
  IMAGE_ONLY: {
    maxSizeInBytes: FILE_SIZE_LIMITS.IMAGE_DEFAULT,
    maxSizeInMB: FILE_SIZE_LIMITS_MB.IMAGE_DEFAULT,
    allowedFileTypes: SUPPORTED_FILE_TYPES.IMAGES,
    description: 'Image files only (up to 10MB)'
  },
  
  // Document only upload (no images or archives)
  DOCUMENT_ONLY: {
    maxSizeInBytes: FILE_SIZE_LIMITS.DOCUMENT_DEFAULT,
    maxSizeInMB: FILE_SIZE_LIMITS_MB.DOCUMENT_DEFAULT,
    allowedFileTypes: SUPPORTED_FILE_TYPES.DOCUMENTS,
    description: 'Document files only (up to 100MB)'
  }
} as const;

// Get file size limit from environment or use default
export function getMaxFileSize(): number {
  const envFileSize = process.env.MAX_FILE_SIZE;
  if (envFileSize) {
    const size = parseInt(envFileSize, 10);
    if (!isNaN(size) && size > 0) {
      return size;
    }
  }
  return FILE_SIZE_LIMITS.DOCUMENT_DEFAULT;
}

// Get file size limit in MB for display
export function getMaxFileSizeMB(): number {
  return Math.round(getMaxFileSize() / (1024 * 1024));
}

// Utility function to format file size for display
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// Validate file size against limits
export function validateFileSize(fileSize: number, maxSize: number = getMaxFileSize()): {
  valid: boolean;
  error?: string;
} {
  if (fileSize > maxSize) {
    return {
      valid: false,
      error: `File size (${formatFileSize(fileSize)}) exceeds the maximum allowed size of ${formatFileSize(maxSize)}`
    };
  }
  return { valid: true };
}

// Get appropriate file config based on file size
export function getRecommendedConfig(fileSize: number) {
  if (fileSize <= FILE_SIZE_LIMITS.MEDIUM) {
    return FILE_CONFIG_PRESETS.STANDARD_DOCUMENT;
  } else if (fileSize <= FILE_SIZE_LIMITS.LARGE) {
    return FILE_CONFIG_PRESETS.LARGE_DOCUMENT;
  } else {
    return FILE_CONFIG_PRESETS.LARGE_DOCUMENT;
  }
}

export default {
  FILE_SIZE_LIMITS,
  FILE_SIZE_LIMITS_MB,
  SUPPORTED_FILE_TYPES,
  FILE_TYPE_DESCRIPTIONS,
  FILE_CONFIG_PRESETS,
  getMaxFileSize,
  getMaxFileSizeMB,
  formatFileSize,
  validateFileSize,
  getRecommendedConfig
};
