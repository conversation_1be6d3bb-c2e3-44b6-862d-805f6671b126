'use client';

import React, { useRef, useState } from 'react';
import { IDocumentJourney } from '@/types';
import DispositionForm from './DispositionForm';
import { generateDispositionFormPDF } from '@/utils/pdfMakeUtils';
import { generateFormattedDocumentId } from '@/utils/documentHelpers';

interface DispositionFormPreviewProps {
  documentId: string;
  documentTitle: string;
  journey: (IDocumentJourney & {
    byUser: {
      name?: string;
      division?: string;
      _id?: string;
    } | string;
  })[];
  isBlank?: boolean;
  trackingNumber?: string;
}

export default function DispositionFormPreview({
  documentId,
  documentTitle,
  journey,
  isBlank = false,
  trackingNumber
}: DispositionFormPreviewProps) {
  const dispositionFormRef = useRef<HTMLDivElement>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Handle downloading the PDF
  const handleDownload = async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);

      // Debug logging for PDF generation
      console.log('Generating PDF with:', {
        documentId,
        documentTitle,
        journeyLength: journey?.length || 0,
        isBlank,
        journey: journey
      });

      // Generate and download the PDF
      await generateDispositionFormPDF(documentId, documentTitle, journey, isBlank);
    } catch (error) {
      console.error('Error generating PDF:', error);

      // Try a simpler approach as fallback
      try {
        alert('Using fallback PDF generation method. Please wait...');

        // Create a simple PDF with minimal content
        const pdfMake = (await import('pdfmake/build/pdfmake')).default;
        const pdfFonts = (await import('pdfmake/build/vfs_fonts')).default;

        // Initialize pdfMake
        if (pdfFonts && pdfFonts.pdfMake) {
          pdfMake.vfs = pdfFonts.pdfMake.vfs;
        }

        // Create a simple document definition
        const docDefinition = {
          content: [
            { text: 'DISPOSITION FORM', style: 'header' },
            { text: isBlank ? 'Blank Form' : documentTitle, style: 'subheader' },
            { text: 'Document ID: ' + documentId },
            { text: 'Generated on: ' + new Date().toLocaleString() }
          ],
          styles: {
            header: { fontSize: 18, bold: true, alignment: 'center', margin: [0, 0, 0, 10] },
            subheader: { fontSize: 14, bold: true, alignment: 'center', margin: [0, 10, 0, 5] }
          }
        };

        // Generate and download the PDF
        pdfMake.createPdf(docDefinition).download(`${isBlank ? 'Blank-DF' : 'DF'}-Fallback.pdf`);
      } catch (fallbackError) {
        console.error('Fallback PDF generation also failed:', fallbackError);
        alert('PDF generation failed. Please try again later.');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Debug logging for props
  console.log('DispositionFormPreview - Props:', {
    documentId,
    documentTitle,
    journeyLength: journey?.length || 0,
    isBlank,
    trackingNumber,
    journey: journey
  });

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 p-4">
      <div className="max-w-[8.5in] mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="p-4 bg-blue-700 text-white flex justify-between items-center">
          <h1 className="text-xl font-bold">
            {isBlank ? 'Blank Disposition Form' : 'Disposition Form Preview'}
          </h1>
          <button
            onClick={handleDownload}
            disabled={isGenerating}
            className={`${isGenerating ? 'bg-gray-200 text-gray-500' : 'bg-white text-blue-700 hover:bg-blue-50'} px-4 py-2 rounded-lg font-medium transition-colors flex items-center`}
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500 mr-2"></div>
                Generating PDF...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </>
            )}
          </button>
        </div>

        <div className="p-4">
          <div className="mb-4 text-sm text-black dark:text-white">
            <p>Preview the disposition form below. Click the "Download PDF" button to save it to your device.</p>
            {isBlank && (
              <p className="mt-2 text-amber-600 font-medium">This is a blank form with no routing history.</p>
            )}
            {!isBlank && journey.length === 0 && (
              <p className="mt-2 text-amber-600 font-medium">No routing history found for this document.</p>
            )}
            {!isBlank && journey.length > 0 && (
              <p className="mt-2 text-green-600 font-medium">Found {journey.length} journey entries for this document.</p>
            )}
          </div>

          {/* IMPORTANT: Do NOT add a border here - the DispositionForm component already has its own border */}
          <div className="rounded-lg overflow-hidden" style={{ width: '8.5in', margin: '0 auto' }}>
            <div ref={dispositionFormRef} style={{ width: '8.5in', height: '11in' }}>
              <DispositionForm
                documentId={documentId}
                documentTitle={documentTitle}
                journey={isBlank ? [] : journey}
                isBlank={isBlank}
                trackingNumber={trackingNumber}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
