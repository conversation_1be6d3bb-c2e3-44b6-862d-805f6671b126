@echo off
color 0A
echo ================================================================
echo           Testing Fixed Gemini API Model
echo ================================================================
echo.

echo ✅ Fixed: Updated model name from 'gemini-pro' to 'gemini-1.5-flash'
echo ✅ Fixed: Using correct v1 API endpoint
echo.

echo Testing your API key with the correct model...
echo.

node quick-api-test.js

echo.
echo ================================================================
echo.

echo If you see "SUCCESS! API key is working" above, then:
echo ✅ Your AI features are now working!
echo ✅ Restart your app (npm run dev) to use the fixed configuration
echo.

echo If you still see errors, please share the error message.
echo.

pause
