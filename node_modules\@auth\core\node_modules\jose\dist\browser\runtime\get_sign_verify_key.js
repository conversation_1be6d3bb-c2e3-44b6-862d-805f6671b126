import crypto, { isCrypto<PERSON><PERSON> } from './webcrypto.js';
import { checkSigCrypto<PERSON><PERSON> } from '../lib/crypto_key.js';
import invalidKeyInput from '../lib/invalid_key_input.js';
import { types } from './is_key_like.js';
import normalize from './normalize_key.js';
export default async function getCrypto<PERSON>ey(alg, key, usage) {
    if (usage === 'sign') {
        key = await normalize.normalizePrivate<PERSON>ey(key, alg);
    }
    if (usage === 'verify') {
        key = await normalize.normalizePublicKey(key, alg);
    }
    if (isCrypto<PERSON>ey(key)) {
        checkSigCryptoKey(key, alg, usage);
        return key;
    }
    if (key instanceof Uint8Array) {
        if (!alg.startsWith('HS')) {
            throw new TypeError(invalidKeyInput(key, ...types));
        }
        return crypto.subtle.importKey('raw', key, { hash: `SHA-${alg.slice(-3)}`, name: 'HM<PERSON>' }, false, [usage]);
    }
    throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array', 'JSON Web Key'));
}
