import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/options';
import dbConnect from '@/lib/db/mongodb';
import Notification from '@/models/Notification';
import SmartNotification from '@/models/SmartNotification';

// Mark this route as dynamic to prevent static generation
export const dynamic = 'force-dynamic';

// GET /api/notifications - Get all notifications for the current user
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';
    const includeSmartNotifications = searchParams.get('includeSmart') !== 'false'; // Include by default
    const lastFetch = searchParams.get('lastFetch'); // For real-time updates

    await dbConnect();

    // Build query
    if (!session.user.id) {
      console.error('User ID is missing from session:', session);
      return NextResponse.json(
        { message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Try to convert to ObjectId if needed
    let userId;
    try {
      const mongoose = require('mongoose');
      userId = mongoose.Types.ObjectId.isValid(session.user.id)
        ? new mongoose.Types.ObjectId(session.user.id)
        : session.user.id;
    } catch (error) {
      console.error('Error converting user ID to ObjectId:', error);
      userId = session.user.id;
    }

    const query: any = { userId };
    if (unreadOnly) {
      query.isRead = false;
    }

    // Add real-time filter if lastFetch is provided
    if (lastFetch) {
      query.createdAt = { $gt: new Date(lastFetch) };
    }

    console.log('Notification query:', query);

    // Fetch regular notifications
    const regularNotifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .limit(Math.ceil(limit / 2)) // Split limit between regular and smart
      .populate({
        path: 'documentId',
        select: 'title category status createdBy recipientId trackingNumber',
        options: { lean: true }
      })
      .lean();

    // Fetch smart notifications if requested
    let smartNotifications: any[] = [];
    if (includeSmartNotifications) {
      smartNotifications = await SmartNotification.find(query)
        .sort({ createdAt: -1 })
        .limit(Math.ceil(limit / 2))
        .populate({
          path: 'documentId',
          select: 'title category status createdBy recipientId trackingNumber',
          options: { lean: true }
        })
        .lean();
    }

    // Combine and sort all notifications
    const allNotifications = [...regularNotifications, ...smartNotifications]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);

    console.log(`Found ${allNotifications.length} total notifications for user ${session.user.id}`);
    console.log(`Regular: ${regularNotifications.length}, Smart: ${smartNotifications.length}`);

    // Log details of each notification for debugging
    allNotifications.forEach((notification, index) => {
      console.log(`Notification ${index + 1}:`, {
        id: notification._id,
        type: notification.type,
        isRead: notification.isRead,
        priority: notification.priority || 'medium',
        documentId: notification.documentId?._id,
        documentStatus: notification.documentId?.status,
        isSmartNotification: !!notification.aiGenerated
      });
    });

    // Count total unread notifications (both types)
    const [regularUnreadCount, smartUnreadCount] = await Promise.all([
      Notification.countDocuments({ userId, isRead: false }),
      SmartNotification.countDocuments({ userId, isRead: false })
    ]);
    const totalUnreadCount = regularUnreadCount + smartUnreadCount;

    // Map the notifications to include enhanced fields
    const mappedNotifications = allNotifications.map((notification: any) => ({
      ...notification,
      id: notification._id.toString(),
      _id: notification._id.toString(),
      // Add title if missing (for smart notifications)
      title: notification.title || (notification.type === 'DOCUMENT_RECEIVED' ? 'Document Received' :
             notification.type === 'DOCUMENT_FORWARDED' ? 'Document Forwarded' :
             notification.type === 'REMINDER' ? 'Reminder' : 'Notification'),
      // Ensure priority exists
      priority: notification.priority || 'medium',
      // Add action URL for document-related notifications
      actionUrl: notification.documentId ? `/documents/${notification.documentId._id || notification.documentId}` : undefined,
      // Mark if it's a smart notification
      isSmartNotification: !!notification.aiGenerated,
      // Add suggested actions if available
      suggestedActions: notification.suggestedActions || [],
      documentId: notification.documentId ? {
        ...notification.documentId,
        id: notification.documentId._id?.toString() || notification.documentId.toString(),
        _id: notification.documentId._id?.toString() || notification.documentId.toString(),
      } : null,
    }));

    return NextResponse.json({
      success: true,
      notifications: mappedNotifications,
      unreadCount: totalUnreadCount,
      stats: {
        total: allNotifications.length,
        regular: regularNotifications.length,
        smart: smartNotifications.length,
        unread: totalUnreadCount,
        regularUnread: regularUnreadCount,
        smartUnread: smartUnreadCount
      },
      lastFetch: new Date().toISOString() // For next real-time request
    });
  } catch (error: any) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}
