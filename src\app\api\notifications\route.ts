import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/options';
import dbConnect from '@/lib/db/mongodb';
import Notification from '@/models/Notification';
import SmartNotification from '@/models/SmartNotification';
import { getOrFetchData } from '@/utils/apiCache';

// Request deduplication cache
const inFlightRequests = new Map<string, Promise<any>>();

// Performance monitoring
const performanceMetrics = {
  totalRequests: 0,
  cacheHits: 0,
  avgResponseTime: 0,
  lastReset: Date.now()
};

// Mark this route as dynamic to prevent static generation
export const dynamic = 'force-dynamic';

// GET /api/notifications - Get all notifications for the current user
export async function GET(request: Request) {
  const startTime = Date.now();
  performanceMetrics.totalRequests++;

  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';
    const includeSmartNotifications = searchParams.get('includeSmart') !== 'false'; // Include by default
    const lastFetch = searchParams.get('lastFetch'); // For real-time updates

    // Create cache key for request deduplication
    const cacheKey = `notifications:${session.user.id}:${limit}:${unreadOnly}:${includeSmartNotifications}:${lastFetch || 'all'}`;

    // Check if there's already an in-flight request for this exact query
    if (inFlightRequests.has(cacheKey)) {
      console.log('🚀 Request deduplication hit for:', cacheKey);
      const cachedResult = await inFlightRequests.get(cacheKey);
      performanceMetrics.cacheHits++;
      return NextResponse.json(cachedResult);
    }

    // Create the request promise and cache it
    const requestPromise = fetchNotificationsData(session.user.id!, limit, unreadOnly, includeSmartNotifications, lastFetch);
    inFlightRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;

      // Update performance metrics
      const responseTime = Date.now() - startTime;
      performanceMetrics.avgResponseTime =
        (performanceMetrics.avgResponseTime * (performanceMetrics.totalRequests - 1) + responseTime) / performanceMetrics.totalRequests;

      return NextResponse.json(result);
    } finally {
      // Clean up the in-flight request
      inFlightRequests.delete(cacheKey);
    }
  } catch (error: any) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}

// Extracted notification fetching logic for better caching and reusability
async function fetchNotificationsData(
  userId: string,
  limit: number,
  unreadOnly: boolean,
  includeSmartNotifications: boolean,
  lastFetch?: string | null
) {
  // Build query - userId is already validated in the main function
  let userObjectId;
  try {
    const mongoose = require('mongoose');
    userObjectId = mongoose.Types.ObjectId.isValid(userId)
      ? new mongoose.Types.ObjectId(userId)
      : userId;
  } catch (error) {
    console.error('Error converting user ID to ObjectId:', error);
    userObjectId = userId;
  }

  const query: any = { userId: userObjectId };
  if (unreadOnly) {
    query.isRead = false;
  }

  // Add real-time filter if lastFetch is provided
  if (lastFetch) {
    query.createdAt = { $gt: new Date(lastFetch) };
  }

  console.log('Notification query:', query);

  // Use Promise.all for parallel queries to improve performance
  const [regularNotifications, smartNotifications] = await Promise.all([
    // Fetch regular notifications with optimized query
    Notification.find(query)
      .sort({ createdAt: -1 })
      .limit(Math.ceil(limit / 2)) // Split limit between regular and smart
      .select('title message type priority isRead createdAt documentId userId') // Only select needed fields
      .populate({
        path: 'documentId',
        select: 'title category status createdBy recipientId trackingNumber',
        options: { lean: true }
      })
      .lean()
      .exec(),

    // Fetch smart notifications if requested
    includeSmartNotifications
      ? SmartNotification.find(query)
          .sort({ createdAt: -1 })
          .limit(Math.ceil(limit / 2))
          .select('title message type priority isRead createdAt documentId userId aiGenerated suggestedActions') // Only select needed fields
          .populate({
            path: 'documentId',
            select: 'title category status createdBy recipientId trackingNumber',
            options: { lean: true }
          })
          .lean()
          .exec()
      : Promise.resolve([])
  ]);

  // Combine and sort all notifications
  const allNotifications = [...regularNotifications, ...smartNotifications]
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, limit);

  console.log(`Found ${allNotifications.length} total notifications for user ${userId}`);
  console.log(`Regular: ${regularNotifications.length}, Smart: ${smartNotifications.length}`);

  // Log details of each notification for debugging
  allNotifications.forEach((notification, index) => {
    console.log(`Notification ${index + 1}:`, {
      id: notification._id,
      type: notification.type,
      isRead: notification.isRead,
      priority: notification.priority || 'medium',
      documentId: notification.documentId?._id,
      documentStatus: notification.documentId?.status,
      isSmartNotification: !!notification.aiGenerated
    });
  });

  // Optimized unread count queries with parallel execution
  const [regularUnreadCount, smartUnreadCount] = await Promise.all([
    Notification.countDocuments({ userId: userObjectId, isRead: false }).exec(),
    includeSmartNotifications
      ? SmartNotification.countDocuments({ userId: userObjectId, isRead: false }).exec()
      : Promise.resolve(0)
  ]);
  const totalUnreadCount = regularUnreadCount + smartUnreadCount;

  // Map the notifications to include enhanced fields
  const mappedNotifications = allNotifications.map((notification: any) => ({
    ...notification,
    id: notification._id.toString(),
    _id: notification._id.toString(),
    // Add title if missing (for smart notifications)
    title: notification.title || (notification.type === 'DOCUMENT_RECEIVED' ? 'Document Received' :
           notification.type === 'DOCUMENT_FORWARDED' ? 'Document Forwarded' :
           notification.type === 'REMINDER' ? 'Reminder' : 'Notification'),
    // Ensure priority exists
    priority: notification.priority || 'medium',
    // Add action URL for document-related notifications
    actionUrl: notification.documentId ? `/documents/${notification.documentId._id || notification.documentId}` : undefined,
    // Mark if it's a smart notification
    isSmartNotification: !!notification.aiGenerated,
    // Add suggested actions if available
    suggestedActions: notification.suggestedActions || [],
    documentId: notification.documentId ? {
      ...notification.documentId,
      id: notification.documentId._id?.toString() || notification.documentId.toString(),
      _id: notification.documentId._id?.toString() || notification.documentId.toString(),
    } : null,
  }));

  return {
    success: true,
    notifications: mappedNotifications,
    unreadCount: totalUnreadCount,
    stats: {
      total: allNotifications.length,
      regular: regularNotifications.length,
      smart: smartNotifications.length,
      unread: totalUnreadCount,
      regularUnread: regularUnreadCount,
      smartUnread: smartUnreadCount
    },
    lastFetch: new Date().toISOString(), // For next real-time request
    performance: {
      totalRequests: performanceMetrics.totalRequests,
      cacheHits: performanceMetrics.cacheHits,
      avgResponseTime: Math.round(performanceMetrics.avgResponseTime)
    }
  };
}
