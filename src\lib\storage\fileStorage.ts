/**
 * File Storage Service
 *
 * This service provides an abstraction layer for file storage operations.
 * Optimized for local intranet usage with file system storage.
 */

import { writeFile, mkdir, unlink, access, constants, stat } from 'fs/promises';
import { existsSync } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { exec } from 'child_process';

// Define file storage interface
export interface FileStorageProvider {
  uploadFile(file: Buffer, fileName: string, mimeType: string): Promise<FileUploadResult>;
  deleteFile(fileKey: string): Promise<boolean>;
  getFileUrl(fileKey: string): string;
}

// File upload result interface
export interface FileUploadResult {
  fileKey: string;
  fileUrl: string;
  fileName: string;
  fileType: string;
  fileSize: number;
}

// File validation options
export interface FileValidationOptions {
  maxSizeInBytes: number;
  allowedFileTypes: string[];
}

// Default validation options
export const defaultValidationOptions: FileValidationOptions = {
  maxSizeInBytes: 100 * 1024 * 1024, // 100MB for large documents
  allowedFileTypes: [
    // Documents
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt',
    // Images
    'jpg', 'jpeg', 'png', 'gif', 'webp',
    // Others
    'zip', 'rar'
  ]
};

// Create a promise-based exec function
const execAsync = (command: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
        return;
      }
      resolve(stdout);
    });
  });
};

// Local file storage provider
export class LocalFileStorageProvider implements FileStorageProvider {
  private uploadsDir: string;
  private minDiskSpacePercent: number;

  constructor(uploadsDir?: string, minDiskSpacePercent: number = 10) {
    this.uploadsDir = uploadsDir || join(process.cwd(), 'public', 'uploads');
    this.minDiskSpacePercent = minDiskSpacePercent; // Minimum free disk space percentage
  }

  /**
   * Check if directory exists and is writable
   * @param dir Directory path to check
   * @returns True if directory exists and is writable
   */
  private async checkDirectoryPermissions(dir: string): Promise<boolean> {
    try {
      // Check if directory exists and we have write permissions
      await access(dir, constants.W_OK);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if there's enough disk space available
   * @param requiredBytes Number of bytes needed
   * @returns True if there's enough disk space
   */
  private async checkDiskSpace(requiredBytes: number): Promise<{ hasSpace: boolean; freeSpacePercent: number }> {
    try {
      // Default values in case we can't check disk space
      let freeSpacePercent = 100;
      let hasEnoughSpace = true;

      // Check if we're on Windows or Unix-like system
      const isWindows = process.platform === 'win32';

      if (isWindows) {
        // Windows: use wmic to get disk space info
        const command = `wmic logicaldisk where "DeviceID='${process.cwd().substring(0, 2)}'" get FreeSpace,Size /format:csv`;
        const stdout = await execAsync(command);

        // Parse the CSV output
        const lines = stdout.trim().split('\n');
        if (lines.length >= 2) {
          const values = lines[1].split(',');
          if (values.length >= 3) {
            const freeSpace = parseInt(values[1], 10);
            const totalSpace = parseInt(values[2], 10);

            if (!isNaN(freeSpace) && !isNaN(totalSpace) && totalSpace > 0) {
              freeSpacePercent = (freeSpace / totalSpace) * 100;
              hasEnoughSpace = freeSpacePercent > this.minDiskSpacePercent;
            }
          }
        }
      } else {
        // Unix-like systems: use df command
        const command = `df -k "${this.uploadsDir}"`;
        const stdout = await execAsync(command);

        // Parse the output
        const lines = stdout.trim().split('\n');
        if (lines.length >= 2) {
          const values = lines[1].split(/\s+/);
          if (values.length >= 5) {
            // df output format: Filesystem 1K-blocks Used Available Use% Mounted on
            const usedPercent = parseInt(values[4].replace('%', ''), 10);

            if (!isNaN(usedPercent)) {
              freeSpacePercent = 100 - usedPercent;
              hasEnoughSpace = freeSpacePercent > this.minDiskSpacePercent;
            }
          }
        }
      }

      return {
        hasSpace: hasEnoughSpace,
        freeSpacePercent
      };
    } catch (error) {
      console.error('Error checking disk space with system commands:', error);

      try {
        // Fallback method: check if we can create a small test file
        const testFilePath = join(this.uploadsDir, `.diskspace-test-${Date.now()}`);
        const testFileSize = 1024; // 1KB

        // Create a buffer with random data
        const testBuffer = Buffer.alloc(testFileSize);

        // Try to write the test file
        await writeFile(testFilePath, testBuffer);

        // If we get here, we can write to the disk
        // Clean up the test file
        await unlink(testFilePath);

        console.log('Disk space check fallback: Successfully wrote test file');
        return { hasSpace: true, freeSpacePercent: 100 };
      } catch (fallbackError) {
        console.error('Disk space check fallback failed:', fallbackError);
        // At this point, we've tried everything and failed
        // Default to true to avoid blocking uploads, but log a warning
        console.warn('WARNING: Could not verify disk space. Proceeding with upload anyway.');
        return { hasSpace: true, freeSpacePercent: 100 };
      }
    }
  }

  /**
   * Create directory if it doesn't exist
   * @param dir Directory path to create
   */
  private async ensureDirectoryExists(dir: string): Promise<void> {
    try {
      if (!existsSync(dir)) {
        await mkdir(dir, { recursive: true, mode: 0o755 });
      }
    } catch (error) {
      throw new Error(`Failed to create directory ${dir}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async uploadFile(file: Buffer, fileName: string, mimeType: string): Promise<FileUploadResult> {
    try {
      // Check disk space before proceeding
      const { hasSpace, freeSpacePercent } = await this.checkDiskSpace(file.length);
      if (!hasSpace) {
        throw new Error(`Insufficient disk space. Only ${freeSpacePercent.toFixed(2)}% free. Minimum required: ${this.minDiskSpacePercent}%`);
      }

      // Ensure uploads directory exists
      await this.ensureDirectoryExists(this.uploadsDir);

      // Check if directory is writable
      const isWritable = await this.checkDirectoryPermissions(this.uploadsDir);
      if (!isWritable) {
        throw new Error(`Directory ${this.uploadsDir} is not writable. Check permissions.`);
      }

      // Generate a unique file key
      const fileExtension = fileName.split('.').pop() || '';
      const uniqueFileName = `${uuidv4()}.${fileExtension}`;
      const filePath = join(this.uploadsDir, uniqueFileName);

      // Write file to disk with error handling
      try {
        await writeFile(filePath, file);
      } catch (error) {
        throw new Error(`Failed to write file to disk: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Verify file was written correctly
      try {
        const fileStats = await stat(filePath);
        if (fileStats.size !== file.length) {
          throw new Error(`File size mismatch. Expected ${file.length} bytes, got ${fileStats.size} bytes.`);
        }
      } catch (error) {
        throw new Error(`Failed to verify file: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Return file metadata
      return {
        fileKey: uniqueFileName,
        fileUrl: `/uploads/${uniqueFileName}`,
        fileName: fileName,
        fileType: fileExtension,
        fileSize: file.length
      };
    } catch (error) {
      console.error('Error in uploadFile:', error);
      throw error; // Re-throw to be handled by the caller
    }
  }

  async deleteFile(fileKey: string): Promise<boolean> {
    try {
      // Validate file key to prevent directory traversal
      if (fileKey.includes('/') || fileKey.includes('\\')) {
        console.error('Invalid file key (potential directory traversal):', fileKey);
        return false;
      }

      const filePath = join(this.uploadsDir, fileKey);

      // Check if file exists
      if (!existsSync(filePath)) {
        console.warn(`File not found for deletion: ${filePath}`);
        return false;
      }

      // Check if we have permission to delete
      try {
        await access(filePath, constants.W_OK);
      } catch (error) {
        console.error(`No permission to delete file ${filePath}:`, error);
        return false;
      }

      // Delete the file
      await unlink(filePath);
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  getFileUrl(fileKey: string): string {
    // Validate file key to prevent path traversal in URLs
    if (fileKey.includes('/') || fileKey.includes('\\')) {
      console.error('Invalid file key (potential path traversal):', fileKey);
      return '';
    }
    return `/uploads/${fileKey}`;
  }
}

// File storage factory - simplified for local storage only
export class FileStorageFactory {
  static getProvider(): FileStorageProvider {
    // Get uploads directory from environment or use default
    const uploadsDir = process.env.UPLOADS_DIRECTORY || join(process.cwd(), 'public', 'uploads');
    return new LocalFileStorageProvider(uploadsDir);
  }
}

// File validation utilities
export function validateFile(
  file: { size: number, name: string },
  options: FileValidationOptions = defaultValidationOptions
): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > options.maxSizeInBytes) {
    const maxSizeMB = (options.maxSizeInBytes / (1024 * 1024)).toFixed(0);
    return {
      valid: false,
      error: `File size exceeds the maximum allowed size of ${maxSizeMB}MB`
    };
  }

  // Check file type
  const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
  if (!options.allowedFileTypes.includes(fileExtension)) {
    return {
      valid: false,
      error: `File type .${fileExtension} is not allowed. Allowed types: ${options.allowedFileTypes.join(', ')}`
    };
  }

  return { valid: true };
}

// Export default provider
export const fileStorage = FileStorageFactory.getProvider();
