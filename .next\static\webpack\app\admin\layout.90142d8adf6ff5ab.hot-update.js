"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/components/UnifiedHeader.tsx":
/*!******************************************!*\
  !*** ./src/components/UnifiedHeader.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _DarkModeToggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./DarkModeToggle */ \"(app-pages-browser)/./src/components/DarkModeToggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Dynamically import non-critical components\nvar RealTimeNotifications = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_c = function() {\n    return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_notifications_RealTimeNotifications_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./notifications/RealTimeNotifications */ \"(app-pages-browser)/./src/components/notifications/RealTimeNotifications.tsx\"));\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\UnifiedHeader.tsx -> \" + \"./notifications/RealTimeNotifications\"\n        ]\n    },\n    ssr: false,\n    loading: function() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-8 h-8 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n            lineNumber: 17,\n            columnNumber: 5\n        }, _this);\n    }\n});\n_c1 = RealTimeNotifications;\nvar ColorSchemeToggle = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n    return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_ColorSchemeToggle_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./ColorSchemeToggle */ \"(app-pages-browser)/./src/components/ColorSchemeToggle.tsx\"));\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\UnifiedHeader.tsx -> \" + \"./ColorSchemeToggle\"\n        ]\n    },\n    ssr: false,\n    loading: function() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-8 h-8 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n            lineNumber: 26,\n            columnNumber: 5\n        }, _this);\n    }\n});\n_c2 = ColorSchemeToggle;\nfunction UnifiedHeader(param) {\n    var _param_isAdminSection = param.isAdminSection, isAdminSection = _param_isAdminSection === void 0 ? false : _param_isAdminSection;\n    var _session_user_name, _session_user, _session_user1, _session_user2, _session_user3, _session_user4, _session_user5, _session_user6, _session_user7;\n    _s();\n    var _useSession = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)(), session = _useSession.data;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_9__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), 2), isProfileOpen = _useState[0], setIsProfileOpen = _useState[1];\n    var dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setIsProfileOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-40 transition-colors duration-200 shadow-sm border-b\",\n        style: {\n            backgroundColor: \"rgba(var(--theme-bg-secondary), 1)\",\n            borderColor: \"rgba(var(--theme-border-primary), 1)\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: isAdminSection ? \"/admin\" : \"/dashboard\",\n                            className: \"flex-shrink-0 flex items-center group pl-10 md:pl-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-10 w-10 overflow-hidden rounded-xl shadow-sm transition-all duration-200 group-hover:shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/MGB Logo_Proxy.png\",\n                                        alt: \"MGB Logo\",\n                                        fill: true,\n                                        sizes: \"40px\",\n                                        priority: true,\n                                        fetchPriority: \"high\",\n                                        className: \"object-cover transition-transform duration-200 group-hover:scale-105\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-secondary-500 dark:text-secondary-400 font-bold text-lg transition-colors\",\n                                            children: isAdminSection ? \"MGB Admin\" : \"MGB\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-accent-green-600 dark:text-accent-green-400 transition-colors\",\n                                            children: isAdminSection ? \"Administration Panel\" : \"Document Management System\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationDropdown, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorSchemeToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DarkModeToggle__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                ref: dropdownRef,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return setIsProfileOpen(!isProfileOpen);\n                                        },\n                                        className: \"flex items-center space-x-3 focus:outline-none\",\n                                        \"aria-expanded\": isProfileOpen,\n                                        \"aria-haspopup\": \"true\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-9 w-9 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center text-white shadow-sm overflow-hidden transition-transform hover:scale-105\",\n                                                children: (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : (_session_user_name = _session_user.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.charAt(0).toUpperCase()) || \"U\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden md:block text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors\",\n                                                        children: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 transition-colors\",\n                                                        children: (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.role) === _types__WEBPACK_IMPORTED_MODULE_6__.UserRole.ADMIN ? \"Administrator\" : session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.division\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 text-gray-400 transition-transform duration-200 \".concat(isProfileOpen ? \"rotate-180\" : \"\"),\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"origin-top-right absolute right-0 mt-2 w-56 rounded-xl shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50 transition-all duration-200 ease-out animate-dropdown\",\n                                        role: \"menu\",\n                                        \"aria-orientation\": \"vertical\",\n                                        \"aria-labelledby\": \"user-menu\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-2\",\n                                            role: \"none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"block px-4 py-3 text-sm border-b border-gray-100 dark:border-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: session === null || session === void 0 ? void 0 : (_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : _session_user4.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 dark:text-gray-400 text-xs mt-0.5\",\n                                                            children: session === null || session === void 0 ? void 0 : (_session_user5 = session.user) === null || _session_user5 === void 0 ? void 0 : _session_user5.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/profile\",\n                                                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                    role: \"menuitem\",\n                                                    onClick: function() {\n                                                        return setIsProfileOpen(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-4 w-4 mr-2 text-gray-500 dark:text-gray-400\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"My Profile\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/settings\",\n                                                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                    role: \"menuitem\",\n                                                    onClick: function() {\n                                                        return setIsProfileOpen(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-4 w-4 mr-2 text-gray-500 dark:text-gray-400\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Settings\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !isAdminSection && ((session === null || session === void 0 ? void 0 : (_session_user6 = session.user) === null || _session_user6 === void 0 ? void 0 : _session_user6.role) === _types__WEBPACK_IMPORTED_MODULE_6__.UserRole.ADMIN || (session === null || session === void 0 ? void 0 : (_session_user7 = session.user) === null || _session_user7 === void 0 ? void 0 : _session_user7.role) === _types__WEBPACK_IMPORTED_MODULE_6__.UserRole.REGIONAL_DIRECTOR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/admin\",\n                                                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                    role: \"menuitem\",\n                                                    onClick: function() {\n                                                        return setIsProfileOpen(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-4 w-4 mr-2 text-gray-500 dark:text-gray-400\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Admin Panel\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-100 dark:border-gray-700 my-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        var callbackUrl = window.location.origin;\n                                                        console.log(\"Signing out with callback URL:\", callbackUrl);\n                                                        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n                                                            callbackUrl: callbackUrl\n                                                        });\n                                                    },\n                                                    className: \"flex items-center w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\",\n                                                    role: \"menuitem\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-4 w-4 mr-2\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm6.293 11.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L11.586 9H5a1 1 0 100 2h6.586l-2.293 2.293z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Sign Out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\UnifiedHeader.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(UnifiedHeader, \"wGlkxzGmN8Dwm3g/cdyEQ0osjzg=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c3 = UnifiedHeader;\n// Memoize the component to prevent unnecessary re-renders\n/* harmony default export */ __webpack_exports__[\"default\"] = (/*#__PURE__*/_c4 = (0,react__WEBPACK_IMPORTED_MODULE_4__.memo)(UnifiedHeader));\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"RealTimeNotifications$dynamic\");\n$RefreshReg$(_c1, \"RealTimeNotifications\");\n$RefreshReg$(_c2, \"ColorSchemeToggle\");\n$RefreshReg$(_c3, \"UnifiedHeader\");\n$RefreshReg$(_c4, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UnifiedHeader.tsx\n"));

/***/ })

});