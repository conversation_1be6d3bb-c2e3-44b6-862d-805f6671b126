const { MongoClient } = require('mongodb');

async function initializeDatabase() {
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db('document-tracker');
    
    // Create a test user
    const usersCollection = db.collection('users');
    const testUser = {
      name: 'Test User',
      email: '<EMAIL>',
      role: 'EMPLOYEE',
      division: 'ORD',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const userResult = await usersCollection.insertOne(testUser);
    console.log('Test user created:', userResult.insertedId);

    // Create a test document
    const documentsCollection = db.collection('documents');
    const testDocument = {
      title: 'Test Document',
      description: 'This is a test document to initialize the database',
      category: 'MEMO',
      actionType: 'FOR_INFORMATION',
      senderId: userResult.insertedId,
      recipientId: userResult.insertedId,
      status: 'PENDING',
      trackingNumber: 'DTN-TEST-001',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const docResult = await documentsCollection.insertOne(testDocument);
    console.log('Test document created:', docResult.insertedId);

    // Create a test notification
    const notificationsCollection = db.collection('notifications');
    const testNotification = {
      userId: userResult.insertedId,
      title: 'Welcome to Document Tracker',
      message: 'Your account has been set up successfully',
      type: 'INFO',
      isRead: false,
      createdAt: new Date()
    };
    
    const notifResult = await notificationsCollection.insertOne(testNotification);
    console.log('Test notification created:', notifResult.insertedId);

    console.log('\n✅ Database initialized successfully!');
    console.log('🔄 Refresh MongoDB Compass to see the "document-tracker" database');
    
  } catch (error) {
    console.error('Error initializing database:', error);
  } finally {
    await client.close();
  }
}

initializeDatabase();
