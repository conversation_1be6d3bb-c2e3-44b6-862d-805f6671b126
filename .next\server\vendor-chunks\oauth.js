/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth";
exports.ids = ["vendor-chunks/oauth"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth/index.js":
/*!*************************************!*\
  !*** ./node_modules/oauth/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.OAuth = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuth;\nexports.OAuthEcho = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuthEcho;\nexports.OAuth2 = __webpack_require__(/*! ./lib/oauth2 */ \"(rsc)/./node_modules/oauth/lib/oauth2.js\").OAuth2;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsdUdBQTRDO0FBQzVDLCtHQUFvRDtBQUNwRCwyR0FBK0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kb2N1bWVudC10cmFja2VyLy4vbm9kZV9tb2R1bGVzL29hdXRoL2luZGV4LmpzPzI0ZjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0cy5PQXV0aCA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aFwiKS5PQXV0aDtcbmV4cG9ydHMuT0F1dGhFY2hvID0gcmVxdWlyZShcIi4vbGliL29hdXRoXCIpLk9BdXRoRWNobztcbmV4cG9ydHMuT0F1dGgyID0gcmVxdWlyZShcIi4vbGliL29hdXRoMlwiKS5PQXV0aDI7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/_utils.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/_utils.js ***!
  \******************************************/
/***/ ((module) => {

eval("// Returns true if this is a host that closes *before* it ends?!?!\nmodule.exports.isAnEarlyCloseHost= function( hostName ) {\n  return hostName && hostName.match(\".*google(apis)?.com$\")\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL191dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZG9jdW1lbnQtdHJhY2tlci8uL25vZGVfbW9kdWxlcy9vYXV0aC9saWIvX3V0aWxzLmpzPzY1MTEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUmV0dXJucyB0cnVlIGlmIHRoaXMgaXMgYSBob3N0IHRoYXQgY2xvc2VzICpiZWZvcmUqIGl0IGVuZHM/IT8hXG5tb2R1bGUuZXhwb3J0cy5pc0FuRWFybHlDbG9zZUhvc3Q9IGZ1bmN0aW9uKCBob3N0TmFtZSApIHtcbiAgcmV0dXJuIGhvc3ROYW1lICYmIGhvc3ROYW1lLm1hdGNoKFwiLipnb29nbGUoYXBpcyk/LmNvbSRcIilcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth.js":
/*!*****************************************!*\
  !*** ./node_modules/oauth/lib/oauth.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto= __webpack_require__(/*! crypto */ \"crypto\"),\n    sha1= __webpack_require__(/*! ./sha1 */ \"(rsc)/./node_modules/oauth/lib/sha1.js\"),\n    http= __webpack_require__(/*! http */ \"http\"),\n    https= __webpack_require__(/*! https */ \"https\"),\n    URL= __webpack_require__(/*! url */ \"url\"),\n    querystring= __webpack_require__(/*! querystring */ \"querystring\"),\n    OAuthUtils= __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\n\nexports.OAuth= function(requestUrl, accessUrl, consumerKey, consumerSecret, version, authorize_callback, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = false;\n\n  this._requestUrl= requestUrl;\n  this._accessUrl= accessUrl;\n  this._consumerKey= consumerKey;\n  this._consumerSecret= this._encodeData( consumerSecret );\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version= version;\n  if( authorize_callback === undefined ) {\n    this._authorize_callback= \"oob\";\n  }\n  else {\n    this._authorize_callback= authorize_callback;\n  }\n\n  if( signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\")\n    throw new Error(\"Un-supported signature method: \" + signatureMethod )\n  this._signatureMethod= signatureMethod;\n  this._nonceSize= nonceSize || 32;\n  this._headers= customHeaders || {\"Accept\" : \"*/*\",\n                                   \"Connection\" : \"close\",\n                                   \"User-Agent\" : \"Node authentication\"}\n  this._clientOptions= this._defaultClientOptions= {\"requestTokenHttpMethod\": \"POST\",\n                                                    \"accessTokenHttpMethod\": \"POST\",\n                                                    \"followRedirects\": true};\n  this._oauthParameterSeperator = \",\";\n};\n\nexports.OAuthEcho= function(realm, verify_credentials, consumerKey, consumerSecret, version, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = true;\n\n  this._realm= realm;\n  this._verifyCredentials = verify_credentials;\n  this._consumerKey= consumerKey;\n  this._consumerSecret= this._encodeData( consumerSecret );\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version= version;\n\n  if( signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\")\n    throw new Error(\"Un-supported signature method: \" + signatureMethod );\n  this._signatureMethod= signatureMethod;\n  this._nonceSize= nonceSize || 32;\n  this._headers= customHeaders || {\"Accept\" : \"*/*\",\n                                   \"Connection\" : \"close\",\n                                   \"User-Agent\" : \"Node authentication\"};\n  this._oauthParameterSeperator = \",\";\n}\n\nexports.OAuthEcho.prototype = exports.OAuth.prototype;\n\nexports.OAuth.prototype._getTimestamp= function() {\n  return Math.floor( (new Date()).getTime() / 1000 );\n}\n\nexports.OAuth.prototype._encodeData= function(toEncode){\n if( toEncode == null || toEncode == \"\" ) return \"\"\n else {\n    var result= encodeURIComponent(toEncode);\n    // Fix the mismatch between OAuth's  RFC3986's and Javascript's beliefs in what is right and wrong ;)\n    return result.replace(/\\!/g, \"%21\")\n                 .replace(/\\'/g, \"%27\")\n                 .replace(/\\(/g, \"%28\")\n                 .replace(/\\)/g, \"%29\")\n                 .replace(/\\*/g, \"%2A\");\n }\n}\n\nexports.OAuth.prototype._decodeData= function(toDecode) {\n  if( toDecode != null ) {\n    toDecode = toDecode.replace(/\\+/g, \" \");\n  }\n  return decodeURIComponent( toDecode);\n}\n\nexports.OAuth.prototype._getSignature= function(method, url, parameters, tokenSecret) {\n  var signatureBase= this._createSignatureBase(method, url, parameters);\n  return this._createSignature( signatureBase, tokenSecret );\n}\n\nexports.OAuth.prototype._normalizeUrl= function(url) {\n  var parsedUrl= URL.parse(url, true)\n   var port =\"\";\n   if( parsedUrl.port ) {\n     if( (parsedUrl.protocol == \"http:\" && parsedUrl.port != \"80\" ) ||\n         (parsedUrl.protocol == \"https:\" && parsedUrl.port != \"443\") ) {\n           port= \":\" + parsedUrl.port;\n         }\n   }\n\n  if( !parsedUrl.pathname  || parsedUrl.pathname == \"\" ) parsedUrl.pathname =\"/\";\n\n  return parsedUrl.protocol + \"//\" + parsedUrl.hostname + port + parsedUrl.pathname;\n}\n\n// Is the parameter considered an OAuth parameter\nexports.OAuth.prototype._isParameterNameAnOAuthParameter= function(parameter) {\n  var m = parameter.match('^oauth_');\n  if( m && ( m[0] === \"oauth_\" ) ) {\n    return true;\n  }\n  else {\n    return false;\n  }\n};\n\n// build the OAuth request authorization header\nexports.OAuth.prototype._buildAuthorizationHeaders= function(orderedParameters) {\n  var authHeader=\"OAuth \";\n  if( this._isEcho ) {\n    authHeader += 'realm=\"' + this._realm + '\",';\n  }\n\n  for( var i= 0 ; i < orderedParameters.length; i++) {\n     // Whilst the all the parameters should be included within the signature, only the oauth_ arguments\n     // should appear within the authorization header.\n     if( this._isParameterNameAnOAuthParameter(orderedParameters[i][0]) ) {\n      authHeader+= \"\" + this._encodeData(orderedParameters[i][0])+\"=\\\"\"+ this._encodeData(orderedParameters[i][1])+\"\\\"\"+ this._oauthParameterSeperator;\n     }\n  }\n\n  authHeader= authHeader.substring(0, authHeader.length-this._oauthParameterSeperator.length);\n  return authHeader;\n}\n\n// Takes an object literal that represents the arguments, and returns an array\n// of argument/value pairs.\nexports.OAuth.prototype._makeArrayOfArgumentsHash= function(argumentsHash) {\n  var argument_pairs= [];\n  for(var key in argumentsHash ) {\n    if (argumentsHash.hasOwnProperty(key)) {\n       var value= argumentsHash[key];\n       if( Array.isArray(value) ) {\n         for(var i=0;i<value.length;i++) {\n           argument_pairs[argument_pairs.length]= [key, value[i]];\n         }\n       }\n       else {\n         argument_pairs[argument_pairs.length]= [key, value];\n       }\n    }\n  }\n  return argument_pairs;\n}\n\n// Sorts the encoded key value pairs by encoded name, then encoded value\nexports.OAuth.prototype._sortRequestParams= function(argument_pairs) {\n  // Sort by name, then value.\n  argument_pairs.sort(function(a,b) {\n      if ( a[0]== b[0] )  {\n        return a[1] < b[1] ? -1 : 1;\n      }\n      else return a[0] < b[0] ? -1 : 1;\n  });\n\n  return argument_pairs;\n}\n\nexports.OAuth.prototype._normaliseRequestParams= function(args) {\n  var argument_pairs= this._makeArrayOfArgumentsHash(args);\n  // First encode them #3.4.1.3.2 .1\n  for(var i=0;i<argument_pairs.length;i++) {\n    argument_pairs[i][0]= this._encodeData( argument_pairs[i][0] );\n    argument_pairs[i][1]= this._encodeData( argument_pairs[i][1] );\n  }\n\n  // Then sort them #3.4.1.3.2 .2\n  argument_pairs= this._sortRequestParams( argument_pairs );\n\n  // Then concatenate together #3.4.1.3.2 .3 & .4\n  var args= \"\";\n  for(var i=0;i<argument_pairs.length;i++) {\n      args+= argument_pairs[i][0];\n      args+= \"=\"\n      args+= argument_pairs[i][1];\n      if( i < argument_pairs.length-1 ) args+= \"&\";\n  }\n  return args;\n}\n\nexports.OAuth.prototype._createSignatureBase= function(method, url, parameters) {\n  url= this._encodeData( this._normalizeUrl(url) );\n  parameters= this._encodeData( parameters );\n  return method.toUpperCase() + \"&\" + url + \"&\" + parameters;\n}\n\nexports.OAuth.prototype._createSignature= function(signatureBase, tokenSecret) {\n   if( tokenSecret === undefined ) var tokenSecret= \"\";\n   else tokenSecret= this._encodeData( tokenSecret );\n   // consumerSecret is already encoded\n   var key= this._consumerSecret + \"&\" + tokenSecret;\n\n   var hash= \"\"\n   if( this._signatureMethod == \"PLAINTEXT\" ) {\n     hash= key;\n   }\n   else if (this._signatureMethod == \"RSA-SHA1\") {\n     key = this._privateKey || \"\";\n     hash= crypto.createSign(\"RSA-SHA1\").update(signatureBase).sign(key, 'base64');\n   }\n   else {\n       if( crypto.Hmac ) {\n         hash = crypto.createHmac(\"sha1\", key).update(signatureBase).digest(\"base64\");\n       }\n       else {\n         hash= sha1.HMACSHA1(key, signatureBase);\n       }\n   }\n   return hash;\n}\nexports.OAuth.prototype.NONCE_CHARS= ['a','b','c','d','e','f','g','h','i','j','k','l','m','n',\n              'o','p','q','r','s','t','u','v','w','x','y','z','A','B',\n              'C','D','E','F','G','H','I','J','K','L','M','N','O','P',\n              'Q','R','S','T','U','V','W','X','Y','Z','0','1','2','3',\n              '4','5','6','7','8','9'];\n\nexports.OAuth.prototype._getNonce= function(nonceSize) {\n   var result = [];\n   var chars= this.NONCE_CHARS;\n   var char_pos;\n   var nonce_chars_length= chars.length;\n\n   for (var i = 0; i < nonceSize; i++) {\n       char_pos= Math.floor(Math.random() * nonce_chars_length);\n       result[i]=  chars[char_pos];\n   }\n   return result.join('');\n}\n\nexports.OAuth.prototype._createClient= function( port, hostname, method, path, headers, sslEnabled ) {\n  var options = {\n    host: hostname,\n    port: port,\n    path: path,\n    method: method,\n    headers: headers\n  };\n  var httpModel;\n  if( sslEnabled ) {\n    httpModel= https;\n  } else {\n    httpModel= http;\n  }\n  return httpModel.request(options);\n}\n\nexports.OAuth.prototype._prepareParameters= function( oauth_token, oauth_token_secret, method, url, extra_params ) {\n  var oauthParameters= {\n      \"oauth_timestamp\":        this._getTimestamp(),\n      \"oauth_nonce\":            this._getNonce(this._nonceSize),\n      \"oauth_version\":          this._version,\n      \"oauth_signature_method\": this._signatureMethod,\n      \"oauth_consumer_key\":     this._consumerKey\n  };\n\n  if( oauth_token ) {\n    oauthParameters[\"oauth_token\"]= oauth_token;\n  }\n\n  var sig;\n  if( this._isEcho ) {\n    sig = this._getSignature( \"GET\",  this._verifyCredentials,  this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  }\n  else {\n    if( extra_params ) {\n      for( var key in extra_params ) {\n        if (extra_params.hasOwnProperty(key)) oauthParameters[key]= extra_params[key];\n      }\n    }\n    var parsedUrl= URL.parse( url, false );\n\n    if( parsedUrl.query ) {\n      var key2;\n      var extraParameters= querystring.parse(parsedUrl.query);\n      for(var key in extraParameters ) {\n        var value= extraParameters[key];\n          if( typeof value == \"object\" ){\n            // TODO: This probably should be recursive\n            for(key2 in value){\n              oauthParameters[key + \"[\" + key2 + \"]\"] = value[key2];\n            }\n          } else {\n            oauthParameters[key]= value;\n          }\n        }\n    }\n\n    sig = this._getSignature( method,  url,  this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  }\n\n  var orderedParameters= this._sortRequestParams( this._makeArrayOfArgumentsHash(oauthParameters) );\n  orderedParameters[orderedParameters.length]= [\"oauth_signature\", sig];\n  return orderedParameters;\n}\n\nexports.OAuth.prototype._performSecureRequest= function( oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type,  callback ) {\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, extra_params);\n\n  if( !post_content_type ) {\n    post_content_type= \"application/x-www-form-urlencoded\";\n  }\n  var parsedUrl= URL.parse( url, false );\n  if( parsedUrl.protocol == \"http:\" && !parsedUrl.port ) parsedUrl.port= 80;\n  if( parsedUrl.protocol == \"https:\" && !parsedUrl.port ) parsedUrl.port= 443;\n\n  var headers= {};\n  var authorization = this._buildAuthorizationHeaders(orderedParameters);\n  if ( this._isEcho ) {\n    headers[\"X-Verify-Credentials-Authorization\"]= authorization;\n  }\n  else {\n    headers[\"Authorization\"]= authorization;\n  }\n\n  headers[\"Host\"] = parsedUrl.host\n\n  for( var key in this._headers ) {\n    if (this._headers.hasOwnProperty(key)) {\n      headers[key]= this._headers[key];\n    }\n  }\n\n  // Filter out any passed extra_params that are really to do with OAuth\n  for(var key in extra_params) {\n    if( this._isParameterNameAnOAuthParameter( key ) ) {\n      delete extra_params[key];\n    }\n  }\n\n  if( (method == \"POST\" || method == \"PUT\")  && ( post_body == null && extra_params != null) ) {\n    // Fix the mismatch between the output of querystring.stringify() and this._encodeData()\n    post_body= querystring.stringify(extra_params)\n                       .replace(/\\!/g, \"%21\")\n                       .replace(/\\'/g, \"%27\")\n                       .replace(/\\(/g, \"%28\")\n                       .replace(/\\)/g, \"%29\")\n                       .replace(/\\*/g, \"%2A\");\n  }\n\n  if( post_body ) {\n      if ( Buffer.isBuffer(post_body) ) {\n          headers[\"Content-length\"]= post_body.length;\n      } else {\n          headers[\"Content-length\"]= Buffer.byteLength(post_body);\n      }\n  } else {\n      headers[\"Content-length\"]= 0;\n  }\n\n  headers[\"Content-Type\"]= post_content_type;\n\n  var path;\n  if( !parsedUrl.pathname  || parsedUrl.pathname == \"\" ) parsedUrl.pathname =\"/\";\n  if( parsedUrl.query ) path= parsedUrl.pathname + \"?\"+ parsedUrl.query ;\n  else path= parsedUrl.pathname;\n\n  var request;\n  if( parsedUrl.protocol == \"https:\" ) {\n    request= this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers, true);\n  }\n  else {\n    request= this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers);\n  }\n\n  var clientOptions = this._clientOptions;\n  if( callback ) {\n    var data=\"\";\n    var self= this;\n\n    // Some hosts *cough* google appear to close the connection early / send no content-length header\n    // allow this behaviour.\n    var allowEarlyClose= OAuthUtils.isAnEarlyCloseHost( parsedUrl.hostname );\n    var callbackCalled= false;\n    var passBackControl = function( response ) {\n      if(!callbackCalled) {\n        callbackCalled= true;\n        if ( response.statusCode >= 200 && response.statusCode <= 299 ) {\n          callback(null, data, response);\n        } else {\n          // Follow 301 or 302 redirects with Location HTTP header\n          if((response.statusCode == 301 || response.statusCode == 302) && clientOptions.followRedirects && response.headers && response.headers.location) {\n            self._performSecureRequest( oauth_token, oauth_token_secret, method, response.headers.location, extra_params, post_body, post_content_type,  callback);\n          }\n          else {\n            callback({ statusCode: response.statusCode, data: data }, data, response);\n          }\n        }\n      }\n    }\n\n    request.on('response', function (response) {\n      response.setEncoding('utf8');\n      response.on('data', function (chunk) {\n        data+=chunk;\n      });\n      response.on('end', function () {\n        passBackControl( response );\n      });\n      response.on('close', function () {\n        if( allowEarlyClose ) {\n          passBackControl( response );\n        }\n      });\n    });\n\n    request.on(\"error\", function(err) {\n      if(!callbackCalled) {\n        callbackCalled= true;\n        callback( err )\n      }\n    });\n\n    if( (method == \"POST\" || method ==\"PUT\") && post_body != null && post_body != \"\" ) {\n      request.write(post_body);\n    }\n    request.end();\n  }\n  else {\n    if( (method == \"POST\" || method ==\"PUT\") && post_body != null && post_body != \"\" ) {\n      request.write(post_body);\n    }\n    return request;\n  }\n\n  return;\n}\n\nexports.OAuth.prototype.setClientOptions= function(options) {\n  var key,\n      mergedOptions= {},\n      hasOwnProperty= Object.prototype.hasOwnProperty;\n\n  for( key in this._defaultClientOptions ) {\n    if( !hasOwnProperty.call(options, key) ) {\n      mergedOptions[key]= this._defaultClientOptions[key];\n    } else {\n      mergedOptions[key]= options[key];\n    }\n  }\n\n  this._clientOptions= mergedOptions;\n};\n\nexports.OAuth.prototype.getOAuthAccessToken= function(oauth_token, oauth_token_secret, oauth_verifier,  callback) {\n  var extraParams= {};\n  if( typeof oauth_verifier == \"function\" ) {\n    callback= oauth_verifier;\n  } else {\n    extraParams.oauth_verifier= oauth_verifier;\n  }\n\n   this._performSecureRequest( oauth_token, oauth_token_secret, this._clientOptions.accessTokenHttpMethod, this._accessUrl, extraParams, null, null, function(error, data, response) {\n         if( error ) callback(error);\n         else {\n           var results= querystring.parse( data );\n           var oauth_access_token= results[\"oauth_token\"];\n           delete results[\"oauth_token\"];\n           var oauth_access_token_secret= results[\"oauth_token_secret\"];\n           delete results[\"oauth_token_secret\"];\n           callback(null, oauth_access_token, oauth_access_token_secret, results );\n         }\n   })\n}\n\n// Deprecated\nexports.OAuth.prototype.getProtectedResource= function(url, method, oauth_token, oauth_token_secret, callback) {\n  this._performSecureRequest( oauth_token, oauth_token_secret, method, url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype[\"delete\"]= function(url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest( oauth_token, oauth_token_secret, \"DELETE\", url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype.get= function(url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest( oauth_token, oauth_token_secret, \"GET\", url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype._putOrPost= function(method, url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  var extra_params= null;\n  if( typeof post_content_type == \"function\" ) {\n    callback= post_content_type;\n    post_content_type= null;\n  }\n  if ( typeof post_body != \"string\" && !Buffer.isBuffer(post_body) ) {\n    post_content_type= \"application/x-www-form-urlencoded\"\n    extra_params= post_body;\n    post_body= null;\n  }\n  return this._performSecureRequest( oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback );\n}\n\n\nexports.OAuth.prototype.put= function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"PUT\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n}\n\nexports.OAuth.prototype.post= function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"POST\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n}\n\n/**\n * Gets a request token from the OAuth provider and passes that information back\n * to the calling code.\n *\n * The callback should expect a function of the following form:\n *\n * function(err, token, token_secret, parsedQueryString) {}\n *\n * This method has optional parameters so can be called in the following 2 ways:\n *\n * 1) Primary use case: Does a basic request with no extra parameters\n *  getOAuthRequestToken( callbackFunction )\n *\n * 2) As above but allows for provision of extra parameters to be sent as part of the query to the server.\n *  getOAuthRequestToken( extraParams, callbackFunction )\n *\n * N.B. This method will HTTP POST verbs by default, if you wish to override this behaviour you will\n * need to provide a requestTokenHttpMethod option when creating the client.\n *\n **/\nexports.OAuth.prototype.getOAuthRequestToken= function( extraParams, callback ) {\n   if( typeof extraParams == \"function\" ){\n     callback = extraParams;\n     extraParams = {};\n   }\n  // Callbacks are 1.0A related\n  if( this._authorize_callback ) {\n    extraParams[\"oauth_callback\"]= this._authorize_callback;\n  }\n  this._performSecureRequest( null, null, this._clientOptions.requestTokenHttpMethod, this._requestUrl, extraParams, null, null, function(error, data, response) {\n    if( error ) callback(error);\n    else {\n      var results= querystring.parse(data);\n\n      var oauth_token= results[\"oauth_token\"];\n      var oauth_token_secret= results[\"oauth_token_secret\"];\n      delete results[\"oauth_token\"];\n      delete results[\"oauth_token_secret\"];\n      callback(null, oauth_token, oauth_token_secret,  results );\n    }\n  });\n}\n\nexports.OAuth.prototype.signUrl= function(url, oauth_token, oauth_token_secret, method) {\n\n  if( method === undefined ) {\n    var method= \"GET\";\n  }\n\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  var parsedUrl= URL.parse( url, false );\n\n  var query=\"\";\n  for( var i= 0 ; i < orderedParameters.length; i++) {\n    query+= orderedParameters[i][0]+\"=\"+ this._encodeData(orderedParameters[i][1]) + \"&\";\n  }\n  query= query.substring(0, query.length-1);\n\n  return parsedUrl.protocol + \"//\"+ parsedUrl.host + parsedUrl.pathname + \"?\" + query;\n};\n\nexports.OAuth.prototype.authHeader= function(url, oauth_token, oauth_token_secret, method) {\n  if( method === undefined ) {\n    var method= \"GET\";\n  }\n\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  return this._buildAuthorizationHeaders(orderedParameters);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL29hdXRoLmpzIiwibWFwcGluZ3MiOiJBQUFBLFlBQVksbUJBQU8sQ0FBQyxzQkFBUTtBQUM1QixVQUFVLG1CQUFPLENBQUMsc0RBQVE7QUFDMUIsVUFBVSxtQkFBTyxDQUFDLGtCQUFNO0FBQ3hCLFdBQVcsbUJBQU8sQ0FBQyxvQkFBTztBQUMxQixTQUFTLG1CQUFPLENBQUMsZ0JBQUs7QUFDdEIsaUJBQWlCLG1CQUFPLENBQUMsZ0NBQWE7QUFDdEMsZ0JBQWdCLG1CQUFPLENBQUMsMERBQVU7O0FBRWxDLGFBQWE7QUFDYjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQSxvREFBb0Q7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUJBQWlCO0FBQ2pCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsMkJBQTJCOztBQUUzQixxQ0FBcUM7QUFDckM7QUFDQTs7QUFFQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0Esd0dBQXdHO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG1DQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7O0FBRUEscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLHdEQUF3RDtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0RBQWtEO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQiw4QkFBOEI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaURBQWlEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsZUFBZTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDBDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7O0FBRUEsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQSxjQUFjLHdCQUF3QjtBQUN0QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsY0FBYyx3QkFBd0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNENBQTRDO0FBQzVDO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHdDQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG1CQUFtQixlQUFlO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDBDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDZDQUE2QztBQUM3Qzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qiw2Q0FBNkM7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSx3Q0FBd0M7QUFDeEM7QUFDQSx1QkFBdUI7QUFDdkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBLDJDQUEyQztBQUMzQztBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjs7QUFFQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBOztBQUVBLGlDQUE4QjtBQUM5QjtBQUNBOztBQUVBLDJCQUEyQjtBQUMzQjtBQUNBOztBQUVBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBLDJCQUEyQjtBQUMzQjtBQUNBOztBQUVBLDRCQUE0QjtBQUM1QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQSwrQkFBK0I7O0FBRS9CO0FBQ0E7QUFDQTs7QUFFQSxpR0FBaUc7QUFDakc7O0FBRUE7QUFDQSxrQkFBa0IsOEJBQThCO0FBQ2hEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7O0FBRUEsaUdBQWlHO0FBQ2pHO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kb2N1bWVudC10cmFja2VyLy4vbm9kZV9tb2R1bGVzL29hdXRoL2xpYi9vYXV0aC5qcz8wYmJiIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBjcnlwdG89IHJlcXVpcmUoJ2NyeXB0bycpLFxuICAgIHNoYTE9IHJlcXVpcmUoJy4vc2hhMScpLFxuICAgIGh0dHA9IHJlcXVpcmUoJ2h0dHAnKSxcbiAgICBodHRwcz0gcmVxdWlyZSgnaHR0cHMnKSxcbiAgICBVUkw9IHJlcXVpcmUoJ3VybCcpLFxuICAgIHF1ZXJ5c3RyaW5nPSByZXF1aXJlKCdxdWVyeXN0cmluZycpLFxuICAgIE9BdXRoVXRpbHM9IHJlcXVpcmUoJy4vX3V0aWxzJyk7XG5cbmV4cG9ydHMuT0F1dGg9IGZ1bmN0aW9uKHJlcXVlc3RVcmwsIGFjY2Vzc1VybCwgY29uc3VtZXJLZXksIGNvbnN1bWVyU2VjcmV0LCB2ZXJzaW9uLCBhdXRob3JpemVfY2FsbGJhY2ssIHNpZ25hdHVyZU1ldGhvZCwgbm9uY2VTaXplLCBjdXN0b21IZWFkZXJzKSB7XG4gIHRoaXMuX2lzRWNobyA9IGZhbHNlO1xuXG4gIHRoaXMuX3JlcXVlc3RVcmw9IHJlcXVlc3RVcmw7XG4gIHRoaXMuX2FjY2Vzc1VybD0gYWNjZXNzVXJsO1xuICB0aGlzLl9jb25zdW1lcktleT0gY29uc3VtZXJLZXk7XG4gIHRoaXMuX2NvbnN1bWVyU2VjcmV0PSB0aGlzLl9lbmNvZGVEYXRhKCBjb25zdW1lclNlY3JldCApO1xuICBpZiAoc2lnbmF0dXJlTWV0aG9kID09IFwiUlNBLVNIQTFcIikge1xuICAgIHRoaXMuX3ByaXZhdGVLZXkgPSBjb25zdW1lclNlY3JldDtcbiAgfVxuICB0aGlzLl92ZXJzaW9uPSB2ZXJzaW9uO1xuICBpZiggYXV0aG9yaXplX2NhbGxiYWNrID09PSB1bmRlZmluZWQgKSB7XG4gICAgdGhpcy5fYXV0aG9yaXplX2NhbGxiYWNrPSBcIm9vYlwiO1xuICB9XG4gIGVsc2Uge1xuICAgIHRoaXMuX2F1dGhvcml6ZV9jYWxsYmFjaz0gYXV0aG9yaXplX2NhbGxiYWNrO1xuICB9XG5cbiAgaWYoIHNpZ25hdHVyZU1ldGhvZCAhPSBcIlBMQUlOVEVYVFwiICYmIHNpZ25hdHVyZU1ldGhvZCAhPSBcIkhNQUMtU0hBMVwiICYmIHNpZ25hdHVyZU1ldGhvZCAhPSBcIlJTQS1TSEExXCIpXG4gICAgdGhyb3cgbmV3IEVycm9yKFwiVW4tc3VwcG9ydGVkIHNpZ25hdHVyZSBtZXRob2Q6IFwiICsgc2lnbmF0dXJlTWV0aG9kIClcbiAgdGhpcy5fc2lnbmF0dXJlTWV0aG9kPSBzaWduYXR1cmVNZXRob2Q7XG4gIHRoaXMuX25vbmNlU2l6ZT0gbm9uY2VTaXplIHx8IDMyO1xuICB0aGlzLl9oZWFkZXJzPSBjdXN0b21IZWFkZXJzIHx8IHtcIkFjY2VwdFwiIDogXCIqLypcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJDb25uZWN0aW9uXCIgOiBcImNsb3NlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiVXNlci1BZ2VudFwiIDogXCJOb2RlIGF1dGhlbnRpY2F0aW9uXCJ9XG4gIHRoaXMuX2NsaWVudE9wdGlvbnM9IHRoaXMuX2RlZmF1bHRDbGllbnRPcHRpb25zPSB7XCJyZXF1ZXN0VG9rZW5IdHRwTWV0aG9kXCI6IFwiUE9TVFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiYWNjZXNzVG9rZW5IdHRwTWV0aG9kXCI6IFwiUE9TVFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiZm9sbG93UmVkaXJlY3RzXCI6IHRydWV9O1xuICB0aGlzLl9vYXV0aFBhcmFtZXRlclNlcGVyYXRvciA9IFwiLFwiO1xufTtcblxuZXhwb3J0cy5PQXV0aEVjaG89IGZ1bmN0aW9uKHJlYWxtLCB2ZXJpZnlfY3JlZGVudGlhbHMsIGNvbnN1bWVyS2V5LCBjb25zdW1lclNlY3JldCwgdmVyc2lvbiwgc2lnbmF0dXJlTWV0aG9kLCBub25jZVNpemUsIGN1c3RvbUhlYWRlcnMpIHtcbiAgdGhpcy5faXNFY2hvID0gdHJ1ZTtcblxuICB0aGlzLl9yZWFsbT0gcmVhbG07XG4gIHRoaXMuX3ZlcmlmeUNyZWRlbnRpYWxzID0gdmVyaWZ5X2NyZWRlbnRpYWxzO1xuICB0aGlzLl9jb25zdW1lcktleT0gY29uc3VtZXJLZXk7XG4gIHRoaXMuX2NvbnN1bWVyU2VjcmV0PSB0aGlzLl9lbmNvZGVEYXRhKCBjb25zdW1lclNlY3JldCApO1xuICBpZiAoc2lnbmF0dXJlTWV0aG9kID09IFwiUlNBLVNIQTFcIikge1xuICAgIHRoaXMuX3ByaXZhdGVLZXkgPSBjb25zdW1lclNlY3JldDtcbiAgfVxuICB0aGlzLl92ZXJzaW9uPSB2ZXJzaW9uO1xuXG4gIGlmKCBzaWduYXR1cmVNZXRob2QgIT0gXCJQTEFJTlRFWFRcIiAmJiBzaWduYXR1cmVNZXRob2QgIT0gXCJITUFDLVNIQTFcIiAmJiBzaWduYXR1cmVNZXRob2QgIT0gXCJSU0EtU0hBMVwiKVxuICAgIHRocm93IG5ldyBFcnJvcihcIlVuLXN1cHBvcnRlZCBzaWduYXR1cmUgbWV0aG9kOiBcIiArIHNpZ25hdHVyZU1ldGhvZCApO1xuICB0aGlzLl9zaWduYXR1cmVNZXRob2Q9IHNpZ25hdHVyZU1ldGhvZDtcbiAgdGhpcy5fbm9uY2VTaXplPSBub25jZVNpemUgfHwgMzI7XG4gIHRoaXMuX2hlYWRlcnM9IGN1c3RvbUhlYWRlcnMgfHwge1wiQWNjZXB0XCIgOiBcIiovKlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIkNvbm5lY3Rpb25cIiA6IFwiY2xvc2VcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJVc2VyLUFnZW50XCIgOiBcIk5vZGUgYXV0aGVudGljYXRpb25cIn07XG4gIHRoaXMuX29hdXRoUGFyYW1ldGVyU2VwZXJhdG9yID0gXCIsXCI7XG59XG5cbmV4cG9ydHMuT0F1dGhFY2hvLnByb3RvdHlwZSA9IGV4cG9ydHMuT0F1dGgucHJvdG90eXBlO1xuXG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5fZ2V0VGltZXN0YW1wPSBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIE1hdGguZmxvb3IoIChuZXcgRGF0ZSgpKS5nZXRUaW1lKCkgLyAxMDAwICk7XG59XG5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLl9lbmNvZGVEYXRhPSBmdW5jdGlvbih0b0VuY29kZSl7XG4gaWYoIHRvRW5jb2RlID09IG51bGwgfHwgdG9FbmNvZGUgPT0gXCJcIiApIHJldHVybiBcIlwiXG4gZWxzZSB7XG4gICAgdmFyIHJlc3VsdD0gZW5jb2RlVVJJQ29tcG9uZW50KHRvRW5jb2RlKTtcbiAgICAvLyBGaXggdGhlIG1pc21hdGNoIGJldHdlZW4gT0F1dGgncyAgUkZDMzk4NidzIGFuZCBKYXZhc2NyaXB0J3MgYmVsaWVmcyBpbiB3aGF0IGlzIHJpZ2h0IGFuZCB3cm9uZyA7KVxuICAgIHJldHVybiByZXN1bHQucmVwbGFjZSgvXFwhL2csIFwiJTIxXCIpXG4gICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXCcvZywgXCIlMjdcIilcbiAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcKC9nLCBcIiUyOFwiKVxuICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFwpL2csIFwiJTI5XCIpXG4gICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXCovZywgXCIlMkFcIik7XG4gfVxufVxuXG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5fZGVjb2RlRGF0YT0gZnVuY3Rpb24odG9EZWNvZGUpIHtcbiAgaWYoIHRvRGVjb2RlICE9IG51bGwgKSB7XG4gICAgdG9EZWNvZGUgPSB0b0RlY29kZS5yZXBsYWNlKC9cXCsvZywgXCIgXCIpO1xuICB9XG4gIHJldHVybiBkZWNvZGVVUklDb21wb25lbnQoIHRvRGVjb2RlKTtcbn1cblxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuX2dldFNpZ25hdHVyZT0gZnVuY3Rpb24obWV0aG9kLCB1cmwsIHBhcmFtZXRlcnMsIHRva2VuU2VjcmV0KSB7XG4gIHZhciBzaWduYXR1cmVCYXNlPSB0aGlzLl9jcmVhdGVTaWduYXR1cmVCYXNlKG1ldGhvZCwgdXJsLCBwYXJhbWV0ZXJzKTtcbiAgcmV0dXJuIHRoaXMuX2NyZWF0ZVNpZ25hdHVyZSggc2lnbmF0dXJlQmFzZSwgdG9rZW5TZWNyZXQgKTtcbn1cblxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuX25vcm1hbGl6ZVVybD0gZnVuY3Rpb24odXJsKSB7XG4gIHZhciBwYXJzZWRVcmw9IFVSTC5wYXJzZSh1cmwsIHRydWUpXG4gICB2YXIgcG9ydCA9XCJcIjtcbiAgIGlmKCBwYXJzZWRVcmwucG9ydCApIHtcbiAgICAgaWYoIChwYXJzZWRVcmwucHJvdG9jb2wgPT0gXCJodHRwOlwiICYmIHBhcnNlZFVybC5wb3J0ICE9IFwiODBcIiApIHx8XG4gICAgICAgICAocGFyc2VkVXJsLnByb3RvY29sID09IFwiaHR0cHM6XCIgJiYgcGFyc2VkVXJsLnBvcnQgIT0gXCI0NDNcIikgKSB7XG4gICAgICAgICAgIHBvcnQ9IFwiOlwiICsgcGFyc2VkVXJsLnBvcnQ7XG4gICAgICAgICB9XG4gICB9XG5cbiAgaWYoICFwYXJzZWRVcmwucGF0aG5hbWUgIHx8IHBhcnNlZFVybC5wYXRobmFtZSA9PSBcIlwiICkgcGFyc2VkVXJsLnBhdGhuYW1lID1cIi9cIjtcblxuICByZXR1cm4gcGFyc2VkVXJsLnByb3RvY29sICsgXCIvL1wiICsgcGFyc2VkVXJsLmhvc3RuYW1lICsgcG9ydCArIHBhcnNlZFVybC5wYXRobmFtZTtcbn1cblxuLy8gSXMgdGhlIHBhcmFtZXRlciBjb25zaWRlcmVkIGFuIE9BdXRoIHBhcmFtZXRlclxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuX2lzUGFyYW1ldGVyTmFtZUFuT0F1dGhQYXJhbWV0ZXI9IGZ1bmN0aW9uKHBhcmFtZXRlcikge1xuICB2YXIgbSA9IHBhcmFtZXRlci5tYXRjaCgnXm9hdXRoXycpO1xuICBpZiggbSAmJiAoIG1bMF0gPT09IFwib2F1dGhfXCIgKSApIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuICBlbHNlIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn07XG5cbi8vIGJ1aWxkIHRoZSBPQXV0aCByZXF1ZXN0IGF1dGhvcml6YXRpb24gaGVhZGVyXG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5fYnVpbGRBdXRob3JpemF0aW9uSGVhZGVycz0gZnVuY3Rpb24ob3JkZXJlZFBhcmFtZXRlcnMpIHtcbiAgdmFyIGF1dGhIZWFkZXI9XCJPQXV0aCBcIjtcbiAgaWYoIHRoaXMuX2lzRWNobyApIHtcbiAgICBhdXRoSGVhZGVyICs9ICdyZWFsbT1cIicgKyB0aGlzLl9yZWFsbSArICdcIiwnO1xuICB9XG5cbiAgZm9yKCB2YXIgaT0gMCA7IGkgPCBvcmRlcmVkUGFyYW1ldGVycy5sZW5ndGg7IGkrKykge1xuICAgICAvLyBXaGlsc3QgdGhlIGFsbCB0aGUgcGFyYW1ldGVycyBzaG91bGQgYmUgaW5jbHVkZWQgd2l0aGluIHRoZSBzaWduYXR1cmUsIG9ubHkgdGhlIG9hdXRoXyBhcmd1bWVudHNcbiAgICAgLy8gc2hvdWxkIGFwcGVhciB3aXRoaW4gdGhlIGF1dGhvcml6YXRpb24gaGVhZGVyLlxuICAgICBpZiggdGhpcy5faXNQYXJhbWV0ZXJOYW1lQW5PQXV0aFBhcmFtZXRlcihvcmRlcmVkUGFyYW1ldGVyc1tpXVswXSkgKSB7XG4gICAgICBhdXRoSGVhZGVyKz0gXCJcIiArIHRoaXMuX2VuY29kZURhdGEob3JkZXJlZFBhcmFtZXRlcnNbaV1bMF0pK1wiPVxcXCJcIisgdGhpcy5fZW5jb2RlRGF0YShvcmRlcmVkUGFyYW1ldGVyc1tpXVsxXSkrXCJcXFwiXCIrIHRoaXMuX29hdXRoUGFyYW1ldGVyU2VwZXJhdG9yO1xuICAgICB9XG4gIH1cblxuICBhdXRoSGVhZGVyPSBhdXRoSGVhZGVyLnN1YnN0cmluZygwLCBhdXRoSGVhZGVyLmxlbmd0aC10aGlzLl9vYXV0aFBhcmFtZXRlclNlcGVyYXRvci5sZW5ndGgpO1xuICByZXR1cm4gYXV0aEhlYWRlcjtcbn1cblxuLy8gVGFrZXMgYW4gb2JqZWN0IGxpdGVyYWwgdGhhdCByZXByZXNlbnRzIHRoZSBhcmd1bWVudHMsIGFuZCByZXR1cm5zIGFuIGFycmF5XG4vLyBvZiBhcmd1bWVudC92YWx1ZSBwYWlycy5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLl9tYWtlQXJyYXlPZkFyZ3VtZW50c0hhc2g9IGZ1bmN0aW9uKGFyZ3VtZW50c0hhc2gpIHtcbiAgdmFyIGFyZ3VtZW50X3BhaXJzPSBbXTtcbiAgZm9yKHZhciBrZXkgaW4gYXJndW1lbnRzSGFzaCApIHtcbiAgICBpZiAoYXJndW1lbnRzSGFzaC5oYXNPd25Qcm9wZXJ0eShrZXkpKSB7XG4gICAgICAgdmFyIHZhbHVlPSBhcmd1bWVudHNIYXNoW2tleV07XG4gICAgICAgaWYoIEFycmF5LmlzQXJyYXkodmFsdWUpICkge1xuICAgICAgICAgZm9yKHZhciBpPTA7aTx2YWx1ZS5sZW5ndGg7aSsrKSB7XG4gICAgICAgICAgIGFyZ3VtZW50X3BhaXJzW2FyZ3VtZW50X3BhaXJzLmxlbmd0aF09IFtrZXksIHZhbHVlW2ldXTtcbiAgICAgICAgIH1cbiAgICAgICB9XG4gICAgICAgZWxzZSB7XG4gICAgICAgICBhcmd1bWVudF9wYWlyc1thcmd1bWVudF9wYWlycy5sZW5ndGhdPSBba2V5LCB2YWx1ZV07XG4gICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gYXJndW1lbnRfcGFpcnM7XG59XG5cbi8vIFNvcnRzIHRoZSBlbmNvZGVkIGtleSB2YWx1ZSBwYWlycyBieSBlbmNvZGVkIG5hbWUsIHRoZW4gZW5jb2RlZCB2YWx1ZVxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuX3NvcnRSZXF1ZXN0UGFyYW1zPSBmdW5jdGlvbihhcmd1bWVudF9wYWlycykge1xuICAvLyBTb3J0IGJ5IG5hbWUsIHRoZW4gdmFsdWUuXG4gIGFyZ3VtZW50X3BhaXJzLnNvcnQoZnVuY3Rpb24oYSxiKSB7XG4gICAgICBpZiAoIGFbMF09PSBiWzBdICkgIHtcbiAgICAgICAgcmV0dXJuIGFbMV0gPCBiWzFdID8gLTEgOiAxO1xuICAgICAgfVxuICAgICAgZWxzZSByZXR1cm4gYVswXSA8IGJbMF0gPyAtMSA6IDE7XG4gIH0pO1xuXG4gIHJldHVybiBhcmd1bWVudF9wYWlycztcbn1cblxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuX25vcm1hbGlzZVJlcXVlc3RQYXJhbXM9IGZ1bmN0aW9uKGFyZ3MpIHtcbiAgdmFyIGFyZ3VtZW50X3BhaXJzPSB0aGlzLl9tYWtlQXJyYXlPZkFyZ3VtZW50c0hhc2goYXJncyk7XG4gIC8vIEZpcnN0IGVuY29kZSB0aGVtICMzLjQuMS4zLjIgLjFcbiAgZm9yKHZhciBpPTA7aTxhcmd1bWVudF9wYWlycy5sZW5ndGg7aSsrKSB7XG4gICAgYXJndW1lbnRfcGFpcnNbaV1bMF09IHRoaXMuX2VuY29kZURhdGEoIGFyZ3VtZW50X3BhaXJzW2ldWzBdICk7XG4gICAgYXJndW1lbnRfcGFpcnNbaV1bMV09IHRoaXMuX2VuY29kZURhdGEoIGFyZ3VtZW50X3BhaXJzW2ldWzFdICk7XG4gIH1cblxuICAvLyBUaGVuIHNvcnQgdGhlbSAjMy40LjEuMy4yIC4yXG4gIGFyZ3VtZW50X3BhaXJzPSB0aGlzLl9zb3J0UmVxdWVzdFBhcmFtcyggYXJndW1lbnRfcGFpcnMgKTtcblxuICAvLyBUaGVuIGNvbmNhdGVuYXRlIHRvZ2V0aGVyICMzLjQuMS4zLjIgLjMgJiAuNFxuICB2YXIgYXJncz0gXCJcIjtcbiAgZm9yKHZhciBpPTA7aTxhcmd1bWVudF9wYWlycy5sZW5ndGg7aSsrKSB7XG4gICAgICBhcmdzKz0gYXJndW1lbnRfcGFpcnNbaV1bMF07XG4gICAgICBhcmdzKz0gXCI9XCJcbiAgICAgIGFyZ3MrPSBhcmd1bWVudF9wYWlyc1tpXVsxXTtcbiAgICAgIGlmKCBpIDwgYXJndW1lbnRfcGFpcnMubGVuZ3RoLTEgKSBhcmdzKz0gXCImXCI7XG4gIH1cbiAgcmV0dXJuIGFyZ3M7XG59XG5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLl9jcmVhdGVTaWduYXR1cmVCYXNlPSBmdW5jdGlvbihtZXRob2QsIHVybCwgcGFyYW1ldGVycykge1xuICB1cmw9IHRoaXMuX2VuY29kZURhdGEoIHRoaXMuX25vcm1hbGl6ZVVybCh1cmwpICk7XG4gIHBhcmFtZXRlcnM9IHRoaXMuX2VuY29kZURhdGEoIHBhcmFtZXRlcnMgKTtcbiAgcmV0dXJuIG1ldGhvZC50b1VwcGVyQ2FzZSgpICsgXCImXCIgKyB1cmwgKyBcIiZcIiArIHBhcmFtZXRlcnM7XG59XG5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLl9jcmVhdGVTaWduYXR1cmU9IGZ1bmN0aW9uKHNpZ25hdHVyZUJhc2UsIHRva2VuU2VjcmV0KSB7XG4gICBpZiggdG9rZW5TZWNyZXQgPT09IHVuZGVmaW5lZCApIHZhciB0b2tlblNlY3JldD0gXCJcIjtcbiAgIGVsc2UgdG9rZW5TZWNyZXQ9IHRoaXMuX2VuY29kZURhdGEoIHRva2VuU2VjcmV0ICk7XG4gICAvLyBjb25zdW1lclNlY3JldCBpcyBhbHJlYWR5IGVuY29kZWRcbiAgIHZhciBrZXk9IHRoaXMuX2NvbnN1bWVyU2VjcmV0ICsgXCImXCIgKyB0b2tlblNlY3JldDtcblxuICAgdmFyIGhhc2g9IFwiXCJcbiAgIGlmKCB0aGlzLl9zaWduYXR1cmVNZXRob2QgPT0gXCJQTEFJTlRFWFRcIiApIHtcbiAgICAgaGFzaD0ga2V5O1xuICAgfVxuICAgZWxzZSBpZiAodGhpcy5fc2lnbmF0dXJlTWV0aG9kID09IFwiUlNBLVNIQTFcIikge1xuICAgICBrZXkgPSB0aGlzLl9wcml2YXRlS2V5IHx8IFwiXCI7XG4gICAgIGhhc2g9IGNyeXB0by5jcmVhdGVTaWduKFwiUlNBLVNIQTFcIikudXBkYXRlKHNpZ25hdHVyZUJhc2UpLnNpZ24oa2V5LCAnYmFzZTY0Jyk7XG4gICB9XG4gICBlbHNlIHtcbiAgICAgICBpZiggY3J5cHRvLkhtYWMgKSB7XG4gICAgICAgICBoYXNoID0gY3J5cHRvLmNyZWF0ZUhtYWMoXCJzaGExXCIsIGtleSkudXBkYXRlKHNpZ25hdHVyZUJhc2UpLmRpZ2VzdChcImJhc2U2NFwiKTtcbiAgICAgICB9XG4gICAgICAgZWxzZSB7XG4gICAgICAgICBoYXNoPSBzaGExLkhNQUNTSEExKGtleSwgc2lnbmF0dXJlQmFzZSk7XG4gICAgICAgfVxuICAgfVxuICAgcmV0dXJuIGhhc2g7XG59XG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5OT05DRV9DSEFSUz0gWydhJywnYicsJ2MnLCdkJywnZScsJ2YnLCdnJywnaCcsJ2knLCdqJywnaycsJ2wnLCdtJywnbicsXG4gICAgICAgICAgICAgICdvJywncCcsJ3EnLCdyJywncycsJ3QnLCd1JywndicsJ3cnLCd4JywneScsJ3onLCdBJywnQicsXG4gICAgICAgICAgICAgICdDJywnRCcsJ0UnLCdGJywnRycsJ0gnLCdJJywnSicsJ0snLCdMJywnTScsJ04nLCdPJywnUCcsXG4gICAgICAgICAgICAgICdRJywnUicsJ1MnLCdUJywnVScsJ1YnLCdXJywnWCcsJ1knLCdaJywnMCcsJzEnLCcyJywnMycsXG4gICAgICAgICAgICAgICc0JywnNScsJzYnLCc3JywnOCcsJzknXTtcblxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuX2dldE5vbmNlPSBmdW5jdGlvbihub25jZVNpemUpIHtcbiAgIHZhciByZXN1bHQgPSBbXTtcbiAgIHZhciBjaGFycz0gdGhpcy5OT05DRV9DSEFSUztcbiAgIHZhciBjaGFyX3BvcztcbiAgIHZhciBub25jZV9jaGFyc19sZW5ndGg9IGNoYXJzLmxlbmd0aDtcblxuICAgZm9yICh2YXIgaSA9IDA7IGkgPCBub25jZVNpemU7IGkrKykge1xuICAgICAgIGNoYXJfcG9zPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBub25jZV9jaGFyc19sZW5ndGgpO1xuICAgICAgIHJlc3VsdFtpXT0gIGNoYXJzW2NoYXJfcG9zXTtcbiAgIH1cbiAgIHJldHVybiByZXN1bHQuam9pbignJyk7XG59XG5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLl9jcmVhdGVDbGllbnQ9IGZ1bmN0aW9uKCBwb3J0LCBob3N0bmFtZSwgbWV0aG9kLCBwYXRoLCBoZWFkZXJzLCBzc2xFbmFibGVkICkge1xuICB2YXIgb3B0aW9ucyA9IHtcbiAgICBob3N0OiBob3N0bmFtZSxcbiAgICBwb3J0OiBwb3J0LFxuICAgIHBhdGg6IHBhdGgsXG4gICAgbWV0aG9kOiBtZXRob2QsXG4gICAgaGVhZGVyczogaGVhZGVyc1xuICB9O1xuICB2YXIgaHR0cE1vZGVsO1xuICBpZiggc3NsRW5hYmxlZCApIHtcbiAgICBodHRwTW9kZWw9IGh0dHBzO1xuICB9IGVsc2Uge1xuICAgIGh0dHBNb2RlbD0gaHR0cDtcbiAgfVxuICByZXR1cm4gaHR0cE1vZGVsLnJlcXVlc3Qob3B0aW9ucyk7XG59XG5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLl9wcmVwYXJlUGFyYW1ldGVycz0gZnVuY3Rpb24oIG9hdXRoX3Rva2VuLCBvYXV0aF90b2tlbl9zZWNyZXQsIG1ldGhvZCwgdXJsLCBleHRyYV9wYXJhbXMgKSB7XG4gIHZhciBvYXV0aFBhcmFtZXRlcnM9IHtcbiAgICAgIFwib2F1dGhfdGltZXN0YW1wXCI6ICAgICAgICB0aGlzLl9nZXRUaW1lc3RhbXAoKSxcbiAgICAgIFwib2F1dGhfbm9uY2VcIjogICAgICAgICAgICB0aGlzLl9nZXROb25jZSh0aGlzLl9ub25jZVNpemUpLFxuICAgICAgXCJvYXV0aF92ZXJzaW9uXCI6ICAgICAgICAgIHRoaXMuX3ZlcnNpb24sXG4gICAgICBcIm9hdXRoX3NpZ25hdHVyZV9tZXRob2RcIjogdGhpcy5fc2lnbmF0dXJlTWV0aG9kLFxuICAgICAgXCJvYXV0aF9jb25zdW1lcl9rZXlcIjogICAgIHRoaXMuX2NvbnN1bWVyS2V5XG4gIH07XG5cbiAgaWYoIG9hdXRoX3Rva2VuICkge1xuICAgIG9hdXRoUGFyYW1ldGVyc1tcIm9hdXRoX3Rva2VuXCJdPSBvYXV0aF90b2tlbjtcbiAgfVxuXG4gIHZhciBzaWc7XG4gIGlmKCB0aGlzLl9pc0VjaG8gKSB7XG4gICAgc2lnID0gdGhpcy5fZ2V0U2lnbmF0dXJlKCBcIkdFVFwiLCAgdGhpcy5fdmVyaWZ5Q3JlZGVudGlhbHMsICB0aGlzLl9ub3JtYWxpc2VSZXF1ZXN0UGFyYW1zKG9hdXRoUGFyYW1ldGVycyksIG9hdXRoX3Rva2VuX3NlY3JldCk7XG4gIH1cbiAgZWxzZSB7XG4gICAgaWYoIGV4dHJhX3BhcmFtcyApIHtcbiAgICAgIGZvciggdmFyIGtleSBpbiBleHRyYV9wYXJhbXMgKSB7XG4gICAgICAgIGlmIChleHRyYV9wYXJhbXMuaGFzT3duUHJvcGVydHkoa2V5KSkgb2F1dGhQYXJhbWV0ZXJzW2tleV09IGV4dHJhX3BhcmFtc1trZXldO1xuICAgICAgfVxuICAgIH1cbiAgICB2YXIgcGFyc2VkVXJsPSBVUkwucGFyc2UoIHVybCwgZmFsc2UgKTtcblxuICAgIGlmKCBwYXJzZWRVcmwucXVlcnkgKSB7XG4gICAgICB2YXIga2V5MjtcbiAgICAgIHZhciBleHRyYVBhcmFtZXRlcnM9IHF1ZXJ5c3RyaW5nLnBhcnNlKHBhcnNlZFVybC5xdWVyeSk7XG4gICAgICBmb3IodmFyIGtleSBpbiBleHRyYVBhcmFtZXRlcnMgKSB7XG4gICAgICAgIHZhciB2YWx1ZT0gZXh0cmFQYXJhbWV0ZXJzW2tleV07XG4gICAgICAgICAgaWYoIHR5cGVvZiB2YWx1ZSA9PSBcIm9iamVjdFwiICl7XG4gICAgICAgICAgICAvLyBUT0RPOiBUaGlzIHByb2JhYmx5IHNob3VsZCBiZSByZWN1cnNpdmVcbiAgICAgICAgICAgIGZvcihrZXkyIGluIHZhbHVlKXtcbiAgICAgICAgICAgICAgb2F1dGhQYXJhbWV0ZXJzW2tleSArIFwiW1wiICsga2V5MiArIFwiXVwiXSA9IHZhbHVlW2tleTJdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBvYXV0aFBhcmFtZXRlcnNba2V5XT0gdmFsdWU7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgc2lnID0gdGhpcy5fZ2V0U2lnbmF0dXJlKCBtZXRob2QsICB1cmwsICB0aGlzLl9ub3JtYWxpc2VSZXF1ZXN0UGFyYW1zKG9hdXRoUGFyYW1ldGVycyksIG9hdXRoX3Rva2VuX3NlY3JldCk7XG4gIH1cblxuICB2YXIgb3JkZXJlZFBhcmFtZXRlcnM9IHRoaXMuX3NvcnRSZXF1ZXN0UGFyYW1zKCB0aGlzLl9tYWtlQXJyYXlPZkFyZ3VtZW50c0hhc2gob2F1dGhQYXJhbWV0ZXJzKSApO1xuICBvcmRlcmVkUGFyYW1ldGVyc1tvcmRlcmVkUGFyYW1ldGVycy5sZW5ndGhdPSBbXCJvYXV0aF9zaWduYXR1cmVcIiwgc2lnXTtcbiAgcmV0dXJuIG9yZGVyZWRQYXJhbWV0ZXJzO1xufVxuXG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5fcGVyZm9ybVNlY3VyZVJlcXVlc3Q9IGZ1bmN0aW9uKCBvYXV0aF90b2tlbiwgb2F1dGhfdG9rZW5fc2VjcmV0LCBtZXRob2QsIHVybCwgZXh0cmFfcGFyYW1zLCBwb3N0X2JvZHksIHBvc3RfY29udGVudF90eXBlLCAgY2FsbGJhY2sgKSB7XG4gIHZhciBvcmRlcmVkUGFyYW1ldGVycz0gdGhpcy5fcHJlcGFyZVBhcmFtZXRlcnMob2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgbWV0aG9kLCB1cmwsIGV4dHJhX3BhcmFtcyk7XG5cbiAgaWYoICFwb3N0X2NvbnRlbnRfdHlwZSApIHtcbiAgICBwb3N0X2NvbnRlbnRfdHlwZT0gXCJhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWRcIjtcbiAgfVxuICB2YXIgcGFyc2VkVXJsPSBVUkwucGFyc2UoIHVybCwgZmFsc2UgKTtcbiAgaWYoIHBhcnNlZFVybC5wcm90b2NvbCA9PSBcImh0dHA6XCIgJiYgIXBhcnNlZFVybC5wb3J0ICkgcGFyc2VkVXJsLnBvcnQ9IDgwO1xuICBpZiggcGFyc2VkVXJsLnByb3RvY29sID09IFwiaHR0cHM6XCIgJiYgIXBhcnNlZFVybC5wb3J0ICkgcGFyc2VkVXJsLnBvcnQ9IDQ0MztcblxuICB2YXIgaGVhZGVycz0ge307XG4gIHZhciBhdXRob3JpemF0aW9uID0gdGhpcy5fYnVpbGRBdXRob3JpemF0aW9uSGVhZGVycyhvcmRlcmVkUGFyYW1ldGVycyk7XG4gIGlmICggdGhpcy5faXNFY2hvICkge1xuICAgIGhlYWRlcnNbXCJYLVZlcmlmeS1DcmVkZW50aWFscy1BdXRob3JpemF0aW9uXCJdPSBhdXRob3JpemF0aW9uO1xuICB9XG4gIGVsc2Uge1xuICAgIGhlYWRlcnNbXCJBdXRob3JpemF0aW9uXCJdPSBhdXRob3JpemF0aW9uO1xuICB9XG5cbiAgaGVhZGVyc1tcIkhvc3RcIl0gPSBwYXJzZWRVcmwuaG9zdFxuXG4gIGZvciggdmFyIGtleSBpbiB0aGlzLl9oZWFkZXJzICkge1xuICAgIGlmICh0aGlzLl9oZWFkZXJzLmhhc093blByb3BlcnR5KGtleSkpIHtcbiAgICAgIGhlYWRlcnNba2V5XT0gdGhpcy5faGVhZGVyc1trZXldO1xuICAgIH1cbiAgfVxuXG4gIC8vIEZpbHRlciBvdXQgYW55IHBhc3NlZCBleHRyYV9wYXJhbXMgdGhhdCBhcmUgcmVhbGx5IHRvIGRvIHdpdGggT0F1dGhcbiAgZm9yKHZhciBrZXkgaW4gZXh0cmFfcGFyYW1zKSB7XG4gICAgaWYoIHRoaXMuX2lzUGFyYW1ldGVyTmFtZUFuT0F1dGhQYXJhbWV0ZXIoIGtleSApICkge1xuICAgICAgZGVsZXRlIGV4dHJhX3BhcmFtc1trZXldO1xuICAgIH1cbiAgfVxuXG4gIGlmKCAobWV0aG9kID09IFwiUE9TVFwiIHx8IG1ldGhvZCA9PSBcIlBVVFwiKSAgJiYgKCBwb3N0X2JvZHkgPT0gbnVsbCAmJiBleHRyYV9wYXJhbXMgIT0gbnVsbCkgKSB7XG4gICAgLy8gRml4IHRoZSBtaXNtYXRjaCBiZXR3ZWVuIHRoZSBvdXRwdXQgb2YgcXVlcnlzdHJpbmcuc3RyaW5naWZ5KCkgYW5kIHRoaXMuX2VuY29kZURhdGEoKVxuICAgIHBvc3RfYm9keT0gcXVlcnlzdHJpbmcuc3RyaW5naWZ5KGV4dHJhX3BhcmFtcylcbiAgICAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcIS9nLCBcIiUyMVwiKVxuICAgICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFwnL2csIFwiJTI3XCIpXG4gICAgICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXCgvZywgXCIlMjhcIilcbiAgICAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcKS9nLCBcIiUyOVwiKVxuICAgICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFwqL2csIFwiJTJBXCIpO1xuICB9XG5cbiAgaWYoIHBvc3RfYm9keSApIHtcbiAgICAgIGlmICggQnVmZmVyLmlzQnVmZmVyKHBvc3RfYm9keSkgKSB7XG4gICAgICAgICAgaGVhZGVyc1tcIkNvbnRlbnQtbGVuZ3RoXCJdPSBwb3N0X2JvZHkubGVuZ3RoO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBoZWFkZXJzW1wiQ29udGVudC1sZW5ndGhcIl09IEJ1ZmZlci5ieXRlTGVuZ3RoKHBvc3RfYm9keSk7XG4gICAgICB9XG4gIH0gZWxzZSB7XG4gICAgICBoZWFkZXJzW1wiQ29udGVudC1sZW5ndGhcIl09IDA7XG4gIH1cblxuICBoZWFkZXJzW1wiQ29udGVudC1UeXBlXCJdPSBwb3N0X2NvbnRlbnRfdHlwZTtcblxuICB2YXIgcGF0aDtcbiAgaWYoICFwYXJzZWRVcmwucGF0aG5hbWUgIHx8IHBhcnNlZFVybC5wYXRobmFtZSA9PSBcIlwiICkgcGFyc2VkVXJsLnBhdGhuYW1lID1cIi9cIjtcbiAgaWYoIHBhcnNlZFVybC5xdWVyeSApIHBhdGg9IHBhcnNlZFVybC5wYXRobmFtZSArIFwiP1wiKyBwYXJzZWRVcmwucXVlcnkgO1xuICBlbHNlIHBhdGg9IHBhcnNlZFVybC5wYXRobmFtZTtcblxuICB2YXIgcmVxdWVzdDtcbiAgaWYoIHBhcnNlZFVybC5wcm90b2NvbCA9PSBcImh0dHBzOlwiICkge1xuICAgIHJlcXVlc3Q9IHRoaXMuX2NyZWF0ZUNsaWVudChwYXJzZWRVcmwucG9ydCwgcGFyc2VkVXJsLmhvc3RuYW1lLCBtZXRob2QsIHBhdGgsIGhlYWRlcnMsIHRydWUpO1xuICB9XG4gIGVsc2Uge1xuICAgIHJlcXVlc3Q9IHRoaXMuX2NyZWF0ZUNsaWVudChwYXJzZWRVcmwucG9ydCwgcGFyc2VkVXJsLmhvc3RuYW1lLCBtZXRob2QsIHBhdGgsIGhlYWRlcnMpO1xuICB9XG5cbiAgdmFyIGNsaWVudE9wdGlvbnMgPSB0aGlzLl9jbGllbnRPcHRpb25zO1xuICBpZiggY2FsbGJhY2sgKSB7XG4gICAgdmFyIGRhdGE9XCJcIjtcbiAgICB2YXIgc2VsZj0gdGhpcztcblxuICAgIC8vIFNvbWUgaG9zdHMgKmNvdWdoKiBnb29nbGUgYXBwZWFyIHRvIGNsb3NlIHRoZSBjb25uZWN0aW9uIGVhcmx5IC8gc2VuZCBubyBjb250ZW50LWxlbmd0aCBoZWFkZXJcbiAgICAvLyBhbGxvdyB0aGlzIGJlaGF2aW91ci5cbiAgICB2YXIgYWxsb3dFYXJseUNsb3NlPSBPQXV0aFV0aWxzLmlzQW5FYXJseUNsb3NlSG9zdCggcGFyc2VkVXJsLmhvc3RuYW1lICk7XG4gICAgdmFyIGNhbGxiYWNrQ2FsbGVkPSBmYWxzZTtcbiAgICB2YXIgcGFzc0JhY2tDb250cm9sID0gZnVuY3Rpb24oIHJlc3BvbnNlICkge1xuICAgICAgaWYoIWNhbGxiYWNrQ2FsbGVkKSB7XG4gICAgICAgIGNhbGxiYWNrQ2FsbGVkPSB0cnVlO1xuICAgICAgICBpZiAoIHJlc3BvbnNlLnN0YXR1c0NvZGUgPj0gMjAwICYmIHJlc3BvbnNlLnN0YXR1c0NvZGUgPD0gMjk5ICkge1xuICAgICAgICAgIGNhbGxiYWNrKG51bGwsIGRhdGEsIHJlc3BvbnNlKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBGb2xsb3cgMzAxIG9yIDMwMiByZWRpcmVjdHMgd2l0aCBMb2NhdGlvbiBIVFRQIGhlYWRlclxuICAgICAgICAgIGlmKChyZXNwb25zZS5zdGF0dXNDb2RlID09IDMwMSB8fCByZXNwb25zZS5zdGF0dXNDb2RlID09IDMwMikgJiYgY2xpZW50T3B0aW9ucy5mb2xsb3dSZWRpcmVjdHMgJiYgcmVzcG9uc2UuaGVhZGVycyAmJiByZXNwb25zZS5oZWFkZXJzLmxvY2F0aW9uKSB7XG4gICAgICAgICAgICBzZWxmLl9wZXJmb3JtU2VjdXJlUmVxdWVzdCggb2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgbWV0aG9kLCByZXNwb25zZS5oZWFkZXJzLmxvY2F0aW9uLCBleHRyYV9wYXJhbXMsIHBvc3RfYm9keSwgcG9zdF9jb250ZW50X3R5cGUsICBjYWxsYmFjayk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY2FsbGJhY2soeyBzdGF0dXNDb2RlOiByZXNwb25zZS5zdGF0dXNDb2RlLCBkYXRhOiBkYXRhIH0sIGRhdGEsIHJlc3BvbnNlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXF1ZXN0Lm9uKCdyZXNwb25zZScsIGZ1bmN0aW9uIChyZXNwb25zZSkge1xuICAgICAgcmVzcG9uc2Uuc2V0RW5jb2RpbmcoJ3V0ZjgnKTtcbiAgICAgIHJlc3BvbnNlLm9uKCdkYXRhJywgZnVuY3Rpb24gKGNodW5rKSB7XG4gICAgICAgIGRhdGErPWNodW5rO1xuICAgICAgfSk7XG4gICAgICByZXNwb25zZS5vbignZW5kJywgZnVuY3Rpb24gKCkge1xuICAgICAgICBwYXNzQmFja0NvbnRyb2woIHJlc3BvbnNlICk7XG4gICAgICB9KTtcbiAgICAgIHJlc3BvbnNlLm9uKCdjbG9zZScsIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgaWYoIGFsbG93RWFybHlDbG9zZSApIHtcbiAgICAgICAgICBwYXNzQmFja0NvbnRyb2woIHJlc3BvbnNlICk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH0pO1xuXG4gICAgcmVxdWVzdC5vbihcImVycm9yXCIsIGZ1bmN0aW9uKGVycikge1xuICAgICAgaWYoIWNhbGxiYWNrQ2FsbGVkKSB7XG4gICAgICAgIGNhbGxiYWNrQ2FsbGVkPSB0cnVlO1xuICAgICAgICBjYWxsYmFjayggZXJyIClcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGlmKCAobWV0aG9kID09IFwiUE9TVFwiIHx8IG1ldGhvZCA9PVwiUFVUXCIpICYmIHBvc3RfYm9keSAhPSBudWxsICYmIHBvc3RfYm9keSAhPSBcIlwiICkge1xuICAgICAgcmVxdWVzdC53cml0ZShwb3N0X2JvZHkpO1xuICAgIH1cbiAgICByZXF1ZXN0LmVuZCgpO1xuICB9XG4gIGVsc2Uge1xuICAgIGlmKCAobWV0aG9kID09IFwiUE9TVFwiIHx8IG1ldGhvZCA9PVwiUFVUXCIpICYmIHBvc3RfYm9keSAhPSBudWxsICYmIHBvc3RfYm9keSAhPSBcIlwiICkge1xuICAgICAgcmVxdWVzdC53cml0ZShwb3N0X2JvZHkpO1xuICAgIH1cbiAgICByZXR1cm4gcmVxdWVzdDtcbiAgfVxuXG4gIHJldHVybjtcbn1cblxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuc2V0Q2xpZW50T3B0aW9ucz0gZnVuY3Rpb24ob3B0aW9ucykge1xuICB2YXIga2V5LFxuICAgICAgbWVyZ2VkT3B0aW9ucz0ge30sXG4gICAgICBoYXNPd25Qcm9wZXJ0eT0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eTtcblxuICBmb3IoIGtleSBpbiB0aGlzLl9kZWZhdWx0Q2xpZW50T3B0aW9ucyApIHtcbiAgICBpZiggIWhhc093blByb3BlcnR5LmNhbGwob3B0aW9ucywga2V5KSApIHtcbiAgICAgIG1lcmdlZE9wdGlvbnNba2V5XT0gdGhpcy5fZGVmYXVsdENsaWVudE9wdGlvbnNba2V5XTtcbiAgICB9IGVsc2Uge1xuICAgICAgbWVyZ2VkT3B0aW9uc1trZXldPSBvcHRpb25zW2tleV07XG4gICAgfVxuICB9XG5cbiAgdGhpcy5fY2xpZW50T3B0aW9ucz0gbWVyZ2VkT3B0aW9ucztcbn07XG5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLmdldE9BdXRoQWNjZXNzVG9rZW49IGZ1bmN0aW9uKG9hdXRoX3Rva2VuLCBvYXV0aF90b2tlbl9zZWNyZXQsIG9hdXRoX3ZlcmlmaWVyLCAgY2FsbGJhY2spIHtcbiAgdmFyIGV4dHJhUGFyYW1zPSB7fTtcbiAgaWYoIHR5cGVvZiBvYXV0aF92ZXJpZmllciA9PSBcImZ1bmN0aW9uXCIgKSB7XG4gICAgY2FsbGJhY2s9IG9hdXRoX3ZlcmlmaWVyO1xuICB9IGVsc2Uge1xuICAgIGV4dHJhUGFyYW1zLm9hdXRoX3ZlcmlmaWVyPSBvYXV0aF92ZXJpZmllcjtcbiAgfVxuXG4gICB0aGlzLl9wZXJmb3JtU2VjdXJlUmVxdWVzdCggb2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgdGhpcy5fY2xpZW50T3B0aW9ucy5hY2Nlc3NUb2tlbkh0dHBNZXRob2QsIHRoaXMuX2FjY2Vzc1VybCwgZXh0cmFQYXJhbXMsIG51bGwsIG51bGwsIGZ1bmN0aW9uKGVycm9yLCBkYXRhLCByZXNwb25zZSkge1xuICAgICAgICAgaWYoIGVycm9yICkgY2FsbGJhY2soZXJyb3IpO1xuICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgIHZhciByZXN1bHRzPSBxdWVyeXN0cmluZy5wYXJzZSggZGF0YSApO1xuICAgICAgICAgICB2YXIgb2F1dGhfYWNjZXNzX3Rva2VuPSByZXN1bHRzW1wib2F1dGhfdG9rZW5cIl07XG4gICAgICAgICAgIGRlbGV0ZSByZXN1bHRzW1wib2F1dGhfdG9rZW5cIl07XG4gICAgICAgICAgIHZhciBvYXV0aF9hY2Nlc3NfdG9rZW5fc2VjcmV0PSByZXN1bHRzW1wib2F1dGhfdG9rZW5fc2VjcmV0XCJdO1xuICAgICAgICAgICBkZWxldGUgcmVzdWx0c1tcIm9hdXRoX3Rva2VuX3NlY3JldFwiXTtcbiAgICAgICAgICAgY2FsbGJhY2sobnVsbCwgb2F1dGhfYWNjZXNzX3Rva2VuLCBvYXV0aF9hY2Nlc3NfdG9rZW5fc2VjcmV0LCByZXN1bHRzICk7XG4gICAgICAgICB9XG4gICB9KVxufVxuXG4vLyBEZXByZWNhdGVkXG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5nZXRQcm90ZWN0ZWRSZXNvdXJjZT0gZnVuY3Rpb24odXJsLCBtZXRob2QsIG9hdXRoX3Rva2VuLCBvYXV0aF90b2tlbl9zZWNyZXQsIGNhbGxiYWNrKSB7XG4gIHRoaXMuX3BlcmZvcm1TZWN1cmVSZXF1ZXN0KCBvYXV0aF90b2tlbiwgb2F1dGhfdG9rZW5fc2VjcmV0LCBtZXRob2QsIHVybCwgbnVsbCwgXCJcIiwgbnVsbCwgY2FsbGJhY2sgKTtcbn1cblxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuZGVsZXRlPSBmdW5jdGlvbih1cmwsIG9hdXRoX3Rva2VuLCBvYXV0aF90b2tlbl9zZWNyZXQsIGNhbGxiYWNrKSB7XG4gIHJldHVybiB0aGlzLl9wZXJmb3JtU2VjdXJlUmVxdWVzdCggb2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgXCJERUxFVEVcIiwgdXJsLCBudWxsLCBcIlwiLCBudWxsLCBjYWxsYmFjayApO1xufVxuXG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5nZXQ9IGZ1bmN0aW9uKHVybCwgb2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgY2FsbGJhY2spIHtcbiAgcmV0dXJuIHRoaXMuX3BlcmZvcm1TZWN1cmVSZXF1ZXN0KCBvYXV0aF90b2tlbiwgb2F1dGhfdG9rZW5fc2VjcmV0LCBcIkdFVFwiLCB1cmwsIG51bGwsIFwiXCIsIG51bGwsIGNhbGxiYWNrICk7XG59XG5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLl9wdXRPclBvc3Q9IGZ1bmN0aW9uKG1ldGhvZCwgdXJsLCBvYXV0aF90b2tlbiwgb2F1dGhfdG9rZW5fc2VjcmV0LCBwb3N0X2JvZHksIHBvc3RfY29udGVudF90eXBlLCBjYWxsYmFjaykge1xuICB2YXIgZXh0cmFfcGFyYW1zPSBudWxsO1xuICBpZiggdHlwZW9mIHBvc3RfY29udGVudF90eXBlID09IFwiZnVuY3Rpb25cIiApIHtcbiAgICBjYWxsYmFjaz0gcG9zdF9jb250ZW50X3R5cGU7XG4gICAgcG9zdF9jb250ZW50X3R5cGU9IG51bGw7XG4gIH1cbiAgaWYgKCB0eXBlb2YgcG9zdF9ib2R5ICE9IFwic3RyaW5nXCIgJiYgIUJ1ZmZlci5pc0J1ZmZlcihwb3N0X2JvZHkpICkge1xuICAgIHBvc3RfY29udGVudF90eXBlPSBcImFwcGxpY2F0aW9uL3gtd3d3LWZvcm0tdXJsZW5jb2RlZFwiXG4gICAgZXh0cmFfcGFyYW1zPSBwb3N0X2JvZHk7XG4gICAgcG9zdF9ib2R5PSBudWxsO1xuICB9XG4gIHJldHVybiB0aGlzLl9wZXJmb3JtU2VjdXJlUmVxdWVzdCggb2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgbWV0aG9kLCB1cmwsIGV4dHJhX3BhcmFtcywgcG9zdF9ib2R5LCBwb3N0X2NvbnRlbnRfdHlwZSwgY2FsbGJhY2sgKTtcbn1cblxuXG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5wdXQ9IGZ1bmN0aW9uKHVybCwgb2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgcG9zdF9ib2R5LCBwb3N0X2NvbnRlbnRfdHlwZSwgY2FsbGJhY2spIHtcbiAgcmV0dXJuIHRoaXMuX3B1dE9yUG9zdChcIlBVVFwiLCB1cmwsIG9hdXRoX3Rva2VuLCBvYXV0aF90b2tlbl9zZWNyZXQsIHBvc3RfYm9keSwgcG9zdF9jb250ZW50X3R5cGUsIGNhbGxiYWNrKTtcbn1cblxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUucG9zdD0gZnVuY3Rpb24odXJsLCBvYXV0aF90b2tlbiwgb2F1dGhfdG9rZW5fc2VjcmV0LCBwb3N0X2JvZHksIHBvc3RfY29udGVudF90eXBlLCBjYWxsYmFjaykge1xuICByZXR1cm4gdGhpcy5fcHV0T3JQb3N0KFwiUE9TVFwiLCB1cmwsIG9hdXRoX3Rva2VuLCBvYXV0aF90b2tlbl9zZWNyZXQsIHBvc3RfYm9keSwgcG9zdF9jb250ZW50X3R5cGUsIGNhbGxiYWNrKTtcbn1cblxuLyoqXG4gKiBHZXRzIGEgcmVxdWVzdCB0b2tlbiBmcm9tIHRoZSBPQXV0aCBwcm92aWRlciBhbmQgcGFzc2VzIHRoYXQgaW5mb3JtYXRpb24gYmFja1xuICogdG8gdGhlIGNhbGxpbmcgY29kZS5cbiAqXG4gKiBUaGUgY2FsbGJhY2sgc2hvdWxkIGV4cGVjdCBhIGZ1bmN0aW9uIG9mIHRoZSBmb2xsb3dpbmcgZm9ybTpcbiAqXG4gKiBmdW5jdGlvbihlcnIsIHRva2VuLCB0b2tlbl9zZWNyZXQsIHBhcnNlZFF1ZXJ5U3RyaW5nKSB7fVxuICpcbiAqIFRoaXMgbWV0aG9kIGhhcyBvcHRpb25hbCBwYXJhbWV0ZXJzIHNvIGNhbiBiZSBjYWxsZWQgaW4gdGhlIGZvbGxvd2luZyAyIHdheXM6XG4gKlxuICogMSkgUHJpbWFyeSB1c2UgY2FzZTogRG9lcyBhIGJhc2ljIHJlcXVlc3Qgd2l0aCBubyBleHRyYSBwYXJhbWV0ZXJzXG4gKiAgZ2V0T0F1dGhSZXF1ZXN0VG9rZW4oIGNhbGxiYWNrRnVuY3Rpb24gKVxuICpcbiAqIDIpIEFzIGFib3ZlIGJ1dCBhbGxvd3MgZm9yIHByb3Zpc2lvbiBvZiBleHRyYSBwYXJhbWV0ZXJzIHRvIGJlIHNlbnQgYXMgcGFydCBvZiB0aGUgcXVlcnkgdG8gdGhlIHNlcnZlci5cbiAqICBnZXRPQXV0aFJlcXVlc3RUb2tlbiggZXh0cmFQYXJhbXMsIGNhbGxiYWNrRnVuY3Rpb24gKVxuICpcbiAqIE4uQi4gVGhpcyBtZXRob2Qgd2lsbCBIVFRQIFBPU1QgdmVyYnMgYnkgZGVmYXVsdCwgaWYgeW91IHdpc2ggdG8gb3ZlcnJpZGUgdGhpcyBiZWhhdmlvdXIgeW91IHdpbGxcbiAqIG5lZWQgdG8gcHJvdmlkZSBhIHJlcXVlc3RUb2tlbkh0dHBNZXRob2Qgb3B0aW9uIHdoZW4gY3JlYXRpbmcgdGhlIGNsaWVudC5cbiAqXG4gKiovXG5leHBvcnRzLk9BdXRoLnByb3RvdHlwZS5nZXRPQXV0aFJlcXVlc3RUb2tlbj0gZnVuY3Rpb24oIGV4dHJhUGFyYW1zLCBjYWxsYmFjayApIHtcbiAgIGlmKCB0eXBlb2YgZXh0cmFQYXJhbXMgPT0gXCJmdW5jdGlvblwiICl7XG4gICAgIGNhbGxiYWNrID0gZXh0cmFQYXJhbXM7XG4gICAgIGV4dHJhUGFyYW1zID0ge307XG4gICB9XG4gIC8vIENhbGxiYWNrcyBhcmUgMS4wQSByZWxhdGVkXG4gIGlmKCB0aGlzLl9hdXRob3JpemVfY2FsbGJhY2sgKSB7XG4gICAgZXh0cmFQYXJhbXNbXCJvYXV0aF9jYWxsYmFja1wiXT0gdGhpcy5fYXV0aG9yaXplX2NhbGxiYWNrO1xuICB9XG4gIHRoaXMuX3BlcmZvcm1TZWN1cmVSZXF1ZXN0KCBudWxsLCBudWxsLCB0aGlzLl9jbGllbnRPcHRpb25zLnJlcXVlc3RUb2tlbkh0dHBNZXRob2QsIHRoaXMuX3JlcXVlc3RVcmwsIGV4dHJhUGFyYW1zLCBudWxsLCBudWxsLCBmdW5jdGlvbihlcnJvciwgZGF0YSwgcmVzcG9uc2UpIHtcbiAgICBpZiggZXJyb3IgKSBjYWxsYmFjayhlcnJvcik7XG4gICAgZWxzZSB7XG4gICAgICB2YXIgcmVzdWx0cz0gcXVlcnlzdHJpbmcucGFyc2UoZGF0YSk7XG5cbiAgICAgIHZhciBvYXV0aF90b2tlbj0gcmVzdWx0c1tcIm9hdXRoX3Rva2VuXCJdO1xuICAgICAgdmFyIG9hdXRoX3Rva2VuX3NlY3JldD0gcmVzdWx0c1tcIm9hdXRoX3Rva2VuX3NlY3JldFwiXTtcbiAgICAgIGRlbGV0ZSByZXN1bHRzW1wib2F1dGhfdG9rZW5cIl07XG4gICAgICBkZWxldGUgcmVzdWx0c1tcIm9hdXRoX3Rva2VuX3NlY3JldFwiXTtcbiAgICAgIGNhbGxiYWNrKG51bGwsIG9hdXRoX3Rva2VuLCBvYXV0aF90b2tlbl9zZWNyZXQsICByZXN1bHRzICk7XG4gICAgfVxuICB9KTtcbn1cblxuZXhwb3J0cy5PQXV0aC5wcm90b3R5cGUuc2lnblVybD0gZnVuY3Rpb24odXJsLCBvYXV0aF90b2tlbiwgb2F1dGhfdG9rZW5fc2VjcmV0LCBtZXRob2QpIHtcblxuICBpZiggbWV0aG9kID09PSB1bmRlZmluZWQgKSB7XG4gICAgdmFyIG1ldGhvZD0gXCJHRVRcIjtcbiAgfVxuXG4gIHZhciBvcmRlcmVkUGFyYW1ldGVycz0gdGhpcy5fcHJlcGFyZVBhcmFtZXRlcnMob2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgbWV0aG9kLCB1cmwsIHt9KTtcbiAgdmFyIHBhcnNlZFVybD0gVVJMLnBhcnNlKCB1cmwsIGZhbHNlICk7XG5cbiAgdmFyIHF1ZXJ5PVwiXCI7XG4gIGZvciggdmFyIGk9IDAgOyBpIDwgb3JkZXJlZFBhcmFtZXRlcnMubGVuZ3RoOyBpKyspIHtcbiAgICBxdWVyeSs9IG9yZGVyZWRQYXJhbWV0ZXJzW2ldWzBdK1wiPVwiKyB0aGlzLl9lbmNvZGVEYXRhKG9yZGVyZWRQYXJhbWV0ZXJzW2ldWzFdKSArIFwiJlwiO1xuICB9XG4gIHF1ZXJ5PSBxdWVyeS5zdWJzdHJpbmcoMCwgcXVlcnkubGVuZ3RoLTEpO1xuXG4gIHJldHVybiBwYXJzZWRVcmwucHJvdG9jb2wgKyBcIi8vXCIrIHBhcnNlZFVybC5ob3N0ICsgcGFyc2VkVXJsLnBhdGhuYW1lICsgXCI/XCIgKyBxdWVyeTtcbn07XG5cbmV4cG9ydHMuT0F1dGgucHJvdG90eXBlLmF1dGhIZWFkZXI9IGZ1bmN0aW9uKHVybCwgb2F1dGhfdG9rZW4sIG9hdXRoX3Rva2VuX3NlY3JldCwgbWV0aG9kKSB7XG4gIGlmKCBtZXRob2QgPT09IHVuZGVmaW5lZCApIHtcbiAgICB2YXIgbWV0aG9kPSBcIkdFVFwiO1xuICB9XG5cbiAgdmFyIG9yZGVyZWRQYXJhbWV0ZXJzPSB0aGlzLl9wcmVwYXJlUGFyYW1ldGVycyhvYXV0aF90b2tlbiwgb2F1dGhfdG9rZW5fc2VjcmV0LCBtZXRob2QsIHVybCwge30pO1xuICByZXR1cm4gdGhpcy5fYnVpbGRBdXRob3JpemF0aW9uSGVhZGVycyhvcmRlcmVkUGFyYW1ldGVycyk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth2.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/oauth2.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var querystring= __webpack_require__(/*! querystring */ \"querystring\"),\n    crypto= __webpack_require__(/*! crypto */ \"crypto\"),\n    https= __webpack_require__(/*! https */ \"https\"),\n    http= __webpack_require__(/*! http */ \"http\"),\n    URL= __webpack_require__(/*! url */ \"url\"),\n    OAuthUtils= __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\n\nexports.OAuth2= function(clientId, clientSecret, baseSite, authorizePath, accessTokenPath, customHeaders) {\n  this._clientId= clientId;\n  this._clientSecret= clientSecret;\n  this._baseSite= baseSite;\n  this._authorizeUrl= authorizePath || \"/oauth/authorize\";\n  this._accessTokenUrl= accessTokenPath || \"/oauth/access_token\";\n  this._accessTokenName= \"access_token\";\n  this._authMethod= \"Bearer\";\n  this._customHeaders = customHeaders || {};\n  this._useAuthorizationHeaderForGET= false;\n\n  //our agent\n  this._agent = undefined;\n};\n\n// Allows you to set an agent to use instead of the default HTTP or\n// HTTPS agents. Useful when dealing with your own certificates.\nexports.OAuth2.prototype.setAgent = function(agent) {\n  this._agent = agent;\n};\n\n// This 'hack' method is required for sites that don't use\n// 'access_token' as the name of the access token (for requests).\n// ( http://tools.ietf.org/html/draft-ietf-oauth-v2-16#section-7 )\n// it isn't clear what the correct value should be atm, so allowing\n// for specific (temporary?) override for now.\nexports.OAuth2.prototype.setAccessTokenName= function ( name ) {\n  this._accessTokenName= name;\n}\n\n// Sets the authorization method for Authorization header.\n// e.g. Authorization: Bearer <token>  # \"Bearer\" is the authorization method.\nexports.OAuth2.prototype.setAuthMethod = function ( authMethod ) {\n  this._authMethod = authMethod;\n};\n\n\n// If you use the OAuth2 exposed 'get' method (and don't construct your own _request call )\n// this will specify whether to use an 'Authorize' header instead of passing the access_token as a query parameter\nexports.OAuth2.prototype.useAuthorizationHeaderforGET = function(useIt) {\n  this._useAuthorizationHeaderForGET= useIt;\n}\n\nexports.OAuth2.prototype._getAccessTokenUrl= function() {\n  return this._baseSite + this._accessTokenUrl; /* + \"?\" + querystring.stringify(params); */\n}\n\n// Build the authorization header. In particular, build the part after the colon.\n// e.g. Authorization: Bearer <token>  # Build \"Bearer <token>\"\nexports.OAuth2.prototype.buildAuthHeader= function(token) {\n  return this._authMethod + ' ' + token;\n};\n\nexports.OAuth2.prototype._chooseHttpLibrary= function( parsedUrl ) {\n  var http_library= https;\n  // As this is OAUth2, we *assume* https unless told explicitly otherwise.\n  if( parsedUrl.protocol != \"https:\" ) {\n    http_library= http;\n  }\n  return http_library;\n};\n\nexports.OAuth2.prototype._request= function(method, url, headers, post_body, access_token, callback) {\n\n  var parsedUrl= URL.parse( url, true );\n  if( parsedUrl.protocol == \"https:\" && !parsedUrl.port ) {\n    parsedUrl.port= 443;\n  }\n\n  var http_library= this._chooseHttpLibrary( parsedUrl );\n\n\n  var realHeaders= {};\n  for( var key in this._customHeaders ) {\n    realHeaders[key]= this._customHeaders[key];\n  }\n  if( headers ) {\n    for(var key in headers) {\n      realHeaders[key] = headers[key];\n    }\n  }\n  realHeaders['Host']= parsedUrl.host;\n\n  if (!realHeaders['User-Agent']) {\n    realHeaders['User-Agent'] = 'Node-oauth';\n  }\n\n  if( post_body ) {\n      if ( Buffer.isBuffer(post_body) ) {\n          realHeaders[\"Content-Length\"]= post_body.length;\n      } else {\n          realHeaders[\"Content-Length\"]= Buffer.byteLength(post_body);\n      }\n  } else {\n      realHeaders[\"Content-length\"]= 0;\n  }\n\n  if( access_token && !('Authorization' in realHeaders)) {\n    if( ! parsedUrl.query ) parsedUrl.query= {};\n    parsedUrl.query[this._accessTokenName]= access_token;\n  }\n\n  var queryStr= querystring.stringify(parsedUrl.query);\n  if( queryStr ) queryStr=  \"?\" + queryStr;\n  var options = {\n    host:parsedUrl.hostname,\n    port: parsedUrl.port,\n    path: parsedUrl.pathname + queryStr,\n    method: method,\n    headers: realHeaders\n  };\n\n  this._executeRequest( http_library, options, post_body, callback );\n}\n\nexports.OAuth2.prototype._executeRequest= function( http_library, options, post_body, callback ) {\n  // Some hosts *cough* google appear to close the connection early / send no content-length header\n  // allow this behaviour.\n  var allowEarlyClose= OAuthUtils.isAnEarlyCloseHost(options.host);\n  var callbackCalled= false;\n  function passBackControl( response, result ) {\n    if(!callbackCalled) {\n      callbackCalled=true;\n      if( !(response.statusCode >= 200 && response.statusCode <= 299) && (response.statusCode != 301) && (response.statusCode != 302) ) {\n        callback({ statusCode: response.statusCode, data: result });\n      } else {\n        callback(null, result, response);\n      }\n    }\n  }\n\n  var result= \"\";\n\n  //set the agent on the request options\n  if (this._agent) {\n    options.agent = this._agent;\n  }\n\n  var request = http_library.request(options);\n  request.on('response', function (response) {\n    response.on(\"data\", function (chunk) {\n      result+= chunk\n    });\n    response.on(\"close\", function (err) {\n      if( allowEarlyClose ) {\n        passBackControl( response, result );\n      }\n    });\n    response.addListener(\"end\", function () {\n      passBackControl( response, result );\n    });\n  });\n  request.on('error', function(e) {\n    callbackCalled= true;\n    callback(e);\n  });\n\n  if( (options.method == 'POST' || options.method == 'PUT') && post_body ) {\n     request.write(post_body);\n  }\n  request.end();\n}\n\nexports.OAuth2.prototype.getAuthorizeUrl= function( params ) {\n  var params= params || {};\n  params['client_id'] = this._clientId;\n  return this._baseSite + this._authorizeUrl + \"?\" + querystring.stringify(params);\n}\n\nexports.OAuth2.prototype.getOAuthAccessToken= function(code, params, callback) {\n  var params= params || {};\n  params['client_id'] = this._clientId;\n  params['client_secret'] = this._clientSecret;\n  var codeParam = (params.grant_type === 'refresh_token') ? 'refresh_token' : 'code';\n  params[codeParam]= code;\n\n  var post_data= querystring.stringify( params );\n  var post_headers= {\n       'Content-Type': 'application/x-www-form-urlencoded'\n   };\n\n\n  this._request(\"POST\", this._getAccessTokenUrl(), post_headers, post_data, null, function(error, data, response) {\n    if( error )  callback(error);\n    else {\n      var results;\n      try {\n        // As of http://tools.ietf.org/html/draft-ietf-oauth-v2-07\n        // responses should be in JSON\n        results= JSON.parse( data );\n      }\n      catch(e) {\n        // .... However both Facebook + Github currently use rev05 of the spec\n        // and neither seem to specify a content-type correctly in their response headers :(\n        // clients of these services will suffer a *minor* performance cost of the exception\n        // being thrown\n        results= querystring.parse( data );\n      }\n      var access_token= results[\"access_token\"];\n      var refresh_token= results[\"refresh_token\"];\n      delete results[\"refresh_token\"];\n      callback(null, access_token, refresh_token, results); // callback results =-=\n    }\n  });\n}\n\n// Deprecated\nexports.OAuth2.prototype.getProtectedResource= function(url, access_token, callback) {\n  this._request(\"GET\", url, {}, \"\", access_token, callback );\n}\n\nexports.OAuth2.prototype.get= function(url, access_token, callback) {\n  if( this._useAuthorizationHeaderForGET ) {\n    var headers= {'Authorization': this.buildAuthHeader(access_token) }\n    access_token= null;\n  }\n  else {\n    headers= {};\n  }\n  this._request(\"GET\", url, headers, \"\", access_token, callback );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/sha1.js":
/*!****************************************!*\
  !*** ./node_modules/oauth/lib/sha1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined\n * in FIPS 180-1\n * Version 2.2 Copyright Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n */\n\n/*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */\nvar hexcase = 1;  /* hex output format. 0 - lowercase; 1 - uppercase        */\nvar b64pad  = \"=\"; /* base-64 pad character. \"=\" for strict RFC compliance   */\n\n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */\nfunction hex_sha1(s)    { return rstr2hex(rstr_sha1(str2rstr_utf8(s))); }\nfunction b64_sha1(s)    { return rstr2b64(rstr_sha1(str2rstr_utf8(s))); }\nfunction any_sha1(s, e) { return rstr2any(rstr_sha1(str2rstr_utf8(s)), e); }\nfunction hex_hmac_sha1(k, d)\n  { return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d))); }\nfunction b64_hmac_sha1(k, d)\n  { return rstr2b64(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d))); }\nfunction any_hmac_sha1(k, d, e)\n  { return rstr2any(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)), e); }\n\n/*\n * Perform a simple self-test to see if the VM is working\n */\nfunction sha1_vm_test()\n{\n  return hex_sha1(\"abc\").toLowerCase() == \"a9993e364706816aba3e25717850c26c9cd0d89d\";\n}\n\n/*\n * Calculate the SHA1 of a raw string\n */\nfunction rstr_sha1(s)\n{\n  return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));\n}\n\n/*\n * Calculate the HMAC-SHA1 of a key and some data (raw strings)\n */\nfunction rstr_hmac_sha1(key, data)\n{\n  var bkey = rstr2binb(key);\n  if(bkey.length > 16) bkey = binb_sha1(bkey, key.length * 8);\n\n  var ipad = Array(16), opad = Array(16);\n  for(var i = 0; i < 16; i++)\n  {\n    ipad[i] = bkey[i] ^ 0x36363636;\n    opad[i] = bkey[i] ^ 0x5C5C5C5C;\n  }\n\n  var hash = binb_sha1(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n  return binb2rstr(binb_sha1(opad.concat(hash), 512 + 160));\n}\n\n/*\n * Convert a raw string to a hex string\n */\nfunction rstr2hex(input)\n{\n  try { hexcase } catch(e) { hexcase=0; }\n  var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n  var output = \"\";\n  var x;\n  for(var i = 0; i < input.length; i++)\n  {\n    x = input.charCodeAt(i);\n    output += hex_tab.charAt((x >>> 4) & 0x0F)\n           +  hex_tab.charAt( x        & 0x0F);\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to a base-64 string\n */\nfunction rstr2b64(input)\n{\n  try { b64pad } catch(e) { b64pad=''; }\n  var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n  var output = \"\";\n  var len = input.length;\n  for(var i = 0; i < len; i += 3)\n  {\n    var triplet = (input.charCodeAt(i) << 16)\n                | (i + 1 < len ? input.charCodeAt(i+1) << 8 : 0)\n                | (i + 2 < len ? input.charCodeAt(i+2)      : 0);\n    for(var j = 0; j < 4; j++)\n    {\n      if(i * 8 + j * 6 > input.length * 8) output += b64pad;\n      else output += tab.charAt((triplet >>> 6*(3-j)) & 0x3F);\n    }\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to an arbitrary string encoding\n */\nfunction rstr2any(input, encoding)\n{\n  var divisor = encoding.length;\n  var remainders = Array();\n  var i, q, x, quotient;\n\n  /* Convert to an array of 16-bit big-endian values, forming the dividend */\n  var dividend = Array(Math.ceil(input.length / 2));\n  for(i = 0; i < dividend.length; i++)\n  {\n    dividend[i] = (input.charCodeAt(i * 2) << 8) | input.charCodeAt(i * 2 + 1);\n  }\n\n  /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */\n  while(dividend.length > 0)\n  {\n    quotient = Array();\n    x = 0;\n    for(i = 0; i < dividend.length; i++)\n    {\n      x = (x << 16) + dividend[i];\n      q = Math.floor(x / divisor);\n      x -= q * divisor;\n      if(quotient.length > 0 || q > 0)\n        quotient[quotient.length] = q;\n    }\n    remainders[remainders.length] = x;\n    dividend = quotient;\n  }\n\n  /* Convert the remainders to the output string */\n  var output = \"\";\n  for(i = remainders.length - 1; i >= 0; i--)\n    output += encoding.charAt(remainders[i]);\n\n  /* Append leading zero equivalents */\n  var full_length = Math.ceil(input.length * 8 /\n                                    (Math.log(encoding.length) / Math.log(2)))\n  for(i = output.length; i < full_length; i++)\n    output = encoding[0] + output;\n\n  return output;\n}\n\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */\nfunction str2rstr_utf8(input)\n{\n  var output = \"\";\n  var i = -1;\n  var x, y;\n\n  while(++i < input.length)\n  {\n    /* Decode utf-16 surrogate pairs */\n    x = input.charCodeAt(i);\n    y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n    if(0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF)\n    {\n      x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n      i++;\n    }\n\n    /* Encode output as utf-8 */\n    if(x <= 0x7F)\n      output += String.fromCharCode(x);\n    else if(x <= 0x7FF)\n      output += String.fromCharCode(0xC0 | ((x >>> 6 ) & 0x1F),\n                                    0x80 | ( x         & 0x3F));\n    else if(x <= 0xFFFF)\n      output += String.fromCharCode(0xE0 | ((x >>> 12) & 0x0F),\n                                    0x80 | ((x >>> 6 ) & 0x3F),\n                                    0x80 | ( x         & 0x3F));\n    else if(x <= 0x1FFFFF)\n      output += String.fromCharCode(0xF0 | ((x >>> 18) & 0x07),\n                                    0x80 | ((x >>> 12) & 0x3F),\n                                    0x80 | ((x >>> 6 ) & 0x3F),\n                                    0x80 | ( x         & 0x3F));\n  }\n  return output;\n}\n\n/*\n * Encode a string as utf-16\n */\nfunction str2rstr_utf16le(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length; i++)\n    output += String.fromCharCode( input.charCodeAt(i)        & 0xFF,\n                                  (input.charCodeAt(i) >>> 8) & 0xFF);\n  return output;\n}\n\nfunction str2rstr_utf16be(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length; i++)\n    output += String.fromCharCode((input.charCodeAt(i) >>> 8) & 0xFF,\n                                   input.charCodeAt(i)        & 0xFF);\n  return output;\n}\n\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */\nfunction rstr2binb(input)\n{\n  var output = Array(input.length >> 2);\n  for(var i = 0; i < output.length; i++)\n    output[i] = 0;\n  for(var i = 0; i < input.length * 8; i += 8)\n    output[i>>5] |= (input.charCodeAt(i / 8) & 0xFF) << (24 - i % 32);\n  return output;\n}\n\n/*\n * Convert an array of big-endian words to a string\n */\nfunction binb2rstr(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length * 32; i += 8)\n    output += String.fromCharCode((input[i>>5] >>> (24 - i % 32)) & 0xFF);\n  return output;\n}\n\n/*\n * Calculate the SHA-1 of an array of big-endian words, and a bit length\n */\nfunction binb_sha1(x, len)\n{\n  /* append padding */\n  x[len >> 5] |= 0x80 << (24 - len % 32);\n  x[((len + 64 >> 9) << 4) + 15] = len;\n\n  var w = Array(80);\n  var a =  1732584193;\n  var b = -271733879;\n  var c = -1732584194;\n  var d =  271733878;\n  var e = -1009589776;\n\n  for(var i = 0; i < x.length; i += 16)\n  {\n    var olda = a;\n    var oldb = b;\n    var oldc = c;\n    var oldd = d;\n    var olde = e;\n\n    for(var j = 0; j < 80; j++)\n    {\n      if(j < 16) w[j] = x[i + j];\n      else w[j] = bit_rol(w[j-3] ^ w[j-8] ^ w[j-14] ^ w[j-16], 1);\n      var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)),\n                       safe_add(safe_add(e, w[j]), sha1_kt(j)));\n      e = d;\n      d = c;\n      c = bit_rol(b, 30);\n      b = a;\n      a = t;\n    }\n\n    a = safe_add(a, olda);\n    b = safe_add(b, oldb);\n    c = safe_add(c, oldc);\n    d = safe_add(d, oldd);\n    e = safe_add(e, olde);\n  }\n  return Array(a, b, c, d, e);\n\n}\n\n/*\n * Perform the appropriate triplet combination function for the current\n * iteration\n */\nfunction sha1_ft(t, b, c, d)\n{\n  if(t < 20) return (b & c) | ((~b) & d);\n  if(t < 40) return b ^ c ^ d;\n  if(t < 60) return (b & c) | (b & d) | (c & d);\n  return b ^ c ^ d;\n}\n\n/*\n * Determine the appropriate additive constant for the current iteration\n */\nfunction sha1_kt(t)\n{\n  return (t < 20) ?  1518500249 : (t < 40) ?  1859775393 :\n         (t < 60) ? -1894007588 : -899497514;\n}\n\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\nfunction safe_add(x, y)\n{\n  var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return (msw << 16) | (lsw & 0xFFFF);\n}\n\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\nfunction bit_rol(num, cnt)\n{\n  return (num << cnt) | (num >>> (32 - cnt));\n}\n\nexports.HMACSHA1= function(key, data) {\n  return b64_hmac_sha1(key, data);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/sha1.js\n");

/***/ })

};
;