'use client';

import { useState, useRef } from 'react';
import FileTypeIcon from './FileTypeIcon';
import { useAlert } from '@/components/AlertProvider';
import { validateFile, defaultValidationOptions } from '@/lib/storage/fileStorage';

interface DocumentFormFileUploadProps {
  setFile: (file: File | null) => void;
  setFileName: (name: string) => void;
  setFileType: (type: string) => void;
  setFileKey?: (key: string) => void;
  setFileSize?: (size: number) => void;
  maxSizeInMB?: number;
  allowedFileTypes?: string[];
}

export default function DocumentFormFileUpload({
  setFile,
  setFileName,
  setFileType,
  setFileKey,
  setFileSize,
  maxSizeInMB = 100, // Default 100MB for large documents
  allowedFileTypes = defaultValidationOptions.allowedFileTypes,
}: DocumentFormFileUploadProps) {
  const { showAlert } = useAlert();
  const [selectedFileName, setSelectedFileName] = useState<string>('');
  const [fileError, setFileError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFileError(null);

    if (e.target.files && e.target.files[0]) {
      processSelectedFile(e.target.files[0]);
    }
  };

  // Process a file whether it comes from file input or scanner
  const processSelectedFile = (selectedFile: File) => {
    // Validate file size and type
    const validation = validateFile(selectedFile, {
      maxSizeInBytes: maxSizeInMB * 1024 * 1024,
      allowedFileTypes
    });

    if (!validation.valid) {
      setFileError(validation.error || 'Invalid file');
      showAlert({
        message: validation.error || 'Invalid file',
        type: 'error'
      });

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    // Set file information
    setFile(selectedFile);
    setFileName(selectedFile.name);
    setSelectedFileName(selectedFile.name);
    const fileExt = selectedFile.name.split('.').pop() || '';
    setFileType(fileExt);

    // Set file size if the callback is provided
    if (setFileSize) {
      setFileSize(selectedFile.size);
    }

    showAlert({
      message: 'File selected successfully',
      type: 'success'
    });
  };

  const handleButtonClick = () => {
    // Trigger the hidden file input
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
    setFileName('');
    setFileType('');
    setSelectedFileName('');
    setFileError(null);
    if (setFileKey) setFileKey('');
    if (setFileSize) setFileSize(0);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };



  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  return (
    <div>
      <label htmlFor="file" className="label flex items-center">
        Document File (PDF, DOCX, Images) - Max {maxSizeInMB}MB
        <span className="ml-2 text-xs font-medium text-red-600 dark:text-red-400">*Required</span>
      </label>

      <div className="mt-1 flex flex-col">
        {/* Progress bar for large file uploads */}
        {isLoading && uploadProgress > 0 && (
          <div className="mb-3">
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
              <span>Uploading...</span>
              <span>{uploadProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        <div className="flex items-center">
          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            id="file"
            name="file"
            type="file"
            className="hidden"
            onChange={handleFileChange}
            accept={allowedFileTypes.map(type => `.${type}`).join(',')}
            disabled={isLoading}
          />

          {/* File upload button */}
          <button
            type="button"
            onClick={handleButtonClick}
            disabled={isLoading}
            className="inline-flex items-center px-5 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 disabled:opacity-50"
          >
            {isLoading ? (
              <span className="loading loading-spinner loading-xs mr-2"></span>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary-500 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            )}
            {isLoading ? 'Uploading...' : 'Upload Document'}
          </button>

          {/* Display selected file name */}
          {selectedFileName && (
            <div className="ml-3 flex items-center text-sm text-gray-500 dark:text-gray-400">
              <FileTypeIcon fileType={selectedFileName.split('.').pop()} />
              <span className="truncate max-w-xs">{selectedFileName}</span>
              <button
                type="button"
                className="ml-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                onClick={handleRemoveFile}
                disabled={isLoading}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}

          {/* File error message */}
          {fileError && (
            <div className="mt-2 text-sm text-red-600 dark:text-red-400">
              {fileError}
            </div>
          )}

          {/* File size and type information */}
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            {!fileError && selectedFileName ? (
              <>Allowed file types: {allowedFileTypes.join(', ')} | Max size: {maxSizeInMB}MB</>
            ) : (
              <span className="text-amber-600 dark:text-amber-400">
                You must attach a document file before sending
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
