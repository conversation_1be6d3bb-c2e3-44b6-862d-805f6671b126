{"version": 3, "file": "devtools.module.js", "sources": ["../src/index.js", "../src/devtools.js"], "sourcesContent": ["import { options } from 'preact';\nimport { initDevTools } from './devtools';\n\ninitDevTools();\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, name: string) => T}\n */\nexport function addHookName(value, name) {\n\tif (options._addHookName) {\n\t\toptions._addHookName(name);\n\t}\n\treturn value;\n}\n", "import { options, Fragment, Component } from 'preact';\n\nexport function initDevTools() {\n\tif (typeof window != 'undefined' && window.__PREACT_DEVTOOLS__) {\n\t\twindow.__PREACT_DEVTOOLS__.attachPreact('10.11.3', options, {\n\t\t\tFragment,\n\t\t\tComponent\n\t\t});\n\t}\n}\n"], "names": ["addHookName", "value", "name", "options", "__a", "window", "__PREACT_DEVTOOLS__", "attachPreact", "Fragment", "Component"], "mappings": "8DASO,SAASA,EAAYC,EAAOC,GAIlC,OAHIC,EAAsBC,KACzBD,EAAOC,IAAcF,GAEfD,CACP,CCXqB,oBAAVI,QAAyBA,OAAOC,qBAC1CD,OAAOC,oBAAoBC,aAAa,UAAWJ,EAAS,CAC3DK,SAAAA,EACAC,UAAAA"}