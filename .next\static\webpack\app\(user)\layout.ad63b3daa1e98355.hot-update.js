"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(user)/layout",{

/***/ "(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx":
/*!**************************************************!*\
  !*** ./src/components/chatbot/ChatInterface.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AlertProvider */ \"(app-pages-browser)/./src/components/AlertProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatInterface(param) {\n    var _this = this;\n    var isOpen = param.isOpen, onClose = param.onClose, documentContext = param.documentContext;\n    _s();\n    var _useSession = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)(), session = _useSession.data;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var showAlert = (0,_components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__.useAlert)().showAlert;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), messages = _useState[0], setMessages = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), inputMessage = _useState1[0], setInputMessage = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isLoading = _useState2[0], setIsLoading = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), sessionId = _useState3[0], setSessionId = _useState3[1];\n    var _useState4 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isTyping = _useState4[0], setIsTyping = _useState4[1];\n    var messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        messages\n    ]);\n    // Focus input when chat opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isOpen && inputRef.current) {\n            inputRef.current.focus();\n        }\n    }, [\n        isOpen\n    ]);\n    // Start chat session when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isOpen && !sessionId && (session === null || session === void 0 ? void 0 : session.user)) {\n            // Always start a fresh session instead of trying to restore\n            // This ensures compatibility with browser restarts and auth session changes\n            console.log(\"Starting fresh chat session for user:\", session.user.name);\n            // Clear any old session data to prevent conflicts\n            localStorage.removeItem(\"chatbot_session_id\");\n            localStorage.removeItem(\"chatbot_session_time\");\n            startChatSession();\n        }\n    }, [\n        isOpen,\n        session\n    ]);\n    // Update document context when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (sessionId && documentContext) {\n            updateDocumentContext();\n        }\n    }, [\n        sessionId,\n        documentContext\n    ]);\n    var startChatSession = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var response, errorData, data, _session_user, welcomeMessage, error, fallbackMessage;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            5,\n                            6,\n                            7\n                        ]);\n                        setIsLoading(true);\n                        console.log(\"Starting chat session...\");\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"start_session\",\n                                    context: {\n                                        currentPage: pathname,\n                                        systemState: {\n                                            timestamp: new Date().toISOString()\n                                        }\n                                    }\n                                })\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!!response.ok) return [\n                            3,\n                            3\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        errorData = _state.sent();\n                        console.error(\"Failed to start chat session:\", errorData);\n                        throw new Error(errorData.message || \"Failed to start chat session\");\n                    case 3:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 4:\n                        data = _state.sent();\n                        console.log(\"Chat session started:\", data);\n                        setSessionId(data.data.sessionId);\n                        // If no messages returned, add a welcome message\n                        if (!data.data.messages || data.data.messages.length === 0) {\n                            ;\n                            welcomeMessage = {\n                                id: \"welcome_\".concat(Date.now()),\n                                role: \"assistant\",\n                                content: \"Hello \".concat((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || \"there\", \"! \\uD83D\\uDC4B\\n\\nI'm MGB Bot, your AI assistant for the Document Tracker system. I can help you with:\\n\\n• **Document workflows** - How to send, receive, and process documents\\n• **System navigation** - Finding features and pages\\n• **Status explanations** - Understanding document statuses\\n• **Troubleshooting** - Solving common issues\\n\\nWhat would you like to know?\"),\n                                timestamp: new Date(),\n                                quickReplies: [\n                                    \"How to send documents?\",\n                                    \"Check my inbox\",\n                                    \"Document workflow\",\n                                    \"System help\"\n                                ]\n                            };\n                            setMessages([\n                                welcomeMessage\n                            ]);\n                        } else {\n                            setMessages(data.data.messages);\n                        }\n                        // Save session to localStorage for persistence\n                        localStorage.setItem(\"chatbot_session_id\", data.data.sessionId);\n                        localStorage.setItem(\"chatbot_session_time\", new Date().toISOString());\n                        return [\n                            3,\n                            7\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        console.error(\"Error starting chat session:\", error);\n                        // Show fallback welcome message even if session creation fails\n                        fallbackMessage = {\n                            id: \"fallback_\".concat(Date.now()),\n                            role: \"assistant\",\n                            content: \"Hello! \\uD83D\\uDC4B\\n\\nI'm MGB Bot, your AI assistant. I'm having trouble connecting to the server right now, but I can still help you with basic information about the Document Tracker system.\\n\\nTry typing a message and I'll do my best to assist you!\",\n                            timestamp: new Date(),\n                            quickReplies: [\n                                \"How to send documents?\",\n                                \"Check my inbox\",\n                                \"Document workflow\"\n                            ]\n                        };\n                        setMessages([\n                            fallbackMessage\n                        ]);\n                        showAlert({\n                            message: \"Chat session started in offline mode\",\n                            type: \"warning\"\n                        });\n                        return [\n                            3,\n                            7\n                        ];\n                    case 6:\n                        setIsLoading(false);\n                        return [\n                            7\n                        ];\n                    case 7:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function startChatSession() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var updateDocumentContext = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!sessionId || !documentContext) return [\n                            2\n                        ];\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"update_document_context\",\n                                    sessionId: sessionId,\n                                    documentData: documentContext\n                                })\n                            })\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error updating document context:\", error);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function updateDocumentContext() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var sendMessage = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function(message) {\n            var userMessage, response, errorData, data, error, _error_message;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!message.trim() || isLoading) return [\n                            2\n                        ];\n                        if (!!sessionId) return [\n                            3,\n                            2\n                        ];\n                        showAlert({\n                            message: \"Starting chat session...\",\n                            type: \"info\"\n                        });\n                        return [\n                            4,\n                            startChatSession()\n                        ];\n                    case 1:\n                        _state.sent();\n                        // Wait a bit for session to be created, then try again\n                        setTimeout(function() {\n                            if (sessionId) {\n                                sendMessage(message);\n                            }\n                        }, 1000);\n                        return [\n                            2\n                        ];\n                    case 2:\n                        userMessage = {\n                            id: \"temp_\".concat(Date.now()),\n                            role: \"user\",\n                            content: message,\n                            timestamp: new Date()\n                        };\n                        setMessages(function(prev) {\n                            return (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_8__._)(prev).concat([\n                                userMessage\n                            ]);\n                        });\n                        setInputMessage(\"\");\n                        setIsLoading(true);\n                        setIsTyping(true);\n                        _state.label = 3;\n                    case 3:\n                        _state.trys.push([\n                            3,\n                            8,\n                            9,\n                            10\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/chatbot\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    action: \"send_message\",\n                                    sessionId: sessionId,\n                                    message: message,\n                                    context: {\n                                        currentPage: pathname\n                                    }\n                                })\n                            })\n                        ];\n                    case 4:\n                        response = _state.sent();\n                        if (!!response.ok) return [\n                            3,\n                            6\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 5:\n                        errorData = _state.sent();\n                        // Handle session expiration\n                        if (response.status === 410 && errorData.error === \"SESSION_EXPIRED\") {\n                            showAlert({\n                                message: \"Chat session expired. Starting a new conversation...\",\n                                type: \"warning\"\n                            });\n                            // Clear current session and start a new one\n                            setSessionId(null);\n                            setMessages([]);\n                            // Clear localStorage\n                            localStorage.removeItem(\"chatbot_session_id\");\n                            localStorage.removeItem(\"chatbot_session_time\");\n                            // Restart the session\n                            setTimeout(function() {\n                                if (session === null || session === void 0 ? void 0 : session.user) {\n                                    startChatSession();\n                                }\n                            }, 1000);\n                            return [\n                                2\n                            ];\n                        }\n                        throw new Error(errorData.message || \"Failed to send message\");\n                    case 6:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 7:\n                        data = _state.sent();\n                        setMessages(data.data.messages);\n                        return [\n                            3,\n                            10\n                        ];\n                    case 8:\n                        error = _state.sent();\n                        console.error(\"Error sending message:\", error);\n                        // Don't show error for session expiration as we handle it above\n                        if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"session expired\"))) {\n                            showAlert({\n                                message: error.message || \"Failed to send message\",\n                                type: \"error\"\n                            });\n                        }\n                        return [\n                            3,\n                            10\n                        ];\n                    case 9:\n                        setIsLoading(false);\n                        setIsTyping(false);\n                        return [\n                            7\n                        ];\n                    case 10:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function sendMessage(message) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var handleQuickReply = function(reply) {\n        sendMessage(reply);\n    };\n    var handleAction = function(action) {\n        switch(action.type){\n            case \"navigate\":\n                var _action_data;\n                if ((_action_data = action.data) === null || _action_data === void 0 ? void 0 : _action_data.url) {\n                    window.location.href = action.data.url;\n                }\n                break;\n            case \"search\":\n                // Implement search functionality\n                console.log(\"Search action:\", action.data);\n                break;\n            case \"help\":\n                // Show help modal or navigate to help\n                console.log(\"Help action:\", action.data);\n                break;\n            default:\n                console.log(\"Unknown action:\", action);\n        }\n    };\n    var formatMessage = function(content) {\n        // Handle undefined, null, or empty content\n        if (!content || typeof content !== \"string\") {\n            return \"<div>No content available</div>\";\n        }\n        // Split content by lines and format each line separately to avoid nesting issues\n        var lines = content.split(\"\\n\");\n        var formattedLines = lines.map(function(line) {\n            return line.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\").replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n        });\n        // Join with div elements instead of br tags to avoid nesting issues\n        return formattedLines.map(function(line) {\n            return line.trim() ? \"<div>\".concat(line, \"</div>\") : \"<div>&nbsp;</div>\";\n        }).join(\"\");\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-end justify-end p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black bg-opacity-25\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-96 h-[600px] flex flex-col border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-lg\",\n                                                    children: \"\\uD83E\\uDD16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-white\",\n                                                children: \"MGB Bot\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-100\",\n                                                children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"animate-pulse\",\n                                                            children: \"Typing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\",\n                                                                    style: {\n                                                                        animationDelay: \"0.1s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1 h-1 bg-blue-200 rounded-full animate-bounce\",\n                                                                    style: {\n                                                                        animationDelay: \"0.2s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this) : \"AI Assistant • Online\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-white hover:text-blue-200 transition-all duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-20 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50\",\n                                title: \"Close MGB Bot\",\n                                \"aria-label\": \"Close chatbot\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2.5,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                        children: [\n                            messages.map(function(message) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                    children: [\n                                        message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"\\uD83E\\uDD16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm \".concat(message.role === \"user\" ? \"bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md\" : \"bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 rounded-bl-md\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"prose prose-sm max-w-none\",\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: formatMessage(message.content)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                message.actions && message.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 space-y-2\",\n                                                    children: message.actions.map(function(action, index) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: function() {\n                                                                return handleAction(action);\n                                                            },\n                                                            className: \"flex items-center justify-center w-full px-3 py-2 text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1\",\n                                                                    children: \"\\uD83D\\uDD17\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                action.label\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                message.quickReplies && message.quickReplies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2\",\n                                                    children: message.quickReplies.map(function(reply, index) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: function() {\n                                                                return handleQuickReply(reply);\n                                                            },\n                                                            className: \"px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-sm\",\n                                                            children: [\n                                                                \"\\uD83D\\uDCAC \",\n                                                                reply\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, _this);\n                            }),\n                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm\",\n                                                children: \"\\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                    children: \"MGB Bot is thinking\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 ml-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.1s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.2s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: inputRef,\n                                                type: \"text\",\n                                                value: inputMessage,\n                                                onChange: function(e) {\n                                                    return setInputMessage(e.target.value);\n                                                },\n                                                onKeyDown: function(e) {\n                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                        e.preventDefault();\n                                                        sendMessage(inputMessage);\n                                                    }\n                                                },\n                                                placeholder: \"Type your message...\",\n                                                disabled: isLoading,\n                                                className: \"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return sendMessage(inputMessage);\n                                        },\n                                        disabled: isLoading || !inputMessage.trim(),\n                                        className: \"px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-sm\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 flex flex-wrap gap-2\",\n                                children: [\n                                    \"How to send documents?\",\n                                    \"Check my inbox\",\n                                    \"Document workflow\"\n                                ].map(function(suggestion, index) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return sendMessage(suggestion);\n                                        },\n                                        disabled: isLoading,\n                                        className: \"px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-full transition-colors duration-200 disabled:opacity-50\",\n                                        children: suggestion\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\DocumentTracker\\\\src\\\\components\\\\chatbot\\\\ChatInterface.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"V6NTTLWUzvNZRus1A3jSe2LATHk=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _components_AlertProvider__WEBPACK_IMPORTED_MODULE_4__.useAlert\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chatbot/ChatInterface.tsx\n"));

/***/ })

});