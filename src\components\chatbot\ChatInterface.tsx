'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import { useAlert } from '@/components/AlertProvider';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  actions?: Array<{
    type: 'navigate' | 'search' | 'create' | 'help' | 'info';
    label: string;
    data?: any;
  }>;
  quickReplies?: string[];
}

interface ChatInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  documentContext?: {
    currentDocument?: any;
    documentJourney?: any[];
    routingSlip?: any[];
    relatedDocuments?: any[];
  };
}

export default function ChatInterface({ isOpen, onClose, documentContext }: ChatInterfaceProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const { showAlert } = useAlert();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Start chat session when component mounts
  useEffect(() => {
    if (isOpen && !sessionId && session?.user) {
      // Try to restore session from localStorage first
      const savedSessionId = localStorage.getItem('chatbot_session_id');
      const savedSessionTime = localStorage.getItem('chatbot_session_time');

      // Check if saved session is still valid (less than 4 hours old)
      if (savedSessionId && savedSessionTime) {
        const sessionTime = new Date(savedSessionTime);
        const fourHoursAgo = new Date(Date.now() - 4 * 60 * 60 * 1000);

        if (sessionTime > fourHoursAgo) {
          console.log('Restoring chat session from localStorage:', savedSessionId);
          setSessionId(savedSessionId);
          return;
        } else {
          // Clear expired session data
          localStorage.removeItem('chatbot_session_id');
          localStorage.removeItem('chatbot_session_time');
        }
      }

      startChatSession();
    }
  }, [isOpen, session]);

  // Update document context when it changes
  useEffect(() => {
    if (sessionId && documentContext) {
      updateDocumentContext();
    }
  }, [sessionId, documentContext]);

  const startChatSession = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'start_session',
          context: {
            currentPage: pathname,
            systemState: {
              timestamp: new Date().toISOString()
            }
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start chat session');
      }

      const data = await response.json();
      setSessionId(data.data.sessionId);
      setMessages(data.data.messages);

      // Save session to localStorage for persistence
      localStorage.setItem('chatbot_session_id', data.data.sessionId);
      localStorage.setItem('chatbot_session_time', new Date().toISOString());
    } catch (error) {
      console.error('Error starting chat session:', error);
      showAlert({
        message: 'Failed to start chat session',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateDocumentContext = async () => {
    if (!sessionId || !documentContext) return;

    try {
      await fetch('/api/chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_document_context',
          sessionId,
          documentData: documentContext
        }),
      });
    } catch (error) {
      console.error('Error updating document context:', error);
    }
  };

  const sendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    // Check if we have a valid session, if not, create one
    if (!sessionId) {
      showAlert({
        message: 'Starting chat session...',
        type: 'info',
      });
      await startChatSession();

      // Wait a bit for session to be created, then try again
      setTimeout(() => {
        if (sessionId) {
          sendMessage(message);
        }
      }, 1000);
      return;
    }

    const userMessage: ChatMessage = {
      id: `temp_${Date.now()}`,
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setIsTyping(true);

    try {
      const response = await fetch('/api/chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send_message',
          sessionId,
          message,
          context: {
            currentPage: pathname
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle session expiration
        if (response.status === 410 && errorData.error === 'SESSION_EXPIRED') {
          showAlert({
            message: 'Chat session expired. Starting a new conversation...',
            type: 'warning',
          });

          // Clear current session and start a new one
          setSessionId(null);
          setMessages([]);

          // Clear localStorage
          localStorage.removeItem('chatbot_session_id');
          localStorage.removeItem('chatbot_session_time');

          // Restart the session
          setTimeout(() => {
            if (session?.user) {
              startChatSession();
            }
          }, 1000);

          return;
        }

        throw new Error(errorData.message || 'Failed to send message');
      }

      const data = await response.json();
      setMessages(data.data.messages);
    } catch (error: any) {
      console.error('Error sending message:', error);

      // Don't show error for session expiration as we handle it above
      if (!error.message?.includes('session expired')) {
        showAlert({
          message: error.message || 'Failed to send message',
          type: 'error',
        });
      }
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleQuickReply = (reply: string) => {
    sendMessage(reply);
  };

  const handleAction = (action: any) => {
    switch (action.type) {
      case 'navigate':
        if (action.data?.url) {
          window.location.href = action.data.url;
        }
        break;
      case 'search':
        // Implement search functionality
        console.log('Search action:', action.data);
        break;
      case 'help':
        // Show help modal or navigate to help
        console.log('Help action:', action.data);
        break;
      default:
        console.log('Unknown action:', action);
    }
  };

  const formatMessage = (content: string | undefined | null) => {
    // Handle undefined, null, or empty content
    if (!content || typeof content !== 'string') {
      return '<div>No content available</div>';
    }

    // Split content by lines and format each line separately to avoid nesting issues
    const lines = content.split('\n');
    const formattedLines = lines.map(line =>
      line
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
    );

    // Join with div elements instead of br tags to avoid nesting issues
    return formattedLines
      .map(line => line.trim() ? `<div>${line}</div>` : '<div>&nbsp;</div>')
      .join('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-end p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-25"
        onClick={onClose}
      />

      {/* Chat Window */}
      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-96 h-[600px] flex flex-col border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <span className="text-white text-lg">🤖</span>
              </div>
              {/* Online indicator */}
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
            </div>
            <div>
              <h3 className="font-bold text-white">MGB Bot</h3>
              <p className="text-xs text-blue-100">
                {isTyping ? (
                  <span className="flex items-center">
                    <span className="animate-pulse">Typing</span>
                    <span className="ml-1 flex space-x-1">
                      <div className="w-1 h-1 bg-blue-200 rounded-full animate-bounce"></div>
                      <div className="w-1 h-1 bg-blue-200 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-1 h-1 bg-blue-200 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </span>
                  </span>
                ) : (
                  'AI Assistant • Online'
                )}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-all duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-20 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            title="Close MGB Bot"
            aria-label="Close chatbot"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {/* Assistant Avatar */}
              {message.role === 'assistant' && (
                <div className="flex-shrink-0 mr-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">🤖</span>
                  </div>
                </div>
              )}

              <div
                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md'
                    : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 rounded-bl-md'
                }`}
              >
                <div
                  className="prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: formatMessage(message.content)
                  }}
                />

                {/* Action Buttons */}
                {message.actions && message.actions.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {message.actions.map((action, index) => (
                      <button
                        key={index}
                        onClick={() => handleAction(action)}
                        className="flex items-center justify-center w-full px-3 py-2 text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm"
                      >
                        <span className="mr-1">🔗</span>
                        {action.label}
                      </button>
                    ))}
                  </div>
                )}

                {/* Quick Replies */}
                {message.quickReplies && message.quickReplies.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-2">
                    {message.quickReplies.map((reply, index) => (
                      <button
                        key={index}
                        onClick={() => handleQuickReply(reply)}
                        className="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-sm"
                      >
                        💬 {reply}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}

          {isTyping && (
            <div className="flex justify-start">
              <div className="flex-shrink-0 mr-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">🤖</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm">
                <div className="flex items-center space-x-1">
                  <span className="text-gray-500 dark:text-gray-400 text-sm">MGB Bot is thinking</span>
                  <div className="flex space-x-1 ml-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800">
          <div className="flex space-x-3">
            <div className="flex-1 relative">
              <input
                ref={inputRef}
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage(inputMessage);
                  }
                }}
                placeholder="Type your message..."
                disabled={isLoading}
                className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                </svg>
              </div>
            </div>
            <button
              onClick={() => sendMessage(inputMessage)}
              disabled={isLoading || !inputMessage.trim()}
              className="px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-sm"
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )}
            </button>
          </div>

          {/* Quick suggestions */}
          <div className="mt-3 flex flex-wrap gap-2">
            {['How to send documents?', 'Check my inbox', 'Document workflow'].map((suggestion, index) => (
              <button
                key={index}
                onClick={() => sendMessage(suggestion)}
                disabled={isLoading}
                className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-full transition-colors duration-200 disabled:opacity-50"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
