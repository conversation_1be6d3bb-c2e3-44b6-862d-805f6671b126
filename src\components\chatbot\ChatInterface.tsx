'use client';

// Fixed version - no localStorage session restoration
import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import { useAlert } from '@/components/AlertProvider';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  actions?: Array<{
    type: 'navigate' | 'search' | 'create' | 'help' | 'info';
    label: string;
    data?: any;
  }>;
  quickReplies?: string[];
}

interface ChatInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  documentContext?: {
    currentDocument?: any;
    documentJourney?: any[];
    routingSlip?: any[];
    relatedDocuments?: any[];
  };
}

export default function ChatInterface({ isOpen, onClose, documentContext }: ChatInterfaceProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const { showAlert } = useAlert();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Clean up any old chat session data on component mount
  useEffect(() => {
    // Clear any existing chatbot localStorage data to prevent conflicts
    localStorage.removeItem('chatbot_session_id');
    localStorage.removeItem('chatbot_session_time');
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Start chat session when component mounts
  useEffect(() => {
    if (isOpen && !sessionId && session?.user) {
      // Always start a fresh session instead of trying to restore
      // This ensures compatibility with browser restarts and auth session changes
      console.log('Starting fresh chat session for user:', session.user.name);

      // Clear any old session data to prevent conflicts
      localStorage.removeItem('chatbot_session_id');
      localStorage.removeItem('chatbot_session_time');

      startChatSession();
    }
  }, [isOpen, session]);

  // Update document context when it changes
  useEffect(() => {
    if (sessionId && documentContext) {
      updateDocumentContext();
    }
  }, [sessionId, documentContext]);

  const startChatSession = async () => {
    try {
      setIsLoading(true);
      console.log('Starting chat session...');

      const response = await fetch('/api/chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'start_session',
          context: {
            currentPage: pathname,
            systemState: {
              timestamp: new Date().toISOString()
            }
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to start chat session:', errorData);
        throw new Error(errorData.message || 'Failed to start chat session');
      }

      const data = await response.json();
      console.log('Chat session started:', data);

      setSessionId(data.data.sessionId);

      // If no messages returned, add a welcome message
      if (!data.data.messages || data.data.messages.length === 0) {
        const welcomeMessage = {
          id: `welcome_${Date.now()}`,
          role: 'assistant' as const,
          content: `Hello ${session?.user?.name || 'there'}! 👋\n\nI'm MGB Bot, your AI assistant for the Document Tracker system. I can help you with:\n\n• **Document workflows** - How to send, receive, and process documents\n• **System navigation** - Finding features and pages\n• **Status explanations** - Understanding document statuses\n• **Troubleshooting** - Solving common issues\n\nWhat would you like to know?`,
          timestamp: new Date(),
          quickReplies: ['How to send documents?', 'Check my inbox', 'Document workflow', 'System help']
        };
        setMessages([welcomeMessage]);
      } else {
        setMessages(data.data.messages);
      }

      // Note: We don't save session to localStorage anymore to avoid conflicts
      // with browser restarts and auth session changes
    } catch (error: any) {
      console.error('Error starting chat session:', error);

      // Show fallback welcome message even if session creation fails
      const fallbackMessage = {
        id: `fallback_${Date.now()}`,
        role: 'assistant' as const,
        content: `Hello! 👋\n\nI'm MGB Bot, your AI assistant. I'm having trouble connecting to the server right now, but I can still help you with basic information about the Document Tracker system.\n\nTry typing a message and I'll do my best to assist you!`,
        timestamp: new Date(),
        quickReplies: ['How to send documents?', 'Check my inbox', 'Document workflow']
      };
      setMessages([fallbackMessage]);

      showAlert({
        message: 'Chat session started in offline mode',
        type: 'warning',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateDocumentContext = async () => {
    if (!sessionId || !documentContext) return;

    try {
      await fetch('/api/chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_document_context',
          sessionId,
          documentData: documentContext
        }),
      });
    } catch (error) {
      console.error('Error updating document context:', error);
    }
  };

  const sendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    // Check if we have a valid session, if not, create one
    if (!sessionId) {
      showAlert({
        message: 'Starting chat session...',
        type: 'info',
      });
      await startChatSession();

      // Wait a bit for session to be created, then try again
      setTimeout(() => {
        if (sessionId) {
          sendMessage(message);
        }
      }, 1000);
      return;
    }

    const userMessage: ChatMessage = {
      id: `temp_${Date.now()}`,
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setIsTyping(true);

    try {
      const response = await fetch('/api/chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send_message',
          sessionId,
          message,
          context: {
            currentPage: pathname
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle session expiration
        if (response.status === 410 && errorData.error === 'SESSION_EXPIRED') {
          showAlert({
            message: 'Chat session expired. Starting a new conversation...',
            type: 'warning',
          });

          // Clear current session and start a new one
          setSessionId(null);
          setMessages([]);

          // Restart the session
          setTimeout(() => {
            if (session?.user) {
              startChatSession();
            }
          }, 1000);

          return;
        }

        throw new Error(errorData.message || 'Failed to send message');
      }

      const data = await response.json();
      setMessages(data.data.messages);
    } catch (error: any) {
      console.error('Error sending message:', error);

      // Don't show error for session expiration as we handle it above
      if (!error.message?.includes('session expired')) {
        showAlert({
          message: error.message || 'Failed to send message',
          type: 'error',
        });
      }
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleQuickReply = (reply: string) => {
    sendMessage(reply);
  };

  const handleAction = (action: any) => {
    switch (action.type) {
      case 'navigate':
        if (action.data?.url) {
          window.location.href = action.data.url;
        }
        break;
      case 'search':
        // Implement search functionality
        sendMessage(`Help me search for: ${action.data?.query || 'documents'}`);
        break;
      case 'help':
        // Handle different help topics
        if (action.data?.topic === 'getting_started') {
          sendMessage('Show me a quick tour of the system');
        } else if (action.data?.topic === 'features') {
          sendMessage('What are the main features of the document tracking system?');
        } else if (action.data?.topic === 'filipino_recipes') {
          sendMessage('Give me some Filipino recipes, especially adobo');
        } else if (action.data?.topic === 'cooking_tips') {
          sendMessage('Share some cooking tips and techniques');
        } else if (action.data?.topic === 'healthy_eating') {
          sendMessage('Tell me about healthy eating habits');
        } else if (action.data?.topic === 'ai_details') {
          sendMessage('Tell me more about artificial intelligence');
        } else if (action.data?.topic === 'science_topics') {
          sendMessage('What are some interesting science topics?');
        } else if (action.data?.topic === 'tech_trends') {
          sendMessage('What are the latest technology trends?');
        } else if (action.data?.topic === 'programming_languages') {
          sendMessage('What are the best programming languages to learn?');
        } else if (action.data?.topic === 'web_development') {
          sendMessage('How do I start with web development?');
        } else if (action.data?.topic === 'mobile_development') {
          sendMessage('Tell me about mobile app development');
        } else if (action.data?.topic === 'philippines_travel') {
          sendMessage('What are the best places to visit in the Philippines?');
        } else if (action.data?.topic === 'world_geography') {
          sendMessage('Tell me some interesting world geography facts');
        } else if (action.data?.topic === 'travel_tips') {
          sendMessage('Give me some travel planning tips');
        } else if (action.data?.topic === 'math_help') {
          sendMessage('I need help with mathematics');
        } else if (action.data?.topic === 'statistics') {
          sendMessage('Explain statistics concepts');
        } else if (action.data?.topic === 'problem_solving') {
          sendMessage('Help me with problem solving techniques');
        } else if (action.data?.topic === 'fitness_tips') {
          sendMessage('Give me some fitness and workout tips');
        } else if (action.data?.topic === 'nutrition_guide') {
          sendMessage('Tell me about nutrition and healthy eating');
        } else if (action.data?.topic === 'mental_health') {
          sendMessage('Share some mental health and wellness tips');
        } else if (action.data?.topic === 'general_help') {
          sendMessage('I want to explore what you can help me with');
        } else {
          sendMessage('I need help with the system');
        }
        break;
      case 'info':
        // Handle info requests
        if (action.data?.topic === 'features') {
          sendMessage('Tell me about the system features');
        } else {
          sendMessage('Give me more information about the system');
        }
        break;
      case 'create':
        // Handle document creation
        if (action.data?.url) {
          window.location.href = action.data.url;
        } else {
          sendMessage('How do I create a new document?');
        }
        break;
      default:
        console.log('Unknown action:', action);
        sendMessage('I need help with this feature');
    }
  };

  // Format message content with basic markdown support
  const formatMessageContent = (content: string | undefined | null) => {
    if (!content || typeof content !== 'string') {
      return 'No content available';
    }

    // Simple text formatting without HTML to avoid nesting issues
    return content
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markers for now
      .replace(/\*(.*?)\*/g, '$1')     // Remove italic markers for now
      .replace(/•/g, '•');             // Keep bullet points
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-end p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-25"
        onClick={onClose}
      />

      {/* Chat Window */}
      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-96 h-[600px] flex flex-col border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <span className="text-white text-lg">🤖</span>
              </div>
              {/* Online indicator */}
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
            </div>
            <div>
              <h3 className="font-bold text-white">MGB Bot</h3>
              <div className="text-xs text-blue-100">
                {isTyping ? (
                  <div className="flex items-center">
                    <span className="animate-pulse">Typing</span>
                    <div className="ml-1 flex space-x-1">
                      <div className="w-1 h-1 bg-blue-200 rounded-full animate-bounce"></div>
                      <div className="w-1 h-1 bg-blue-200 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-1 h-1 bg-blue-200 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                ) : (
                  'AI Assistant • Online'
                )}
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-all duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-20 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            title="Close MGB Bot"
            aria-label="Close chatbot"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {/* Assistant Avatar */}
              {message.role === 'assistant' && (
                <div className="flex-shrink-0 mr-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">🤖</span>
                  </div>
                </div>
              )}

              <div
                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md'
                    : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 rounded-bl-md'
                }`}
              >
                <div className="text-sm leading-relaxed whitespace-pre-wrap">
                  {formatMessageContent(message.content)}
                </div>

                {/* Action Buttons */}
                {message.actions && message.actions.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {message.actions.map((action, index) => (
                      <button
                        key={index}
                        onClick={() => handleAction(action)}
                        className="flex items-center justify-center w-full px-3 py-2 text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm"
                      >
                        <span className="mr-1">🔗</span>
                        {action.label}
                      </button>
                    ))}
                  </div>
                )}

                {/* Quick Replies */}
                {message.quickReplies && message.quickReplies.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-2">
                    {message.quickReplies.map((reply, index) => (
                      <button
                        key={index}
                        onClick={() => handleQuickReply(reply)}
                        className="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-sm"
                      >
                        💬 {reply}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}

          {isTyping && (
            <div className="flex justify-start">
              <div className="flex-shrink-0 mr-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">🤖</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm">
                <div className="flex items-center space-x-1">
                  <span className="text-gray-500 dark:text-gray-400 text-sm">MGB Bot is thinking</span>
                  <div className="flex space-x-1 ml-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800">
          <div className="flex space-x-3">
            <div className="flex-1 relative">
              <input
                ref={inputRef}
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage(inputMessage);
                  }
                }}
                placeholder="Type your message..."
                disabled={isLoading}
                className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                </svg>
              </div>
            </div>
            <button
              onClick={() => sendMessage(inputMessage)}
              disabled={isLoading || !inputMessage.trim()}
              className="px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-sm"
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )}
            </button>
          </div>

          {/* Quick suggestions */}
          <div className="mt-3 flex flex-wrap gap-2">
            {['How to send documents?', 'Check my inbox', 'Document workflow'].map((suggestion, index) => (
              <button
                key={index}
                onClick={() => sendMessage(suggestion)}
                disabled={isLoading}
                className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-full transition-colors duration-200 disabled:opacity-50"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
