/**
 * Validation Service
 * Centralized validation logic for the application
 */

import { DocumentStatus, DocumentAction, UserRole, Division } from '@/types';

export interface ValidationResult {
  valid: boolean;
  error?: string;
}

export interface DocumentValidationData {
  title?: string;
  description?: string;
  category?: string;
  action?: DocumentAction;
  targetDivision?: string;
  recipientId?: string;
  fileUrl?: string;
  fileName?: string;
  fileType?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface FileValidationOptions {
  maxSizeInBytes?: number;
  allowedFileTypes?: string[];
}

export class ValidationService {
  /**
   * Validate document creation data
   */
  static validateDocumentCreation(data: DocumentValidationData): ValidationResult {
    const { title, description, category, targetDivision, recipientId, fileUrl, fileName, fileType } = data;

    // Check required fields
    if (!title || title.trim() === '') {
      return { valid: false, error: 'Document title is required' };
    }

    if (!description || description.trim() === '') {
      return { valid: false, error: 'Document description is required' };
    }

    if (!category || category.trim() === '') {
      return { valid: false, error: 'Document category is required' };
    }

    if (!targetDivision || targetDivision.trim() === '') {
      return { valid: false, error: 'Target division is required' };
    }

    if (!recipientId || recipientId.trim() === '') {
      return { valid: false, error: 'Recipient is required' };
    }

    // Validate file attachment
    if (!fileUrl || !fileName || !fileType) {
      return { valid: false, error: 'Please attach a document file before sending' };
    }

    // Validate title length
    if (title.length > 200) {
      return { valid: false, error: 'Document title must be less than 200 characters' };
    }

    // Validate description length
    if (description.length > 1000) {
      return { valid: false, error: 'Document description must be less than 1000 characters' };
    }

    // Validate division
    if (!Object.values(Division).includes(targetDivision as Division)) {
      return { valid: false, error: 'Invalid target division' };
    }

    // Validate ObjectId format for recipientId
    if (!this.isValidObjectId(recipientId)) {
      return { valid: false, error: 'Invalid recipient ID format' };
    }

    return { valid: true };
  }

  /**
   * Validate pagination parameters
   */
  static validatePagination(params: PaginationParams): ValidationResult {
    const { page = 1, limit = 20 } = params;

    if (isNaN(page) || page < 1) {
      return { valid: false, error: 'Page must be a positive number' };
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      return { valid: false, error: 'Limit must be between 1 and 100' };
    }

    return { valid: true };
  }

  /**
   * Validate file upload
   */
  static validateFile(file: File, options: FileValidationOptions = {}): ValidationResult {
    const { maxSizeInBytes = 104857600, allowedFileTypes = [] } = options; // Default 100MB

    // Check file size
    if (file.size > maxSizeInBytes) {
      const maxSizeMB = Math.round(maxSizeInBytes / (1024 * 1024));
      return { valid: false, error: `File size must be less than ${maxSizeMB}MB` };
    }

    // Check file type if specified
    if (allowedFileTypes.length > 0) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!fileExtension || !allowedFileTypes.includes(fileExtension)) {
        return {
          valid: false,
          error: `File type not allowed. Allowed types: ${allowedFileTypes.join(', ')}`
        };
      }
    }

    // Check if file name is valid
    if (!file.name || file.name.trim() === '') {
      return { valid: false, error: 'File must have a valid name' };
    }

    // Check for potentially dangerous file names
    const dangerousPatterns = [
      /\.\./,  // Directory traversal
      /[<>:"|?*]/,  // Invalid filename characters
      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i  // Reserved Windows names
    ];

    if (dangerousPatterns.some(pattern => pattern.test(file.name))) {
      return { valid: false, error: 'File name contains invalid characters' };
    }

    return { valid: true };
  }

  /**
   * Validate user permissions for document operations
   */
  static validateDocumentPermissions(
    operation: 'view' | 'edit' | 'delete' | 'receive' | 'process' | 'forward' | 'archive',
    document: any,
    userId: string,
    userRole: UserRole,
    userDivision: Division
  ): ValidationResult {
    switch (operation) {
      case 'view':
        return this.validateViewPermission(document, userId, userRole, userDivision);

      case 'receive':
        return this.validateReceivePermission(document, userId);

      case 'process':
        return this.validateProcessPermission(document, userId);

      case 'forward':
        return this.validateForwardPermission(document, userId);

      case 'archive':
        return this.validateArchivePermission(document, userId, userRole);

      case 'edit':
      case 'delete':
        return this.validateEditDeletePermission(document, userId, userRole);

      default:
        return { valid: false, error: 'Invalid operation' };
    }
  }

  /**
   * Validate view permission
   */
  private static validateViewPermission(
    document: any,
    userId: string,
    userRole: UserRole,
    userDivision: Division
  ): ValidationResult {
    // Regional Director can view all documents
    if (userRole === UserRole.REGIONAL_DIRECTOR) {
      return { valid: true };
    }

    // Division Chiefs can view documents in their division
    if (userRole === UserRole.DIVISION_CHIEF && document.currentLocation === userDivision) {
      return { valid: true };
    }

    // Users can view documents they created or are recipients of
    if (document.createdBy === userId || document.recipientId === userId) {
      return { valid: true };
    }

    return { valid: false, error: 'You do not have permission to view this document' };
  }

  /**
   * Validate receive permission
   */
  private static validateReceivePermission(document: any, userId: string): ValidationResult {
    if (document.recipientId !== userId) {
      return { valid: false, error: 'You are not the recipient of this document' };
    }

    if (document.status !== DocumentStatus.PENDING) {
      return { valid: false, error: 'Document is not in PENDING status' };
    }

    return { valid: true };
  }

  /**
   * Validate process permission
   */
  private static validateProcessPermission(document: any, userId: string): ValidationResult {
    if (document.recipientId !== userId) {
      return { valid: false, error: 'You are not the recipient of this document' };
    }

    if (document.status !== DocumentStatus.RECEIVED) {
      return { valid: false, error: 'Document must be received before processing' };
    }

    return { valid: true };
  }

  /**
   * Validate forward permission
   */
  private static validateForwardPermission(document: any, userId: string): ValidationResult {
    if (document.recipientId !== userId && document.createdBy !== userId) {
      return { valid: false, error: 'You can only forward documents you created or received' };
    }

    const validStatuses = [DocumentStatus.RECEIVED, DocumentStatus.PROCESSED];
    if (!validStatuses.includes(document.status)) {
      return { valid: false, error: 'Document must be received or processed before forwarding' };
    }

    return { valid: true };
  }

  /**
   * Validate archive permission
   */
  private static validateArchivePermission(
    document: any,
    userId: string,
    userRole: UserRole
  ): ValidationResult {
    // Regional Director can archive any document
    if (userRole === UserRole.REGIONAL_DIRECTOR) {
      return { valid: true };
    }

    // Users can archive documents they received and processed
    if (document.recipientId === userId && document.status === DocumentStatus.PROCESSED) {
      return { valid: true };
    }

    return { valid: false, error: 'You can only archive documents you have processed' };
  }

  /**
   * Validate edit/delete permission
   */
  private static validateEditDeletePermission(
    document: any,
    userId: string,
    userRole: UserRole
  ): ValidationResult {
    // Regional Director can edit/delete any document
    if (userRole === UserRole.REGIONAL_DIRECTOR) {
      return { valid: true };
    }

    // Users can only edit/delete documents they created and are still in SENT status
    if (document.createdBy === userId && document.status === DocumentStatus.SENT) {
      return { valid: true };
    }

    return { valid: false, error: 'You can only edit/delete documents you created that are still in SENT status' };
  }

  /**
   * Validate search query
   */
  static validateSearchQuery(query: string): ValidationResult {
    if (!query || typeof query !== 'string') {
      return { valid: false, error: 'Search query must be a string' };
    }

    if (query.length > 100) {
      return { valid: false, error: 'Search query must be less than 100 characters' };
    }

    // Check for potentially dangerous patterns
    const dangerousPatterns = [
      /\$where/i,  // MongoDB injection
      /javascript:/i,  // XSS
      /<script/i,  // XSS
      /eval\(/i,  // Code injection
    ];

    if (dangerousPatterns.some(pattern => pattern.test(query))) {
      return { valid: false, error: 'Search query contains invalid characters' };
    }

    return { valid: true };
  }

  /**
   * Validate ObjectId format
   */
  static isValidObjectId(id: string): boolean {
    return /^[0-9a-fA-F]{24}$/.test(id);
  }

  /**
   * Validate DTN format
   */
  static isValidDTN(dtn: string): boolean {
    return /^MGBR2-\d{4}-\d{4}-\d{4}$/.test(dtn);
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate user role
   */
  static isValidUserRole(role: string): boolean {
    return Object.values(UserRole).includes(role as UserRole);
  }

  /**
   * Validate division
   */
  static isValidDivision(division: string): boolean {
    return Object.values(Division).includes(division as Division);
  }

  /**
   * Sanitize string input
   */
  static sanitizeString(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, 1000); // Limit length
  }

  /**
   * Validate and sanitize remarks/notes
   */
  static validateRemarks(remarks: string): ValidationResult {
    if (!remarks || typeof remarks !== 'string') {
      return { valid: false, error: 'Remarks must be a string' };
    }

    if (remarks.length > 500) {
      return { valid: false, error: 'Remarks must be less than 500 characters' };
    }

    return { valid: true };
  }
}
