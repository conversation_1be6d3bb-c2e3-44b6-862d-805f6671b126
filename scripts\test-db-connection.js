/**
 * Simple Database Connection Test
 * This script tests if we can connect to MongoDB
 */

require('dotenv').config({ path: '.env.local' });
const mongoose = require('mongoose');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/document-tracker';

console.log('🧪 Testing MongoDB Connection...');
console.log('📍 MongoDB URI:', MONGODB_URI.replace(/\/\/.*@/, '//***:***@'));

async function testConnection() {
  try {
    console.log('🔌 Attempting to connect...');
    
    await mongoose.connect(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000, // 5 second timeout
      socketTimeoutMS: 5000,
    });
    
    console.log('✅ Successfully connected to MongoDB!');
    
    // Test basic operations
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    
    console.log('📊 Database Info:');
    console.log(`   Database Name: ${db.databaseName}`);
    console.log(`   Collections: ${collections.length}`);
    
    if (collections.length > 0) {
      console.log('   Existing Collections:');
      collections.forEach(col => {
        console.log(`     - ${col.name}`);
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('');
      console.log('💡 Troubleshooting Tips:');
      console.log('   1. Make sure MongoDB is running');
      console.log('   2. Check if MongoDB Compass can connect');
      console.log('   3. Verify the connection string in .env.local');
      console.log('   4. Try starting MongoDB service:');
      console.log('      - Windows: net start MongoDB');
      console.log('      - Or start MongoDB manually');
    }
    
    return false;
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Connection closed');
  }
}

if (require.main === module) {
  testConnection().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = testConnection;
