/**
 * Database Migration Script for Document Tracker System
 * This script will:
 * 1. Connect to MongoDB
 * 2. Initialize all required collections
 * 3. Set up indexes for performance
 * 4. Create initial data (counters, admin user)
 * 5. Verify the setup
 */

require('dotenv').config({ path: '.env.local' });
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// MongoDB connection string from environment
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/document-tracker';

console.log('🚀 Starting Database Migration for Document Tracker System');
console.log('📍 MongoDB URI:', MONGODB_URI.replace(/\/\/.*@/, '//***:***@')); // Hide credentials in log

// Define all schemas
const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a name'],
    maxlength: [60, 'Name cannot be more than 60 characters'],
    unique: true,
    trim: true,
  },
  email: {
    type: String,
    required: false,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    select: false,
  },
  role: {
    type: String,
    enum: ['ADMIN', 'REGIONAL_DIRECTOR', 'DIVISION_CHIEF', 'EMPLOYEE'],
    default: 'EMPLOYEE',
  },
  division: {
    type: String,
    enum: ['ORD', 'MSD', 'MSESDD', 'MGSD', 'MRED', 'MRMD', 'LEGAL', 'RECORDS'],
    required: [true, 'Please provide a division'],
  },
  image: {
    type: String,
  },
  activeSessions: {
    type: Array,
    default: [],
  },
}, {
  timestamps: true,
});

const CounterSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    enum: ['document_tracking_number'],
    default: 'document_tracking_number',
  },
  year: {
    type: Number,
    required: true,
  },
  sequence: {
    type: Number,
    required: true,
    default: 0,
  },
}, { timestamps: true });

const DocumentJourneySchema = new mongoose.Schema({
  action: {
    type: String,
    required: [true, 'Please provide an action'],
  },
  fromDivision: {
    type: String,
    enum: ['ORD', 'MSD', 'MSESDD', 'MGSD', 'MRED', 'MRMD', 'LEGAL', 'RECORDS'],
  },
  toDivision: {
    type: String,
    enum: ['ORD', 'MSD', 'MSESDD', 'MGSD', 'MRED', 'MRMD', 'LEGAL', 'RECORDS'],
  },
  byUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Please provide a user'],
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
  notes: {
    type: String,
  },
});

const DocumentSchema = new mongoose.Schema({
  trackingNumber: {
    type: String,
    required: [true, 'Please provide a tracking number'],
    unique: true,
    index: true,
  },
  title: {
    type: String,
    required: [true, 'Please provide a title'],
    maxlength: [200, 'Title cannot be more than 200 characters'],
    index: true,
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters'],
  },
  category: {
    type: String,
    enum: ['MEMO', 'LETTER', 'REPORT', 'REQUEST', 'NOTICE', 'OTHER'],
    required: [true, 'Please provide a category'],
    index: true,
  },
  status: {
    type: String,
    enum: ['PENDING', 'RECEIVED', 'PROCESSED', 'FORWARDED', 'COMPLETED'],
    default: 'PENDING',
    index: true,
  },
  priority: {
    type: String,
    enum: ['LOW', 'MEDIUM', 'HIGH', 'URGENT'],
    default: 'MEDIUM',
    index: true,
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Please provide a sender'],
    index: true,
  },
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Please provide a recipient'],
    index: true,
  },
  currentHolder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true,
  },
  fromDivision: {
    type: String,
    enum: ['ORD', 'MSD', 'MSESDD', 'MGSD', 'MRED', 'MRMD', 'LEGAL', 'RECORDS'],
    required: [true, 'Please provide a from division'],
    index: true,
  },
  toDivision: {
    type: String,
    enum: ['ORD', 'MSD', 'MSESDD', 'MGSD', 'MRED', 'MRMD', 'LEGAL', 'RECORDS'],
    required: [true, 'Please provide a to division'],
    index: true,
  },
  fileUrl: {
    type: String,
  },
  fileName: {
    type: String,
  },
  fileType: {
    type: String,
  },
  fileSize: {
    type: Number,
  },
  dueDate: {
    type: Date,
    index: true,
  },
  isOriginal: {
    type: Boolean,
    default: false,
    index: true,
  },
  journey: [DocumentJourneySchema],
}, {
  timestamps: true,
});

const NotificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  title: {
    type: String,
    required: true,
  },
  message: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: ['DOCUMENT_RECEIVED', 'DOCUMENT_PROCESSED', 'DOCUMENT_FORWARDED', 'SYSTEM', 'REMINDER'],
    default: 'SYSTEM',
    index: true,
  },
  isRead: {
    type: Boolean,
    default: false,
    index: true,
  },
  documentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document',
    index: true,
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
  },
}, {
  timestamps: true,
});

// Migration functions
async function connectToDatabase() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      bufferCommands: false,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4,
      autoIndex: true,
      autoCreate: true,
    });
    console.log('✅ Connected to MongoDB successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error.message);
    return false;
  }
}

async function createCollections() {
  try {
    console.log('📦 Creating collections and indexes...');

    // Register models
    const User = mongoose.model('User', UserSchema);
    const Counter = mongoose.model('Counter', CounterSchema);
    const Document = mongoose.model('Document', DocumentSchema);
    const Notification = mongoose.model('Notification', NotificationSchema);

    // Create indexes
    await User.createIndexes();
    await Counter.createIndexes();
    await Document.createIndexes();
    await Notification.createIndexes();

    console.log('✅ Collections and indexes created successfully');
    return { User, Counter, Document, Notification };
  } catch (error) {
    console.error('❌ Failed to create collections:', error.message);
    throw error;
  }
}

async function initializeCounters(Counter) {
  try {
    console.log('🔢 Initializing document counters...');

    const currentYear = new Date().getFullYear();

    // Check if counter already exists
    const existingCounter = await Counter.findOne({
      name: 'document_tracking_number',
      year: currentYear
    });

    if (!existingCounter) {
      await Counter.create({
        name: 'document_tracking_number',
        year: currentYear,
        sequence: 0
      });
      console.log(`✅ Created counter for year ${currentYear}`);
    } else {
      console.log(`✅ Counter for year ${currentYear} already exists (sequence: ${existingCounter.sequence})`);
    }
  } catch (error) {
    console.error('❌ Failed to initialize counters:', error.message);
    throw error;
  }
}

async function createAdminUser(User) {
  try {
    console.log('👤 Creating admin user...');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ role: 'ADMIN' });

    if (!existingAdmin) {
      const hashedPassword = await bcrypt.hash('Admin123!', 12);

      const adminUser = await User.create({
        name: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        division: 'ORD'
      });

      console.log('✅ Admin user created successfully');
      console.log('📧 Username: admin');
      console.log('🔑 Password: Admin123!');
      console.log('🏢 Division: ORD');

      return adminUser;
    } else {
      console.log('✅ Admin user already exists:', existingAdmin.name);
      return existingAdmin;
    }
  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message);
    throw error;
  }
}

async function createSampleUsers(User) {
  try {
    console.log('👥 Creating sample users...');

    const sampleUsers = [
      { name: 'john_doe', email: '<EMAIL>', role: 'DIVISION_CHIEF', division: 'MSD' },
      { name: 'jane_smith', email: '<EMAIL>', role: 'EMPLOYEE', division: 'MSESDD' },
      { name: 'mike_wilson', email: '<EMAIL>', role: 'EMPLOYEE', division: 'MGSD' },
      { name: 'sarah_johnson', email: '<EMAIL>', role: 'DIVISION_CHIEF', division: 'MRED' },
    ];

    for (const userData of sampleUsers) {
      const existingUser = await User.findOne({ name: userData.name });

      if (!existingUser) {
        const hashedPassword = await bcrypt.hash('Password123!', 12);
        await User.create({
          ...userData,
          password: hashedPassword
        });
        console.log(`✅ Created user: ${userData.name} (${userData.role} - ${userData.division})`);
      } else {
        console.log(`✅ User already exists: ${userData.name}`);
      }
    }
  } catch (error) {
    console.error('❌ Failed to create sample users:', error.message);
    throw error;
  }
}

async function verifySetup(models) {
  try {
    console.log('🔍 Verifying database setup...');

    const { User, Counter, Document, Notification } = models;

    // Count documents in each collection
    const userCount = await User.countDocuments();
    const counterCount = await Counter.countDocuments();
    const documentCount = await Document.countDocuments();
    const notificationCount = await Notification.countDocuments();

    console.log('📊 Database Statistics:');
    console.log(`   👤 Users: ${userCount}`);
    console.log(`   🔢 Counters: ${counterCount}`);
    console.log(`   📄 Documents: ${documentCount}`);
    console.log(`   🔔 Notifications: ${notificationCount}`);

    // Verify admin user exists
    const adminUser = await User.findOne({ role: 'ADMIN' });
    if (adminUser) {
      console.log(`✅ Admin user verified: ${adminUser.name}`);
    } else {
      console.log('❌ No admin user found!');
    }

    // Verify counter exists
    const currentYear = new Date().getFullYear();
    const counter = await Counter.findOne({ year: currentYear });
    if (counter) {
      console.log(`✅ Counter verified for year ${currentYear}: sequence ${counter.sequence}`);
    } else {
      console.log(`❌ No counter found for year ${currentYear}!`);
    }

    return true;
  } catch (error) {
    console.error('❌ Failed to verify setup:', error.message);
    return false;
  }
}

// Main migration function
async function runMigration() {
  try {
    console.log('🎯 Starting database migration...');
    console.log('⏰ Timestamp:', new Date().toISOString());
    console.log('');

    // Step 1: Connect to database
    const connected = await connectToDatabase();
    if (!connected) {
      process.exit(1);
    }

    // Step 2: Create collections and indexes
    const models = await createCollections();

    // Step 3: Initialize counters
    await initializeCounters(models.Counter);

    // Step 4: Create admin user
    await createAdminUser(models.User);

    // Step 5: Create sample users (optional)
    await createSampleUsers(models.User);

    // Step 6: Verify setup
    const verified = await verifySetup(models);

    if (verified) {
      console.log('');
      console.log('🎉 Database migration completed successfully!');
      console.log('');
      console.log('📋 Next Steps:');
      console.log('   1. Start the application: npm run dev');
      console.log('   2. Open http://localhost:3000 in your browser');
      console.log('   3. Login with username: admin, password: Admin123!');
      console.log('');
    } else {
      console.log('❌ Migration completed with errors. Please check the logs above.');
    }

  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the migration
if (require.main === module) {
  runMigration();
}

module.exports = {
  runMigration,
  connectToDatabase,
  createCollections,
  initializeCounters,
  createAdminUser,
  verifySetup
};
